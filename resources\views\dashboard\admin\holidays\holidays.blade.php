@extends('dashboard.layout.master')
@push('css')
    <!--begin::Vendor Stylesheets(used for this page only)-->
    <link href="{{ asset('website') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet"
        type="text/css" />
    <link href="{{ asset('website') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet"
        type="text/css" />
    <!--end::Vendor Stylesheets-->
    <style>
        .error {
            color: #dc3545 !important;
            font-weight: bold;
            font-size: 12px;
            margin-top: 5px;
        }

        /* FullCalendar specific styles to override master layout conflicts */
        #booking-calendar,
        #holidays-calendar {
            font-family: 'Sora', sans-serif;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        #booking-calendar .fc,
        #holidays-calendar .fc {
            font-family: 'Sora', sans-serif;
        }

        /* Holidays calendar specific styles */
        #holidays-calendar .fc-event {
            border-radius: 4px !important;
            border: none !important;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: 500;
            margin: 1px;
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
            color: #ffffff !important;
        }

        #holidays-calendar .fc-event-title {
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        #holidays-calendar .fc-daygrid-event {
            border-radius: 4px !important;
            margin: 1px;
        }

        #booking-calendar .fc-timegrid-slot {
            height: 60px !important;
            border: 1px solid #e5e7eb !important;
        }

        #booking-calendar .fc-timegrid-col {
            min-width: 60px !important;
        }

        #booking-calendar .fc-timegrid-axis {
            width: 80px !important;
            background: #f9fafb;
            border-right: 2px solid #e5e7eb !important;
        }

        #booking-calendar .fc-timegrid-axis-cushion {
            padding: 8px 12px;
            font-size: 12px;
            color: #6b7280;
            font-weight: 500;
        }

        #booking-calendar .fc-col-header-cell {
            background: #f9fafb;
            border: 1px solid #e5e7eb !important;
            padding: 12px 8px;
            font-weight: 600;
            color: #374151;
        }

        #booking-calendar .fc-daygrid-day,
        #booking-calendar .fc-timegrid-col {
            border: 1px solid #e5e7eb !important;
        }

        #booking-calendar .fc-event {
            border-radius: 6px !important;
            border: none !important;
            padding: 2px 6px;
            font-size: 12px;
            font-weight: 500;
            margin: 1px;
        }

        #booking-calendar .fc-event-title {
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        #booking-calendar .fc-timegrid-event {
            border-radius: 4px !important;
            margin: 1px 2px;
        }

        #booking-calendar .fc-now-indicator-line {
            border-color: #ef4444;
            border-width: 2px;
        }

        #booking-calendar .fc-now-indicator-arrow {
            border-left-color: #ef4444;
            border-width: 6px;
        }

        /* Ensure proper table layout */
        #booking-calendar table {
            width: 100% !important;
            table-layout: fixed !important;
        }

        #booking-calendar .fc-scrollgrid {
            border: 1px solid #e5e7eb !important;
            border-radius: 8px;
            overflow: hidden;
        }

        #booking-calendar .fc-scrollgrid-section>* {
            border-left: 0 !important;
            border-right: 0 !important;
        }

        #booking-calendar .fc-scrollgrid-section-header>td {
            border-bottom: 2px solid #e5e7eb !important;
        }

        /* Time slot styling */
        #booking-calendar .fc-timegrid-slot-minor {
            border-top: 1px dashed #e5e7eb !important;
        }

        #booking-calendar .fc-timegrid-slot-major {
            border-top: 1px solid #d1d5db !important;
        }

        /* Today column highlighting */
        #booking-calendar .fc-day-today {
            background-color: #fef3c7 !important;
        }

        /* Holiday date picker specific styles */
        #holidayDatePicker {
            cursor: pointer;
        }

        #holidayDatePicker:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Ensure daterangepicker appears above other elements */
        .daterangepicker {
            z-index: 9999 !important;
        }


        /* Responsive adjustments */
        @media (max-width: 768px) {
            #booking-calendar .fc-timegrid-axis {
                width: 60px !important;
            }

            #booking-calendar .fc-event {
                font-size: 11px;
            }
        }

        /* Custom FullCalendar styles for customer booking */
        .customer-calender #calendar {
            overflow: visible;
        }

        .customer-calender .fc {
            font-family: 'Sora', sans-serif;
        }

        .customer-calender .fc-timegrid-slot {
            height: 40px;
        }

        .customer-calender .fc-timegrid-axis {
            width: 60px;
        }

        .customer-calender .fc-col-header-cell {
            background-color: var(--whisper-gray);
            border: 1px solid var(--border-color);
            padding: 10px;
            font-weight: 600;
            color: var(--black);
        }

        .customer-calender .fc-timegrid-slot {
            border-color: var(--border-color);
        }

        .customer-calender .fc-event {
            border-radius: 6px;
            border: none;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
        }

        .customer-calender .fc-event-title {
            font-weight: 600;
        }

        .customer-calender .fc-event:hover {
            opacity: 0.8;
            cursor: pointer;
        }

        .customer-calender .fc-timegrid-now-indicator-line {
            border-color: var(--deep-blue);
            border-width: 2px;
        }

        .customer-calender .fc-timegrid-now-indicator-arrow {
            border-left-color: var(--deep-blue);
            border-right-color: var(--deep-blue);
        }

        /* Hide default scrollbars and adjust layout */
        .customer-calender .fc-scroller {
            overflow-x: auto;
            overflow-y: auto;
        }

        .customer-calender .fc-timegrid-body {
            min-width: 100%;
        }

        /* Custom SweetAlert styles for booking details */
        .booking-details-popup {
            font-family: 'Sora', sans-serif !important;
        }

        .booking-details-modal {
            text-align: left;
            padding: 10px;
        }

        .booking-details-modal .row {
            margin-bottom: 10px;
        }

        .booking-details-modal .badge {
            font-size: 12px;
            padding: 5px 10px;
            border-radius: 15px;
        }

        .booking-details-modal .bg-primary {
            background-color: var(--deep-blue) !important;
        }

        .booking-details-modal .bg-secondary {
            background-color: #6c757d !important;
        }

        .booking-details-modal .text-primary {
            color: var(--deep-blue) !important;
        }

        .booking-details-modal .text-success {
            color: #28a745 !important;
        }

        .booking-details-modal .text-muted {
            color: #6c757d !important;
        }

        .booking-details-modal .fw-bold {
            font-weight: 600 !important;
        }

        /* Calendar Filter Styles */
        .calendar-filters {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .filter-group .form-label {
            font-size: 12px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 4px;
        }

        .filter-group .form-select {
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 13px;
        }

        .filter-group .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service manage-pro-holidays">
        <div id="kt_app_content_container" class="app-container container padding-block booking-section">
            <div class="row row-gap-5">
                <div
                    class="col-md-12 d-flex justify-content-between align-items-center flex-lg-wrap flex-md-wrap flex-sm-wrap flex-wrap">
                    <div>
                        <h6 class="sora black">Manage Holidays</h6>
                        <p class="fs-14 sora light-black m-0"> Add and manage holidays to keep your calendar up to date. </p>
                    </div>
                    <div class="d-flex align-items-center gap-2 flex-wrap">
                        @can('holidays-create')
                            <a class="add-btn" data-bs-toggle="modal" data-bs-target="#import-holidays">
                                <i class="fa-solid fa-upload me-3"></i> Import Holidays
                            </a>
                            <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-holiday">
                                <i class="fa-solid fa-plus me-3"></i> Add Holidays
                            </a>
                        @endcan
                    </div>
                </div>

                <!-- Calendar view -->
                <div class="row business-booking-table">
                    <div class="col-lg-12">
                        <div class="table-container">
                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                <ul class="ms-auto booking-tabs nav nav-pills mb-3 justify-content-end" id="view-tab"
                                    role="tablist">
                                    <li class="nav-item " role="presentation"> <button class="nav-link calendar-view active"
                                            id="list-tab" data-bs-toggle="pill" data-bs-target="#list-view" type="button"
                                            role="tab" aria-controls="list-view" aria-selected="false" tabindex="-1">
                                            <i class="fa-solid fa-list me-2"></i> List View </button> </li>

                                    <li class="nav-item " role="presentation"> <button class="nav-link calendar-view"
                                            id="calendar-tab" data-bs-toggle="pill" data-bs-target="#calendar-view"
                                            type="button" role="tab" aria-controls="calendar-view"
                                            aria-selected="true"> <i class="fa-regular fa-calendar me-2"></i> Calendar View
                                        </button>
                                    </li>

                                </ul>
                            </div>

                            <div class="tab-content" id="view-tabContent">
                                <div class="tab-pane fade show active" id="list-view" role="tabpanel"
                                    aria-labelledby="list-tab" tabindex="0">
                                    <div class="d-flex gap-3 align-items-center flex-wrap">
                                        <!-- Search Input -->
                                        <div class="search_box"> <label for="customSearchInput">
                                                <i class="fas fa-search"></i> </label> <input class="search_input search"
                                                type="text" id="customSearchInput" placeholder="Search...">
                                        </div>

                                        <!-- Date Picker -->
                                        <label for="holidayDatePicker" class="date_picker">
                                            <div class="date-picker-container">
                                                <i class="bi bi-calendar-event calender-icon"></i>
                                                <input type="text" name="holidayDatePicker" id="holidayDatePicker"
                                                    class="datePicker w-200px ms-3" placeholder="Select date range" readonly>
                                                <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                            </div>
                                        </label>

                                        <!-- Clear Filters Button -->
                                        <button type="button" class="add-btn py-2" id="clearFiltersBtn">
                                            <i class="fas fa-times me-2"></i>Clear Filters
                                        </button>
                                    </div>

                                    <div class="col-lg-12">
                                        <div class="table-container">
                                            <table id="responsiveTable"
                                                class="manage-holiday vat-managment-table display w-100">
                                                <thead>
                                                    <tr>
                                                        <th>EVENT TITLE</th>
                                                        <th>DATE</th>
                                                        <th>COUNTRY</th>
                                                        <th>ACTION</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="holidayTableBody">
                                                    @include(
                                                        'dashboard.admin.holidays.partials.holiday-table',
                                                        [
                                                            'holidays' => $holidays,
                                                        ]
                                                    )
                                                </tbody>
                                            </table>
                                            <!-- Load More Button -->
                                            <div class="text-center mt-4" id="loadMoreContainer"
                                                style="{{ $totalCount > 10 ? '' : 'display: none;' }}">
                                                <button type="button" class="add-btn" id="loadMoreBtn">
                                                    <i class="fas fa-plus me-2"></i>Load More
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane fade" id="calendar-view" role="tabpanel" aria-labelledby="calendar-tab"
                                    tabindex="0">
                                    <!-- 📅 Calendar View Content -->
                                    <div class="row">
                                        <div class="col-lg-12  p-0">
                                            <div class="schedule-container ">
                                                <!-- Calendar Filter -->
                                                <div class="calendar-filters mb-3">
                                                    <div class="d-flex gap-3 align-items-center flex-wrap">
                                                        <!-- Simple Filter Dropdown -->
                                                        <div class="filter-group">
                                                            <label for="calendarFilter" class="form-label mb-1">Filter:</label>
                                                            <select id="calendarFilter" class="form-select form-select-sm" style="width: 160px;">
                                                                <option value="">All Holidays</option>
                                                                <option value="today">Today</option>
                                                                <option value="this_month">This Month</option>
                                                                <option value="this_year">This Year</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="calendar-header flex-align-space-btw">
                                                    <div class="flex-align-space-btw mb-10">
                                                        <div class="calendar-controls d-flex  gap-2 align-items-center">
                                                            <div class="d-flex gap-4">
                                                                <p id="holidays-today"
                                                                    class="m-0 fs-13 regular black cursor-pointer">Today
                                                                </p>
                                                                <button id="holidays-prev" class="btn-prev"><i
                                                                        class="fa-solid fa-chevron-left"></i></button>
                                                                <button id="holidays-next" class="btn-next"><i
                                                                        class="fa-solid fa-chevron-right"></i></button>
                                                            </div>
                                                            <h3 id="holidays-date-range"
                                                                class="m-0 fs-16 semi-bold black">
                                                                Loading...</h3>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="holidays-calendar"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('dashboard.admin.holidays.modal.add-holiday-modal')
        @include('dashboard.admin.holidays.modal.edit-holiday-modal')
        @include('dashboard.admin.holidays.modal.import-holidays-modal')
    @endsection

@push('js')

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="{{ asset('website') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>


    <script>

    function resetHolidayForm() {

        $('#holidayForm')[0].reset();
        $('.error').html('');
        $('.form-control, .form-select').removeClass('is-invalid is-valid error');
        $('#country_name').val(null).trigger('change');

        if (window.datePickerInstance) {
            window.datePickerInstance.clear();
        }

        $('.form-control, .form-select, .flatpickr').removeAttr('style');
        }

        $('#add-holiday, #edit-holiday').on('hidden.bs.modal', function () {
            resetHolidayForm();
        });

        $('#add-holiday, #edit-holiday').on('show.bs.modal', function () {
            $('.form-control, .form-select, .flatpickr').css({
                'display': 'block',
                'visibility': 'visible',
                'opacity': '1'
            });
        });
    </script>


    <script>
        let holidaysCalendar;
        let calendarInitialized = false;

        $(document).ready(function() {
            let currentOffset = 10;
            let isLoading = false;
            let currentSearchTerm = '';
            let currentDateFilter = '';
            let searchTimeout;
            let holidayDatePickerUsed = false;

            $('[data-control="select2"]').each(function() {
                $(this).select2({
                    placeholder: $(this).data('placeholder') || 'Select an option',
                    allowClear: true,
                    width: '100%'
                });
            });

            resetHolidayDatePicker();

            function resetHolidayDatePicker() {
                holidayDatePickerUsed = false;
                $('#holidayDatePicker').val('');

                if ($('#holidayDatePicker').data('daterangepicker')) {
                    $('#holidayDatePicker').data('daterangepicker').remove();
                }

                $('#holidayDatePicker').daterangepicker({
                    autoUpdateInput: false,
                    opens: 'center',
                    locale: {
                        format: 'MMM D, YYYY',
                        cancelLabel: 'Clear'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last Week': [moment().subtract(6, 'days'), moment()],
                        'Last Month': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')],
                        'Last 3 Months': [moment().subtract(3, 'months').startOf('month'), moment().endOf('month')]
                    }
                });

                attachHolidayDatePickerEvents();
                console.log('Holiday date picker completely reset to initial state');
            }

            function attachHolidayDatePickerEvents() {
                $('#holidayDatePicker').on('apply.daterangepicker', function(ev, picker) {
                    holidayDatePickerUsed = true;
                    if (picker.startDate.isSame(picker.endDate, 'day')) {
                        $(this).val(picker.startDate.format('MMM D, YYYY'));
                    } else {
                        $(this).val(picker.startDate.format('MMM D, YYYY') + ' - ' + picker.endDate.format('MMM D, YYYY'));
                    }
                    currentDateFilter = $(this).val();
                    console.log('Holiday date range applied:', $(this).val());
                    performFilter();
                });

                $('#holidayDatePicker').on('cancel.daterangepicker', function(ev, picker) {
                    resetHolidayDatePicker();
                    performFilter();
                });

                $('#holidayDatePicker').on('clear.daterangepicker', function(ev, picker) {
                    resetHolidayDatePicker();
                    performFilter();
                });
            }

            $('.date-picker-container .down-arrow').on('click', function() {
                var input = $(this).siblings('input.datePicker');
                if (input.data('daterangepicker')) {
                    input.data('daterangepicker').show();
                }
            });

                $(document).on('shown.bs.modal', '#add-holiday', function () {
                $("#holidayForm").validate({
                    rules: {
                        name: {
                            required: true,
                        },
                        date: {
                            required: true
                        },
                        country_name: {
                            required: true
                        },
                        status: {
                            required: true
                        }
                    },
                    messages: {
                        name: {
                            required: "Please enter holiday name",
                        },
                        date: {
                            required: "Please select date"
                        },
                        country_name: {
                            required: "Please select country"
                        },
                        status: {
                            required: "Please select status"
                        }
                    },
                    submitHandler: function(form) {
                        form.submit();
                    },
                });
            });

            $('#customSearchInput').on('input', function() {
                currentSearchTerm = $(this).val();
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    performFilter();
                }, 300);
            });

            $('#clearFiltersBtn').on('click', function() {
                $('#customSearchInput').val('');
                currentSearchTerm = '';
                resetHolidayDatePicker();
                performFilter();
            });

            function performFilter() {
                currentOffset = 0;
                let data = {};

                if (currentSearchTerm !== '') {
                    data.search = currentSearchTerm;
                }

                if (holidayDatePickerUsed && currentDateFilter) {
                    data.date = currentDateFilter;
                }

                console.log('Filter data:', data);

                $.ajax({
                    url: "{{ route('holidays.filter') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            $('#holidayTableBody').html(response.html);
                            currentOffset = response.next_offset;
                            updateLoadMoreButton(response);
                            bindEditButtons();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Filter error:', error);
                        console.error('Response:', xhr.responseText);
                        alert('Failed to filter records. Please try again.');
                    }
                });
            }

            $('#loadMoreBtn').on('click', function() {
                loadMoreHolidays();
            });

            function loadMoreHolidays() {
                if (isLoading) return;
                isLoading = true;

                let data = {
                    offset: currentOffset
                };

                if (currentSearchTerm !== '') {
                    data.search = currentSearchTerm;
                }

                if (holidayDatePickerUsed && currentDateFilter) {
                    data.date = currentDateFilter;
                }

                console.log('Load more data:', data);

                $.ajax({
                    url: "{{ route('holidays.load-more') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            $('#holidayTableBody').append(response.html);
                            currentOffset = response.next_offset;
                            updateLoadMoreButton(response);
                            bindEditButtons();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load more error:', error);
                        console.error('Response:', xhr.responseText);
                        alert('Failed to load more records. Please try again.');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            function updateLoadMoreButton(response) {
                if (response.has_more && response.count > 0) {
                    $('#loadMoreContainer').show();
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            function bindEditButtons() {
                $('.edit-holiday').off('click').on('click', function(e) {
                    e.preventDefault();
                    var holidayId = $(this).data('id');
                    $.ajax({
                        url: '/admin/holidays/' + holidayId + '/edit',
                        method: 'GET',
                        success: function(data) {
                            $('#holidayId').val(data.ids);
                            $('#edit_name').val(data.name);
                            // Ensure date shows as YYYY-MM-DD in the edit modal input (local timezone)
                            if (data.date) {
                                var d = new Date(data.date);
                                if (!isNaN(d.getTime())) {
                                    var yyyy = d.getFullYear();
                                    var mm = String(d.getMonth() + 1).padStart(2, '0');
                                    var dd = String(d.getDate()).padStart(2, '0');
                                    $('#edit_date').val(yyyy + '-' + mm + '-' + dd);
                                } else {
                                    $('#edit_date').val('');
                                }
                            } else {
                                $('#edit_date').val('');
                            }

                            $('#edit-holiday').data('holiday-data', data);
                        },
                        complete: function() {
                            $('#edit-holiday').modal('show');
                        },
                        error: function(xhr) {
                            alert('Failed to fetch holiday data. Please try again.');
                        }
                    });
                });
            }

            $("#editHolidayForm").validate({
                errorClass: "error",
                errorElement: "label",
                rules: {
                    name: {
                        required: true,
                    },
                    country_name: {
                        required: true
                    },
                    date: {
                        required: true
                    },
                    status: {
                        required: true
                    }
                },
                messages: {
                    name: {
                        required: "Please enter holiday name",
                    },
                    country_name: {
                        required: "Please select a country"
                    },
                    date: {
                        required: "Please select a date"
                    },
                    status: {
                        required: "Please select a status"
                    }
                },
                submitHandler: function(form) {
                    var holidayId = $('#holidayId').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/admin/holidays/' + holidayId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-holiday').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                },
            });

            $("#importHolidaysForm").validate({
                errorClass: "error",
                errorElement: "label",
                rules: {
                    import_file: {
                        required: true
                    },
                    country_name: {
                        required: true
                    },
                },
                messages: {
                    import_file: {
                        required: "Please select a file to import"
                    },
                    country_name: {
                        required: "Please select a country"
                    },
                },
                submitHandler: function(form) {
                    $(form).trigger('submit.import');
                },
            });

            $(document).on('shown.bs.modal', '#add-holiday', function () {
                $('#holidayForm input, #holidayForm select').prop('disabled', false).show();
                $('#name').prop('disabled', false).show();
                $('#date').prop('disabled', false).show();
                $('#country_name').prop('disabled', false).show();

                if ($('#country_name').hasClass('select2-hidden-accessible')) {
                    $('#country_name').select2('destroy');
                    $('#add-holiday .select2-container').remove();
                }

                $('#country_name').val('').prop('selectedIndex', 0);

                $('#country_name').select2({
                    dropdownParent: $('#add-holiday'),
                    placeholder: 'Select Country',
                    allowClear: true,
                    width: '100%'
                });

                var addHolidayWrapper = document.querySelector('#add-holiday .flatpickr');
                if (addHolidayWrapper && addHolidayWrapper._flatpickr) {
                    addHolidayWrapper._flatpickr.destroy();
                }

                flatpickr('#add-holiday .flatpickr', {
                    wrap: true,
                    dateFormat: 'Y-m-d',
                    allowInput: true,
                    onChange: function(selectedDates, dateStr, instance) {
                        instance.close();
                    }
                });
            });


            $(document).on('hidden.bs.modal', '#add-holiday', function() {
                const v = $("#holidayForm").data('validator');
                if (v) v.resetForm();

                $('#holidayForm')[0].reset();
                $('#name').val('');
                $('#date').val('');

                $('#holidayForm .error').remove();
                $('#holidayForm .form-control, #holidayForm .form-select')
                    .removeClass('error valid is-valid is-invalid')
                    .attr('aria-invalid', false)
                    .prop('disabled', false)
                    .show();

                if ($('#country_name').hasClass('select2-hidden-accessible')) {
                    $('#country_name').val('').trigger('change');
                    $('#country_name').select2('destroy');
                    $('#add-holiday .select2-container').remove();
                }
                $('#country_name').val('').prop('selectedIndex', 0);

                var addHolidayWrapperHidden = document.querySelector('#add-holiday .flatpickr');
                if (addHolidayWrapperHidden && addHolidayWrapperHidden._flatpickr) {
                    addHolidayWrapperHidden._flatpickr.clear();
                    addHolidayWrapperHidden._flatpickr.destroy();
                }
                $('#date').val('');
            });

            $('#edit-holiday').on('shown.bs.modal', function() {
                $('#edit_country_name').select2({
                    dropdownParent: $('#edit-holiday'),
                    placeholder: 'Select Country',
                    allowClear: true,
                    width: '100%'
                });
                $('#edit_status').select2({
                    dropdownParent: $('#edit-holiday'),
                    placeholder: 'Select Status',
                    allowClear: true,
                    width: '100%'
                });

                var holidayData = $(this).data('holiday-data');
                if (holidayData) {
                    $('#edit_country_name').val(holidayData.country_name).trigger('change');
                    $('#edit_status').val(holidayData.status).trigger('change');
                    $(this).removeData('holiday-data');
                }

                // Initialize flatpickr for edit modal with Y-m-d format and set date
                var editHolidayWrapper = document.querySelector('#edit-holiday .flatpickr');
                if (editHolidayWrapper && editHolidayWrapper._flatpickr) {
                    editHolidayWrapper._flatpickr.destroy();
                }

                var dateToSet = '';
                if (holidayData && holidayData.date) {
                    var parsed = new Date(holidayData.date);
                    if (!isNaN(parsed.getTime())) {
                        var y = parsed.getFullYear();
                        var m = String(parsed.getMonth() + 1).padStart(2, '0');
                        var d = String(parsed.getDate()).padStart(2, '0');
                        dateToSet = y + '-' + m + '-' + d;
                    }
                } else {
                    var inputVal = $('#edit_date').val();
                    if (inputVal) dateToSet = String(inputVal).substring(0, 10);
                }

                var fp = flatpickr('#edit-holiday .flatpickr', {
                    wrap: true,
                    dateFormat: 'Y-m-d',
                    allowInput: true,
                    defaultDate: dateToSet || null,
                    onChange: function(selectedDates, dateStr, instance) {
                        instance.close();
                    }
                });
                if (fp && dateToSet) {
                    fp.setDate(dateToSet, false, 'Y-m-d');
                }
            });

            $('#edit-holiday').on('hidden.bs.modal', function() {
                $('#edit_country_name').select2('destroy');
                $('#edit_status').select2('destroy');
            });

            $('#import-holidays').on('shown.bs.modal', function() {
                $('#import_country_name').select2({
                    dropdownParent: $('#import-holidays'),
                    placeholder: 'Select Country',
                    allowClear: false,
                    width: '100%'
                });
            });

            $('#import-holidays').on('hidden.bs.modal', function() {
                $('#import_country_name').select2('destroy');
            });

            bindEditButtons();

            $('#calendar-tab').on('shown.bs.tab shown.bs.pill', function(e) {
                if (!calendarInitialized) {
                    initializeHolidaysCalendar();
                    calendarInitialized = true;
                } else if (holidaysCalendar) {
                    holidaysCalendar.updateSize();
                    holidaysCalendar.refetchEvents();
                }
            });

            if ($('#calendar-tab').hasClass('active')) {
                setTimeout(function() {
                    if (!calendarInitialized) {
                        initializeHolidaysCalendar();
                        calendarInitialized = true;
                    }
                }, 100);
            }

            $('#calendar-tab').on('click', function() {
                setTimeout(function() {
                    if (!calendarInitialized) {
                        initializeHolidaysCalendar();
                        calendarInitialized = true;
                    } else if (holidaysCalendar) {
                        holidaysCalendar.updateSize();
                        holidaysCalendar.render();
                    }
                }, 150);
            });

            initializeSimpleCalendarFilter();
        });

        function initializeHolidaysCalendar() {
            const calendarEl = document.getElementById('holidays-calendar');
            if (!calendarEl) return;

            holidaysCalendar = new FullCalendar.Calendar(calendarEl, {
                headerToolbar: false,
                initialView: 'dayGridMonth',
                height: 'auto',
                editable: false,
                selectable: false,
                selectMirror: false,
                dayMaxEvents: 3,
                weekends: true,
                firstDay: 1,
                events: {
                    url: '{{ route('holidays.calendar-data') }}',
                    method: 'GET',
                    failure: function() {
                        console.error('There was an error while fetching holiday events!');
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Failed to load holiday events. Please refresh the page.'
                        });
                    },
                    success: function(data) {
                        console.log('Holiday calendar events loaded:', data.length);
                    }
                },
                eventDidMount: function(info) {
                    info.el.setAttribute('title', info.event.title);

                    info.el.addEventListener('click', function() {
                        Swal.fire({
                            title: info.event.title,
                            text: 'Holiday Date: ' + info.event.start.toLocaleDateString(),
                            icon: 'info',
                            confirmButtonText: 'OK'
                        });
                    });
                },
                datesSet: function(dateInfo) {
                    updateHolidaysDateRange(dateInfo.start, dateInfo.end);
                }
            });

            holidaysCalendar.render();

            $('#holidays-today').on('click', function() {
                holidaysCalendar.today();
            });

            $('#holidays-prev').on('click', function() {
                holidaysCalendar.prev();
            });

            $('#holidays-next').on('click', function() {
                holidaysCalendar.next();
            });
        }

        function updateHolidaysDateRange(start, end) {
            const currentDate = new Date(start.getTime() + (end.getTime() - start.getTime()) / 2);
            const options = {
                year: 'numeric',
                month: 'long'
            };

            const monthStr = currentDate.toLocaleDateString('en-US', options);
            $('#holidays-date-range').text(monthStr);
        }

        function initializeSimpleCalendarFilter() {
            $('#calendarFilter').on('change', function() {
                const filterValue = $(this).val();
                applySimpleCalendarFilter(filterValue);
            });
        }

        function applySimpleCalendarFilter(filterValue) {
            if (!holidaysCalendar) return;

            const today = new Date();
            let startDate = '';
            let endDate = '';
            let navigateDate = null;

            switch(filterValue) {
                case 'today':
                    startDate = today.toISOString().split('T')[0];
                    endDate = today.toISOString().split('T')[0];
                    navigateDate = new Date(today);
                    break;

                case 'this_month':
                    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                    startDate = firstDay.toISOString().split('T')[0];
                    endDate = lastDay.toISOString().split('T')[0];
                    navigateDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    break;

                case 'this_year':
                    const yearStart = new Date(today.getFullYear(), 0, 1);
                    const yearEnd = new Date(today.getFullYear(), 11, 31);
                    startDate = yearStart.toISOString().split('T')[0];
                    endDate = yearEnd.toISOString().split('T')[0];
                    navigateDate = new Date(today.getFullYear(), 0, 1);
                    break;

                default:
                    startDate = '';
                    endDate = '';
                    break;
            }

            const eventSource = holidaysCalendar.getEventSources()[0];
            if (eventSource) {
                eventSource.remove();
            }

            holidaysCalendar.addEventSource({
                url: '{{ route('holidays.calendar-data') }}',
                method: 'GET',
                extraParams: {
                    filter: filterValue,
                    start: startDate,
                    end: endDate
                },
                failure: function() {
                    console.error('There was an error while fetching filtered holiday events!');
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: 'Failed to load filtered holiday events. Please try again.'
                    });
                }
            });

            if (navigateDate) {
                holidaysCalendar.gotoDate(navigateDate);
            }
        }
    </script>

    <script>
        $('#importHolidaysForm').on('submit.import', function(e) {
            e.preventDefault();

            if (!$(this).valid()) {
                return false;
            }

            var formData = new FormData(this);
            var submitBtn = $(this).find('button[type="submit"]');
            var originalText = submitBtn.html();

            submitBtn.html('<i class="fa-solid fa-spinner fa-spin me-2"></i>Importing...');
            submitBtn.prop('disabled', true);

            $.ajax({
                url: $(this).attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    $('#import-holidays').modal('hide');
                    Swal.fire({
                        icon: response.type || 'success',
                        title: response.title || 'Success',
                        html: response.message || 'Import completed successfully'
                    }).then((result) => {
                        location.reload();
                    });
                },
                error: function(xhr) {
                    var response = xhr.responseJSON;
                    var errorMessage = 'Import failed. Please try again.';

                    if (response && response.message) {
                        errorMessage = response.message;
                    } else if (xhr.status === 422 && response && response.errors) {
                        var errors = [];
                        for (var field in response.errors) {
                            errors = errors.concat(response.errors[field]);
                        }
                        errorMessage = errors.join('<br>');
                    }

                    Swal.fire({
                        icon: 'error',
                        title: 'Import Failed',
                        html: errorMessage
                    });
                },
                complete: function() {
                    submitBtn.html(originalText);
                    submitBtn.prop('disabled', false);
                }
            });
        });
    </script>

@endpush
