<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="sora black  fs-34 semi_bold">💟 My favorites</h2>
                <a href="{{ route('favorite_professional') }}" class="fs-16 sora semi_bold dark-blue">View
                    All <i class="fa-solid fa-chevron-right icon-color ms-2"></i></a>
            </div>
            <div class="row pt-5">
                @foreach (auth()->user()->favoriteProfessionals as $professional)
                    <div class="col-md-3">
                        <a href="{{ route('professional_profile', $professional->profile->slug) }}">
                            <div class="card top-rated-card h-100 d-flex flex-column">
                                <div class="card-header border-0 p-0 position-relative">
                                    <img src="{{ asset('website') . '/' . $professional->profile->pic ?? '' }}"
                                        class="h-100 w-100 top-rated-image" alt="card-image" onerror="this.src='{{ asset('website/assets/images/default.png') }}'" /> 
                                    <div class="fav-icon position-absolute  bottom-10 ">
                                        <i class="fa-heart fa-5 fas text-danger mx-1"></i>
                                    </div>
                                </div>
                                <div class="card-body pb-0 p-5">
                                    <p class="fs-16 semi_bold black m-0 ">{{ $professional->name ?? '' }}
                                    </p>
                                    <p class="fs-15 sora bold m-0 light-black review-icon mx-1">4.5 <i
                                            class="fa-solid fa-star"></i>
                                        <span class="normal">(440)</span>
                                    </p>
                                    <p class="fs-14 regular light-black">
                                        {{ $professional->profile->location ?? '' }}</p>
                                </div>
                                <div class="card-footer border-0 pt-0 p-5">
                                    <span
                                        class="badge white-badge">{{ $professional->profile->company_name ?? '' }}</span>
                                </div>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
