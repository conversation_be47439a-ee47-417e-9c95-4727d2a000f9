<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Notification extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'sender_user_id',
        'title',
        'message',
        'filter_keyword',
        'read',
    ];

    protected $casts = [
        'read' => 'integer',
    ];

    /**
     * Get the user who receives the notification
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user who triggered/sent the notification
     */
    public function senderUser()
    {
        return $this->belongsTo(User::class, 'sender_user_id');
    }
}
