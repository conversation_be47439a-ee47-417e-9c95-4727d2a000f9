@extends('website.layout.master')
@section('content')

<div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard">
    <div id="kt_app_content_container" class="app-container container padding-block">
        <div class="row row-gap-5">
            <div class="col-md-12 mb-10 ms-3">
                <h4 class="sora black">My Bookings</h4>
            </div>
        </div>

        <div class="row business-booking-table">
            <div class="col-lg-12">
                <div class="table-container">
                    <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                        <ul class="ms-auto booking-tabs nav nav-pills mb-3 justify-content-end" id="view-tab"
                            role="tablist">
                            <li class="nav-item " role="presentation"> <button class="nav-link calendar-view"
                                    id="list-tab" data-bs-toggle="pill" data-bs-target="#list-view" type="button"
                                    role="tab" aria-controls="list-view" aria-selected="false" tabindex="-1"> <i
                                        class="fa-solid fa-list me-2"></i> List View </button>
                            </li>
                            <li class="nav-item " role="presentation"> <button class="nav-link calendar-view active"
                                    id="calendar-tab" data-bs-toggle="pill" data-bs-target="#calendar-view"
                                    type="button" role="tab" aria-controls="calendar-view" aria-selected="true"> <i
                                        class="fa-regular fa-calendar me-2"></i> Calendar View </button>
                            </li>
                        </ul>



                        <div class="tab-content" id="view-tabContent">
                            <div class="tab-pane fade" id="list-view" role="tabpanel" aria-labelledby="list-tab"
                                tabindex="0">
                                <div class="row row-gap-5 my-8">
                                    <div class="col-md-12">
                                        <div class="card-box">
                                            <p class="fs-18 semi_bold sora">Upcoming Bookings</p>
                                            <div class="row" id="upcoming-bookings-container">
                                                @include('dashboard.customer.partials.upcoming-bookings', ['upcomingBookings' => $upcomingBookings])
                                            </div>
                                            {{-- <div class="col-md-12 gap-2">
                                                <div class="card shadow-none booking_card flex-row py-2 px-3 gap-3">
                                                    <!-- Card Header -->
                                                    <div
                                                        class="card-header p-0 d-flex justify-content-between align-items-center border-0">
                                                        <div
                                                            class="d-flex align-items-center flex-column calendar-icon fs-20 semi_bold black">
                                                            <p class="m-0 fs-20 semi_bold sora">24</p>
                                                            <p class="m-0 fs-15 mormal">Dec</p>
                                                        </div>
                                                    </div>
                                                    <!-- Card Body -->
                                                    <div
                                                        class="card-body  p-0 d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <p class="mb-0 fs-15 sora balck normal">Hair Cut</p>
                                                            <p class="mb-0 fs-12 sora black normal light-black">Tue, 20
                                                                Aug 2024
                                                                2:45pm
                                                                <span class="status cancelled">Cancelled</span>
                                                            </p>
                                                            <p
                                                                class="mb-0 fs-12 sora black normal light-black opacity-6">
                                                                Justin
                                                                Dokidis
                                                                with Gustavo Dias</p>
                                                        </div>
                                                        <div class="d-flex gap-5 align-items-center">
                                                            <p class="fs-20 semi_bold black sora m-0">$110</p>
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButton2" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton2">
                                                                    <li>
                                                                        <a href=""
                                                                            data-bs-target="#rescheduleBookingModalLabel"
                                                                            data-bs-toggle="modal"
                                                                            class="dropdown-item  black fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-calendar-event me-2"></i>
                                                                            Reschedule
                                                                        </a>
                                                                    </li>
                                                                    <a href="" data-bs-target="#cancelBookingModalLabel"
                                                                        data-bs-toggle="modal"
                                                                        class="dropdown-item cancel fs-14 regular"
                                                                        type="button">
                                                                        <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                        Cancel
                                                                    </a>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12 gap-2">
                                                <div class="card shadow-none booking_card flex-row py-2 px-3 gap-3">
                                                    <!-- Card Header -->
                                                    <div
                                                        class="card-header x p-0 d-flex justify-content-between align-items-center border-0">
                                                        <div
                                                            class="d-flex align-items-center flex-column calendar-icon fs-20 semi_bold black">
                                                            <p class="m-0 fs-20 semi_bold sora">24</p>
                                                            <p class="m-0 fs-15 mormal">Dec</p>

                                                        </div>
                                                    </div>
                                                    <!-- Card Body -->
                                                    <div
                                                        class="card-body  p-0 d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <p class="mb-0 fs-15 sora balck normal">Hair Cut</p>
                                                            <p
                                                                class="mb-0 fs-12 sora black normal light-black service-time">
                                                                Tue, 20
                                                                Aug 2024
                                                                2:45pm
                                                                <span class="status complete">Complete</span>
                                                            </p>
                                                            <p
                                                                class="mb-0 fs-12 sora black normal light-black opacity-6">
                                                                Justin
                                                                Dokidis
                                                                with Gustavo Dias</p>
                                                        </div>
                                                        <div class="d-flex gap-5 align-items-center">
                                                            <p class="fs-20 semi_bold black sora m-0">$110</p>
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButton3" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton3">
                                                                    <li>
                                                                        <a href=""
                                                                            data-bs-target="#rescheduleBookingModalLabel"
                                                                            data-bs-toggle="modal"
                                                                            class="dropdown-item  black fs-14 regular "
                                                                            type="button">
                                                                            <i class="bi bi-calendar-event me-2"></i>
                                                                            Reschedule
                                                                        </a>
                                                                    </li>
                                                                    <li>
                                                                        <a href=""
                                                                            data-bs-target="#cancelBookingModalLabel"
                                                                            data-bs-toggle="modal"
                                                                            class="dropdown-item cancel fs-14 regular"
                                                                            type="button">
                                                                            <i
                                                                                class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Cancel
                                                                        </a>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div> --}}
                                        </div>
                                    </div>

                                    <div class="col-lg-12 mt-10">
                                        <p class="fs-16 sora black semi_bold">Past Bookings</p>
                                        <div id="responsiveTable" class="table-container">
                                            <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                                <div class="search_box">
                                                    <label for="customSearchInput">
                                                        <i class="fas fa-search"></i>
                                                    </label>
                                                    <input class="search_input search" type="text"
                                                        id="customSearchInput" placeholder="Search..." />
                                                </div>
                                                <!-- Select with dots -->
                                                <div class="dropdown search_box select-box">
                                                    <button
                                                        class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                                        type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <span><span class="dot all"></span>
                                                            All</span>
                                                    </button>
                                                    <ul class="dropdown-menu" id="status-filter-dropdown">
                                                        <li><a class="dropdown-item dropdown-status" href="#"
                                                                data-label="All" data-color="#4B5563"
                                                                data-status="all"><span class="dot all"></span>
                                                                All</a></li>
                                                        <li><a class="dropdown-item dropdown-status" href="#"
                                                                data-label="Completed" data-color="#10B981"
                                                                data-status="completed"><span
                                                                    class="dot completed"></span>
                                                                Completed</a></li>
                                                        {{-- <li><a class="dropdown-item dropdown-status" href="#"
                                                                data-label="Ongoing" data-color="#F59E0B"
                                                                data-status="ongoing"><span class="dot ongoing"></span>
                                                                Ongoing</a></li> --}}
                                                        <li><a class="dropdown-item dropdown-status" href="#"
                                                                data-label="Cancelled" data-color="#EF4444"
                                                                data-status="cancelled"><span
                                                                    class="dot cancelled-dot"></span>
                                                                Cancelled</a></li>
                                                    </ul>
                                                </div>
                                                <!-- Date Picker -->
                                                <label for="datePicker" class="date_picker">
                                                    <div class="date-picker-container">
                                                        <i class="bi bi-calendar-event calender-icon"></i>
                                                        <input type="text" name="datePicker"
                                                            class="datePicker w-200px ms-3" placeholder="Select Date">
                                                        <i class="fa fa-chevron-down down-arrow ms-9"></i>

                                                    </div>
                                                </label>

                                                <!-- Reset Filters -->
                                                <button type="button" id="pastFiltersResetBtn"
                                                    class="btn btn-light border ms-2">
                                                    Reset
                                                </button>

                                            </div>

                                            <!-- 📝 List View Content -->
                                            <table id="past-bookings-table responsiveTable"
                                                class="responsiveTable display nowrap w-100 mt-4">
                                                <thead>
                                                    <tr>
                                                        <th>Booking ID</th>
                                                        <th>Professional</th>
                                                        <th>Service Name</th>
                                                        <th>Status</th>
                                                        <th>Date & Time</th>
                                                        <th>Amount</th>
                                                        <th></th>
                                                    </tr>
                                                </thead>
                                                <tbody id="past-bookings-tbody">
                                                    @include('dashboard.customer.partials.past-bookings-table', ['pastBookings' => $pastBookings])
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                            </div>


                            <div class="tab-pane fade active show" id="calendar-view" role="tabpanel"
                                aria-labelledby="calendar-tab" tabindex="0">

                                <div class="row ">
                                    <!-- <div class="col-lg-3  col-md-4 calender-box calendar-duplicate">
                                                <div class="d-flex align-items-center w-100  justify-content-end">
                                                    <div id="inline-calendar" class="double-cal"></div>
                                                </div>

                                                <div class="mt-2 d-flex justify-content-end  gap-3">
                                                    <button id="cancel-btn" class="btn btn-secondary btn-sm">Cancel</button>
                                                    <button id="apply-btn" class=" button blue-button px-5 py-2">Apply</button>
                                                </div>
                                            </div> -->
                                    <div class="col-lg-12 col-md-12 py-0 px-7 new-customer-cal-view">
                                        <div class="schedule-container customer-calender ">
                                            <div class="calendar-header flex-align-space-between">
                                                <div class="flex-align-space-btw">
                                                    <div class="calendar-controls d-flex  gap-2 align-items-center">
                                                        <div class="d-flex gap-4">
                                                            <p id="today" class="m-0 fs-13 regular black">Today</p>
                                                            <button id="prev-week" class="btn-prev"><i
                                                                    class="fa-solid fa-chevron-left"></i></button>
                                                            <button id="next-week" class="btn-next"><i
                                                                    class="fa-solid fa-chevron-right"></i></button>
                                                        </div>
                                                        <h3 id="date-range" class="m-0 fs-16 semi-bold black">14 July
                                                            2025 - 20
                                                            July 2025
                                                        </h3>
                                                    </div>

                                                    <div class="search_box select-box"> 
                                                        <select id="periodFilter" class="search_input"> 
                                                            <option value="all">All Time</option> 
                                                            <option value="weekly">Weekly</option> 
                                                            <option value="monthly">Monthly</option> 
                                                        </select> 
                                                    </div>
                                                </div>
                                            </div>
                                            <div id="calendar"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
 
@include('dashboard.templates.modal.cancel-booking-modal')
@include('dashboard.templates.modal.rescheduled-booking-modal')
@include('dashboard.templates.modal.add-service-details-modal')

@endsection

@push('css')
    <!--begin::Vendor Stylesheets(used for this page only)-->
    <link href="{{ asset('website') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.css" rel="stylesheet"
        type="text/css" />
    <!--end::Vendor Stylesheets-->
    <style>
        /* Custom FullCalendar styles for customer booking */
        /* .customer-calender #calendar {
                                                                overflow: visible;
                                                            } */


        .customer-calender #calendar {
            overflow: scroll;
            height: 500px
        }

        .customer-calender .fc {
            font-family: 'Sora', sans-serif;
        }

        .customer-calender .fc-timegrid-slot {
            height: 40px;
        }

        .customer-calender .fc-timegrid-axis {
            width: 60px;
        }

        .customer-calender .fc-col-header-cell {
            background-color: var(--whisper-gray);
            border: 1px solid var(--border-color);
            padding: 10px;
            font-weight: 600;
            color: var(--black);
        }

        .customer-calender .fc-timegrid-slot {
            border-color: var(--border-color);
        }

        .customer-calender .fc-event {
            border-radius: 6px;
            border: none;
            font-size: 12px;
            font-weight: 500;
            padding: 2px 6px;
        }

        .customer-calender .fc-event-title {
            font-weight: 600;
        }

        .customer-calender .fc-event:hover {
            opacity: 0.8;
            cursor: pointer;
        }

        .customer-calender .fc-timegrid-now-indicator-line {
            border-color: var(--deep-blue);
            border-width: 2px;
        }

        .customer-calender .fc-timegrid-now-indicator-arrow {
            border-left-color: var(--deep-blue);
            border-right-color: var(--deep-blue);
        }

        /* Hide default scrollbars and adjust layout */
        .customer-calender .fc-scroller {
            overflow-x: auto;
            overflow-y: auto;
        }

        .customer-calender .fc-timegrid-body {
            min-width: 100%;
        }

        /* Custom SweetAlert styles for booking details */
        .booking-details-popup {
            font-family: 'Sora', sans-serif !important;
        }

        .booking-details-modal {
            text-align: left;
            padding: 10px;
        }

        .booking-details-modal .row {
            margin-bottom: 10px;
        }

        .booking-details-modal .badge {
            font-size: 12px;
            padding: 5px 10px;
            border-radius: 15px;
        }

        .booking-details-modal .bg-primary {
            background-color: var(--deep-blue) !important;
        }

        .booking-details-modal .bg-secondary {
            background-color: #6c757d !important;
        }

        .booking-details-modal .text-primary {
            color: var(--deep-blue) !important;
        }

        .booking-details-modal .text-success {
            color: #28a745 !important;
        }

        .booking-details-modal .text-muted {
            color: #6c757d !important;
        }

        .booking-details-modal .fw-bold {
            font-weight: 600 !important;
        }

        /* Status filter dot styles */
        .dot.ongoing {
            background-color: #F59E0B !important;
        }
    </style>
@endpush
@push('js')
    <!--begin::Vendor Javascripts(used for this page only)-->
    <script src="{{ asset('website') }}/assets/plugins/custom/fullcalendar/fullcalendar.bundle.js"></script>
    <!--end::Vendor Javascripts-->

    <script>
        $(document).ready(function () {
            var calendarEl = $('#calendar')[0];
            var calendar;
            // Initialize FullCalendar
            calendar = new FullCalendar.Calendar(calendarEl, {
                headerToolbar: false, // We'll use custom header
                initialView: 'timeGridWeek',
                height: 'auto',
                slotMinTime: '08:00:00',
                slotMaxTime: '20:00:00',
                slotDuration: '00:30:00',
                allDaySlot: false,
                nowIndicator: true,
                editable: false,
                selectable: false,
                selectMirror: false,
                dayMaxEvents: true,
                weekends: true,
                // Configure 24-hour time format
                slotLabelFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                eventTimeFormat: {
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                },
                events: {
                    url: '{{ route('customer_booking_calendar_data') }}',
                    method: 'GET',
                    failure: function () {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error!',
                            text: 'There was an error while fetching booking events!',
                            confirmButtonColor: '#006AA0'
                        });
                    }
                },
                eventClick: function (info) {
                    // Show booking details in SweetAlert
                    var booking = info.event.extendedProps;
                    var startTime = info.event.start.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    var endTime = info.event.end.toLocaleTimeString([], {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });

                    var statusBadge;
                    if (booking.status === 2) {
                        statusBadge = '<span class="badge bg-danger">Cancelled</span>';
                    } else if (booking.status === 1) {
                        statusBadge = '<span class="badge bg-success">Completed</span>';
                    } else if (booking.status === 0) {
                        const bookingDateTime = new Date(booking.booking_date + ' ' + booking.booking_time);
                        const now = new Date();
                        if (bookingDateTime < now) {
                            statusBadge = '<span class="badge bg-secondary">Past</span>';
                        } else if (bookingDateTime.toDateString() === now.toDateString()) {
                            statusBadge = '<span class="badge bg-info">Ongoing</span>';
                        } else {
                            statusBadge = '<span class="badge bg-warning text-dark">Upcoming</span>';
                        }
                    }

                    var htmlContent = `
                                                                    <div class="booking-details-modal">
                                                                        <div class="row mb-3">
                                                                            <div class="col-12 text-center">
                                                                                <h5 class="text-primary mb-2">${booking.service_name}</h5>
                                                                                ${statusBadge}
                                                                            </div>
                                                                        </div>
                                                                        <div class="row">
                                                                            <div class="col-6">
                                                                                <strong>Time:</strong><br>
                                                                                <span class="text-muted">${startTime} - ${endTime}</span>
                                                                            </div>
                                                                            <div class="col-6">
                                                                                <strong>Duration:</strong><br>
                                                                                <span class="text-muted">${booking.duration} minutes</span>
                                                                            </div>
                                                                        </div>
                                                                        <div class="row mt-3">
                                                                            <div class="col-6">
                                                                                <strong>Price:</strong><br>
                                                                                <span class="text-success fw-bold">$${booking.service_price}</span>
                                                                            </div>
                                                                            <div class="col-6">
                                                                                <strong>Provider:</strong><br>
                                                                                <span class="text-muted">${booking.provider_name}</span>
                                                                            </div>
                                                                        </div>
                                                                        ${booking.comments ? `
                                                                                                <div class="row mt-3">
                                                                                                    <div class="col-12">
                                                                                                        <strong>Comments:</strong><br>
                                                                                                        <span class="text-muted">${booking.comments}</span>
                                                                                                    </div>
                                                                                                </div>
                                                                                                ` : ''}
                                                                        <div class="row mt-3">
                                                                            <div class="col-12">
                                                                                <strong>Booking ID:</strong><br>
                                                                                <span class="text-muted">${booking.booking_number}</span>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                `;

                    Swal.fire({
                        title: 'Booking Details',
                        html: htmlContent,
                        icon: 'info',
                        confirmButtonText: 'Close',
                        confirmButtonColor: '#006AA0',
                        width: '500px',
                        customClass: {
                            popup: 'booking-details-popup'
                        }
                    });
                },
                eventDidMount: function (info) {
                    // Add custom styling based on booking status
                    if (info.event.extendedProps.status === 'past') {
                        $(info.el).css('opacity', '0.6');
                    }
                }
            });

            calendar.render();

            // Custom navigation buttons using jQuery
            $('#today').on('click', function () {
                calendar.today();
                updateDateRange();
            });

            $('#prev-week').on('click', function () {
                calendar.prev();
                updateDateRange();
            });

            $('#next-week').on('click', function () {
                calendar.next();
                updateDateRange();
            });

            // Update date range display
            function updateDateRange() {
                var view = calendar.view;
                var start = view.activeStart;
                var end = new Date(view.activeEnd);
                end.setDate(end.getDate() - 1); // Adjust end date

                var startStr = start.toLocaleDateString('en-US', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });
                var endStr = end.toLocaleDateString('en-US', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric'
                });

                $('#date-range').text(startStr + ' - ' + endStr);
            }

            // Initial date range update
            updateDateRange();

            // Past Bookings Search and Filter Functionality
            // Note: Filters only apply to past bookings, upcoming bookings remain unfiltered
            let currentSearchTerm = '';
            let currentStatusFilter = 'all';
            let currentDateFilter = '';
            let searchTimeout;

            // Search input functionality with debounce
            $('#customSearchInput').on('input', function () {
                currentSearchTerm = $(this).val().trim();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounced search
                searchTimeout = setTimeout(function () {
                    performSearch();
                }, 300); // 300ms delay
            });

            // Status dropdown functionality
            $(document).on('click', '.dropdown-status', function (e) {
                e.preventDefault();

                const label = $(this).data('label');
                const color = $(this).data('color');
                const status = $(this).data('status');

                console.log('Filter clicked:', {
                    label,
                    color,
                    status
                }); // Debug log

                // Update button text and color with proper dot class
                let dotClass = 'dot';
                if (status === 'all') dotClass = 'dot all';
                else if (status === 'completed') dotClass = 'dot completed';
                else if (status === 'ongoing') dotClass = 'dot ongoing';
                else if (status === 'cancelled') dotClass = 'dot cancelled-dot';

                $('.status-dropdown-button span').html(`<span class="${dotClass}"></span> ${label}`);

                currentStatusFilter = status;
                console.log('Current status filter set to:', currentStatusFilter); // Debug log
                performSearch();
            });

            // Update existing date picker to use the filter functionality
            if ($('.datePicker').length) {
                $('.datePicker').flatpickr({
                    mode: "range",
                    dateFormat: "M d, Y",
                    onChange: function (selectedDates, dateStr, instance) {
                        // Handle both single date and date range
                        if (selectedDates.length === 1) {
                            // Single date selected
                            const singleDate = selectedDates[0];
                            const formattedDate = singleDate.toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                year: 'numeric'
                            });
                            currentDateFilter = formattedDate;
                        } else if (selectedDates.length === 2) {
                            // Date range selected
                            currentDateFilter = dateStr;
                        } else {
                            // No dates selected
                            currentDateFilter = '';
                        }

                        console.log('Date filter updated:', currentDateFilter);
                        performSearch();
                    }
                });
            }

            // Main search function with AJAX
            function performSearch() {
                console.log('Performing AJAX search with:', {
                    searchTerm: currentSearchTerm,
                    statusFilter: currentStatusFilter,
                    dateFilter: currentDateFilter
                }); // Debug log

                // Show loading state
                $('#past-bookings-tbody').html(`
                                                        <tr>
                                                            <td colspan="7" class="text-center py-4">
                                                                <div class="d-flex flex-column align-items-center justify-content-center py-4">
                                                                    <div class="spinner-border text-primary mb-3" role="status">
                                                                        <span class="visually-hidden">Loading...</span>
                                                                    </div>
                                                                    <p class="fs-16 text-muted mb-0">Searching bookings...</p>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    `);

                // Prepare data for AJAX request
                const requestData = {};

                if (currentSearchTerm) {
                    requestData.search = currentSearchTerm;
                }

                if (currentStatusFilter && currentStatusFilter !== 'all') {
                    requestData.status = currentStatusFilter;
                }

                if (currentDateFilter) {
                    requestData.date_range = currentDateFilter;
                }

                // Make AJAX request
                $.ajax({
                    url: '{{ route('customer_booking_filter') }}',
                    method: 'GET',
                    data: requestData,
                    success: function (response) {
                        if (response.success) {
                            // Only update past bookings table (filters don't apply to upcoming bookings)
                            $('#past-bookings-tbody').html(response.past_html);
                        } else {
                            // Show error message
                            $('#past-bookings-tbody').html(`
                                                                    <tr>
                                                                        <td colspan="7" class="text-center py-4">
                                                                            <div class="d-flex flex-column align-items-center justify-content-center py-4">
                                                                                <i class="fas fa-exclamation-triangle fs-48 text-warning mb-3"></i>
                                                                                <p class="fs-16 text-muted mb-0">Error loading bookings</p>
                                                                                <p class="fs-14 text-muted">Please try again</p>
                                                                            </div>
                                                                        </td>
                                                                    </tr>
                                                                `);
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Search error:', error); // Debug log

                        // Show error message
                        $('#past-bookings-tbody').html(`
                                                                <tr>
                                                                    <td colspan="7" class="text-center py-4">
                                                                        <div class="d-flex flex-column align-items-center justify-content-center py-4">
                                                                            <i class="fas fa-exclamation-triangle fs-48 text-danger mb-3"></i>
                                                                            <p class="fs-16 text-muted mb-0">Failed to load bookings</p>
                                                                            <p class="fs-14 text-muted">Please refresh the page and try again</p>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            `);
                    }
                });
            }

            // Reset filters handler
            $(document).on('click', '#pastFiltersResetBtn', function () {
                // Reset state
                currentSearchTerm = '';
                currentStatusFilter = 'all';
                currentDateFilter = '';

                // Reset UI controls
                $('#customSearchInput').val('');
                $('.status-dropdown-button span').html('<span class="dot all"></span> All');
                const fp = document.querySelector('.datePicker');
                if (fp && fp._flatpickr) {
                    fp._flatpickr.clear();
                }

                // Re-run search with cleared filters
                performSearch();
            });

            // Handle inline calendar date selection using jQuery
            $('#apply-btn').on('click', function () {
                if (selectedRange.length === 2) {
                    var startDate = selectedRange[0];
                    var endDate = selectedRange[1];

                    // Navigate calendar to selected date range
                    calendar.gotoDate(startDate);
                    updateDateRange();
                } else if (selectedRange.length === 1) {
                    // Single date selected
                    calendar.gotoDate(selectedRange[0]);
                    updateDateRange();
                }
            });

            $('#cancel-btn').on('click', function () {
                if (typeof calendarInstance !== 'undefined') {
                    calendarInstance.clear();
                }
                selectedRange = [];
            });

            // Initialize past bookings filters on page load
            setTimeout(function () {
                // Set initial filter state
                currentStatusFilter = 'all';
                currentSearchTerm = '';
                currentDateFilter = '';

                // Set initial dropdown button state
                $('.status-dropdown-button span').html('<span class="dot all"></span> All');

                console.log('Past bookings filters initialized with:', {
                    statusFilter: currentStatusFilter,
                    searchTerm: currentSearchTerm,
                    dateFilter: currentDateFilter
                });
            }, 100);
        });

        // Handle cancel booking modal
        $(document).on('click', '[data-bs-target="#cancelBookingModalLabel"]', function () {
            const bookingId = $(this).data('booking-id');
            console.log('Cancel booking clicked for ID:', bookingId); // Debug log

            // Set the booking ID in the modal input field
            $('#booking_id').val(bookingId);

            // Clear any previous reason text
            $('#cancelReason').val('');

            console.log('Booking ID set in modal:', $('#booking_id').val()); // Debug log
        });
    </script>
@endpush