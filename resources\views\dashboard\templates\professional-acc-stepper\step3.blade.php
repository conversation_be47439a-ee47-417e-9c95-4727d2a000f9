<div class="container form-card">
    <div class="row">
        <div class="col-12 mb-3">
            <h2 class="fs-title">Qualifications & Certifications</h2>
            <p>Add your qualifications and certifications for verification.</p>
        </div>

        <div class="col-md-12">
            <h5>Product Certification</h5>
            <div class="custom-checkbox-group mb-3 gray-card">
                @foreach ($certifications as $certification)
                    <label class="custom-checkbox">
                        <input type="checkbox" name="product_certifications[]" value="{{ $certification->id }}"
                            {{ auth()->user()->product_cerficates->contains($certification->id) ? 'checked' : '' }}>

                        <span class="checkbox-label">
                        <img src="{{ asset('website') . '/' . $certification->image }}" style="border-radius: 50%" width="30" height="30" alt="">

                            {{ $certification->name ?? '' }}</span>
                    </label>
                @endforeach
            </div>
        </div>
    </div>

    @if (auth()->user()->certificates->count() == 0)
        <div class="gray-card mt-5 file-upload-group certificate-group initial-cert">
            <div class="row">
                <h4 class="mb-8 text-center cert-title">Certificate #1</h4>
                <div class="col-md-12 field-spacing-cust">
                    <label class="fieldlabels">Certification Title*</label>
                    <input type="text" class="no_validate" name="certificates[0][title]" placeholder="Enter ">

                  
                </div>
                <div class="row">
                    <div class="col-md-12 field-spacing-cust">
                        <label class="fieldlabels">Issued by*</label>
                        <input type="text" class="no_validate" name="certificates[0][issued_by]" placeholder="Enter name">
                    </div>


                    <div class="col-md-6 field-spacing-cust">
                        <label class="fieldlabels">Issued Date*</label>
                        <input type="date" class="no_validate issued-date" name="certificates[0][issued_date]" placeholder="Enter issued date" data-index="0">
                    </div>

                    <div class="col-md-6 field-spacing-cust">
                        <label class="fieldlabels">Expiry Date*</label>
                        <input type="date" class="no_validate end-date" name="certificates[0][end_date]" placeholder="Enter expiry date" data-index="0" disabled style="background-color: #e9ecef; color: #6c757d; cursor: not-allowed;">
                        <label class="error text-danger" style="display: none;"></label>
                    </div>
                </div>

                <div class="col-md-12 form-border">
                    <p class="manrope fw-600 light-black">Share Certificates</p>
                    <div class="file-upload-group">
                        <div class="file-upload-group">
                            <label class="upload-box">
                                <!-- <img src="http://127.0.0.1:8000/website/assets/images/upload.svg" alt="Upload Icon"> -->
                                <img src="{{ asset('website/assets/images/upload.svg') }}"    alt="Upload Icon">
                                <p>Upload Certificate</p>
                                <p class="mb-0">Maximum file size: 2 MB</p>
                                <p>Supported format: JPG and PNG</p>
                                <span class="add-file">
                                    <p class="upload-cert-btn no_validate">Upload</p>
                                </span>
                                <input type="file" name="certificates[0][image]" class="file-input no_validate"
                                    hidden="">
                            </label>
                            <div class="preview-container"></div>
                        </div>
                    </div>


                    <div class="exception-checkbox">
                        <label class="cert-excep">
                            <input type="checkbox" name="certificates[0][exception]" id="exceptionToggle">
                            <span class="">Certificate Exception</span>
                        </label>

                        <div class="exception-textarea" style="display: none;">
                            <label class="mb-2" for="w3review">Reason for Exception</label>
                            <textarea class="mb-0 no_validate" id="w3review" name="certificates[0][exception_reason]" rows="4" cols="50"
                                placeholder="Write reason for exception"></textarea>
                        </div>
                    </div>
                </div>

                <!-- <div class="mt-3 d-flex justify-content-between">
                    <button type="button" class="delete-block" style="display: none;">Delete This Block</button>
                </div> -->
            </div>
        </div>
    @endif

    <div class="col-md-12">
        <div id="certifications-wrapper">
            @foreach (auth()->user()->certificates ?? [] as $index => $certificate)
                <div class="gray-card my-5 file-upload-group certificate-group">
                     <h4 class="mb-8 text-center cert-title">Certificate #{{ $index + 1 }}</h4>
                    <div class="col-md-12 field-spacing-cust">
                        <label class="fieldlabels">Certification Title*</label>
                        <input type="text" class="no_validate" name="certificates[{{ $index }}][title]"
                            placeholder="Enter certification title" value="{{ $certificate->title }}">
                        <label class="fieldlabels">Issued by*</label>
                        <input class="no_validate" type="text" name="certificates[{{ $index }}][issued_by]"
                            value="{{ $certificate->issued_by }}" placeholder="Enter name">
                    </div>

                    <div class="row">
                        <div class="col-md-6 field-spacing-cust">
                            <label class="fieldlabels">Issued Date*</label>
                            <input class="no_validate issued-date" type="date" name="certificates[{{ $index }}][issued_date]"
                                value="{{ $certificate->issued_date }}" placeholder="Enter issued date" data-index="{{ $index }}">
                        </div>

                        <div class="col-md-6">
                            <label class="fieldlabels">Expiry Date*</label>
                            <input class="no_validate end-date" type="date" name="certificates[{{ $index }}][end_date]"
                                value="{{ $certificate->end_date }}" placeholder="Enter expiry date" data-index="{{ $index }}">
                            <label class="error text-danger" style="display: none;"></label>
                        </div>
                    </div>

                    <div class="col-md-12 form-border">
                        <p class="manrope fw-600 light-black">Share Certificates</p>
                        <div>
                            <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans" style="cursor:pointer;">
                                <img src="{{ asset('website') . '/' . $certificate->image }}" alt="Upload Icon">
                                <p>Upload Certificate</p>
                                <p class="mb-0">Maximum file size: 2 MB</p>
                                <p>Supported format: JPG and PNG</p>
                                <span class="add-file">
                                    <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                </span>
                                <input class="no_validate" name="certificates[{{ $index }}][image]"
                                    type="file" hidden="">
                                @if ($certificate->image)
                                    <input type="hidden" name="certificates[{{ $index }}][old_image]"
                                        value="{{ $certificate->image }}">
                                @endif
                            </label>
                        </div>
                        <div class="preview-container"></div>

                        <div class="exception-checkbox">
                            <label class="cert-excep">
                                <input class="no_validate" type="checkbox" id="exceptionToggle"
                                    name="certificates[{{ $index }}][exception]"
                                    {{ $certificate->exception ? 'checked' : '' }}>
                                <span class="checkmark">Certificate Exception</span>
                            </label>

                            <div class="exception-textarea" style="display: {{ $certificate->exception ? 'block' : 'none' }};">
                                <label class="mb-2" for="w3review_1">Reason for Exception</label>
                                <textarea class="mb-0 no_validate" id="w3review_1" name="certificates[{{ $index }}][exception_reason]"
                                    rows="4" cols="50" placeholder="Write reason for exception"> {{ $certificate->exception_reason }}</textarea>
                            </div>
                        </div>
                    </div>
                    @if(!$loop->first)
                    <div class="mt-3 d-flex justify-content-between">
                        <button type="button" class="delete-block">Delete This Block</button>
                    </div>
                    @endif
                </div>
            @endforeach
        </div>
        <button type="button" id="addMoreBtn" class="addMoreBtn blue-text mt-3">
            <span><i class="fas fa-plus"></i></span> Add More
        </button>
    </div>
</div>

<script>
$(document).ready(function() {
    console.log('Step3 certificate date functionality loaded');

    // Function to handle issued date changes and control expiry date field
    function handleIssuedDateChange(issuedDateInput) {
        const $issuedInput = $(issuedDateInput);
        const issuedDate = $issuedInput.val();

        // Find the corresponding end date input in the same certificate block
        const $certificateBlock = $issuedInput.closest('.gray-card, .file-upload-group');
        const $endDateInput = $certificateBlock.find('input[name*="[end_date]"]');

        console.log('Handling issued date change:', issuedDate, 'End date input found:', $endDateInput.length);

        if (issuedDate) {
            // Enable expiry date field and restore normal styling
            $endDateInput.prop('disabled', false);
            $endDateInput.removeClass('disabled-field');
            $endDateInput.css({
                'background-color': '#ffffff',
                'color': '#495057',
                'cursor': 'text',
                'opacity': '1'
            });

            // COMBINED APPROACH: Set min attribute AND JavaScript validation
            // Parse the issue date properly to avoid timezone issues
            const issueDateParts = issuedDate.split('-');
            const year = parseInt(issueDateParts[0]);
            const month = parseInt(issueDateParts[1]) - 1; // Month is 0-indexed
            const day = parseInt(issueDateParts[2]);

            // Create date object for TWO days after issue date to ensure issue date is disabled
            const dayAfterIssue = new Date(year, month, day + 2);
            const minDateString = dayAfterIssue.toISOString().split('T')[0];

            // Set min attribute to disable issue date and earlier dates in dropdown
            $endDateInput.attr('min', minDateString);
            $endDateInput[0].setAttribute('min', minDateString);

            // ALSO store issue date for JavaScript validation as backup
            $endDateInput.data('issue-date', issuedDate);

            console.log('Issue Date:', issuedDate);
            console.log('Min Date Set (day after issue):', minDateString);
            console.log('This disables issue date (' + issuedDate + ') and all earlier dates in dropdown');

            // Validate expiry date if it exists
            const currentEndDate = $endDateInput.val();
            if (currentEndDate && currentEndDate <= issuedDate) {
                $endDateInput.siblings('.error').text('Expiry date must be after issued date').show();
            } else {
                $endDateInput.siblings('.error').text('').hide();
            }

            console.log('End date field enabled, min date set to:', issuedDate);
        } else {
            // Disable and gray out expiry date field when no issued date is set
            $endDateInput.prop('disabled', true);
            $endDateInput.addClass('disabled-field');
            $endDateInput.css({
                'background-color': '#e9ecef',
                'color': '#6c757d',
                'cursor': 'not-allowed',
                'opacity': '0.7'
            });
            $endDateInput.val('');
            $endDateInput.removeAttr('min');
            $endDateInput.siblings('.error').text('').hide();

            console.log('End date field disabled');
        }
    }

    // Handle issued date changes for all certificates using event delegation
    $(document).on('change', 'input[name*="[issued_date]"], input.issued-date', function() {
        console.log('Issued date changed:', $(this).val());
        handleIssuedDateChange(this);
    });

    // Disable keyboard input for BOTH issue date and end date - only allow dropdown selection
    $(document).on('keydown keypress keyup input', 'input[name*="[issued_date]"], input.issued-date, input[name*="[end_date]"], input.end-date', function(e) {
        e.preventDefault(); // Prevents all keyboard input
        return false;
    });

    // ADDITIONAL APPROACH: Monitor for any value changes and validate immediately
    $(document).on('input propertychange paste', 'input[name*="[issued_date]"], input.issued-date, input[name*="[end_date]"], input.end-date', function() {
        // Block any manual input attempts
        const $input = $(this);
        const inputName = $input.attr('name') || $input.attr('class');

        if (inputName && (inputName.includes('end_date') || inputName.includes('end-date'))) {
            const endDate = $input.val();
            const storedIssueDate = $input.data('issue-date');

            if (storedIssueDate && endDate && endDate <= storedIssueDate) {
                // Immediately clear invalid selections
                $input.val('');
                $input.siblings('.error').text('Issue date (' + storedIssueDate + ') and all earlier dates are blocked!').show();
            }
        }
    });

    // STRICT VALIDATION: Block any date selection up to and including issue date
    $(document).on('change input', 'input[name*="[end_date]"], input.end-date', function() {
        const $endInput = $(this);
        const endDate = $endInput.val();
        const storedIssueDate = $endInput.data('issue-date');

        console.log('End date selected:', endDate);
        console.log('Stored issue date:', storedIssueDate);

        if (storedIssueDate && endDate) {
            // Convert both dates to Date objects for accurate comparison
            const issueDateObj = new Date(storedIssueDate);
            const endDateObj = new Date(endDate);

            console.log('Comparing:', endDateObj, '<=', issueDateObj);

            if (endDateObj <= issueDateObj) {
                console.log('BLOCKED: End date is same or before issue date');
                // BLOCK the selection - clear the field and show error
                $endInput.val('');
                $endInput.siblings('.error').text('Cannot select issue date (' + storedIssueDate + ') or any earlier date. Please select a date AFTER the issue date.').show();

                // Focus back to the field to force user to select a valid date
                setTimeout(() => {
                    $endInput.focus();
                }, 100);
            } else {
                console.log('ALLOWED: End date is after issue date');
                $endInput.siblings('.error').text('').hide();
            }
        }
    });

    // New validation approach handles all date validation above

    // Initialize all certificate fields on page load
    function initializeCertificateFields() {
        console.log('Initializing certificate fields...');

        // Find all certificate blocks
        $('.gray-card, .file-upload-group').each(function() {
            const $block = $(this);
            const $issuedInput = $block.find('input[name*="[issued_date]"], input.issued-date');
            const $endInput = $block.find('input[name*="[end_date]"], input.end-date');

            if ($issuedInput.length && $endInput.length) {
                console.log('Found certificate block with issued and end date inputs');

                // Initialize the issued date field - this handles both cases
                handleIssuedDateChange($issuedInput[0]);
            }
        });
    }

    // Initialize on page load
    setTimeout(initializeCertificateFields, 200);

    // Handle dynamically added certificates (Add More button)
    $(document).on('click', '#addMoreBtn', function() {
        console.log('Add More button clicked');

        // Wait for DOM to update, then initialize new fields
        setTimeout(function() {
            console.log('Initializing new certificate fields...');
            initializeCertificateFields();
        }, 500);
    });

    // Add CSS for disabled field styling
    if (!$('#certificate-date-styles-step3').length) {
        $('<style id="certificate-date-styles-step3">')
            .text(`
                .disabled-field {
                    background-color: #e9ecef !important;
                    color: #6c757d !important;
                    cursor: not-allowed !important;
                    opacity: 0.7 !important;
                }
                .disabled-field:focus {
                    box-shadow: none !important;
                    border-color: #ced4da !important;
                }
                input[name*="[end_date]"]:disabled,
                input.end-date:disabled {
                    background-color: #e9ecef !important;
                    color: #6c757d !important;
                    cursor: not-allowed !important;
                    opacity: 0.7 !important;
                }
                .error {
                    font-size: 12px;
                    margin-top: 5px;
                    display: block;
                }
            `)
            .appendTo('head');
    }

    console.log('Step3 certificate date functionality initialized');
});
</script>
