<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Message extends Model
{
    use HasFactory;
    protected $fillable = [
        'conversation_id',
        'sender_id',
        'message_type',
        'content',
        'attachments',
        'read_at',
        'is_edited',
        'edited_at',
        'sending_status',
        'delivered_at',
        'sent_at'
    ];

    protected $casts = [
        'read_at' => 'datetime',
        'edited_at' => 'datetime',
        'delivered_at' => 'datetime',
        'sent_at' => 'datetime',
        'is_edited' => 'integer',
        'attachments' => 'array'
    ];

    /**
     * Get the conversation this message belongs to
     */
    public function conversation()
    {
        return $this->belongsTo(Conversation::class, 'conversation_id');
    }

    /**
     * Get the sender of the message
     */
    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    /**
     * Check if message is read
     */
    public function isRead()
    {
        return !is_null($this->read_at);
    }

    /**
     * Check if message is delivered
     */
    public function isDelivered()
    {
        return !is_null($this->delivered_at);
    }

    /**
     * Mark message as read
     */
    public function markAsRead()
    {
        if (!$this->isRead()) {
            $this->update([
                'read_at' => now(),
                'sending_status' => 'read'
            ]);
        }
    }

    /**
     * Mark message as delivered
     */
    public function markAsDelivered()
    {
        if (!$this->isDelivered()) {
            $this->update([
                'delivered_at' => now(),
                'sending_status' => 'delivered'
            ]);
        }
    }

    /**
     * Mark message as sent
     */
    public function markAsSent()
    {
        $this->update([
            'sent_at' => now(),
            'sending_status' => 'sent'
        ]);
    }

    /**
     * Get formatted attachments
     */
    public function getFormattedAttachments()
    {
        if (!$this->attachments) {
            return [];
        }

        // Handle both array and string cases (for backward compatibility)
        $attachments = $this->attachments;
        if (is_string($attachments)) {
            $attachments = json_decode($attachments, true) ?? [];
        }

        if (!is_array($attachments)) {
            return [];
        }

        $formatted = [];
        foreach ($attachments as $attachment) {
            $formatted[] = [
                'name' => $attachment['name'] ?? '',
                'path' => $attachment['path'] ?? '',
                'type' => $attachment['type'] ?? '',
                'size' => $attachment['size'] ?? 0,
                'url' => asset('website/' . ($attachment['path'] ?? ''))
            ];
        }

        return $formatted;
    }

    /**
     * Check if message has attachments
     */
    public function hasAttachments()
    {
        if (!$this->attachments) {
            return false;
        }

        // Handle both array and string cases
        $attachments = $this->attachments;
        if (is_string($attachments)) {
            $attachments = json_decode($attachments, true) ?? [];
        }

        return !empty($attachments);
    }

    /**
     * Get attachment count by type
     */
    public function getAttachmentCountByType($type)
    {
        if (!$this->attachments) {
            return 0;
        }

        // Handle both array and string cases
        $attachments = $this->attachments;
        if (is_string($attachments)) {
            $attachments = json_decode($attachments, true) ?? [];
        }

        if (!is_array($attachments)) {
            return 0;
        }

        return collect($attachments)->where('type', $type)->count();
    }

    /**
     * Scope for unread messages
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope for messages in conversation
     */
    public function scopeInConversation($query, $conversationId)
    {
        return $query->where('conversation_id', $conversationId);
    }

    /**
     * Scope for messages by sender
     */
    public function scopeBySender($query, $senderId)
    {
        return $query->where('sender_id', $senderId);
    }
}
