<section class="top-rated pagination">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center ">
                    <div class="d-flex gap-5">
                        @include('svg.medal')
                        <div>
                            <h2 class="sora black  fs-34 semi_bold">Top Rated Near You</h2>
                            <p class="sora fs-14 light-black">Lorem ipsum dolor sit amet consectetur. Duis arcu sapien
                                justo
                                nulla.</p>
                        </div>
                    </div>
                    {{-- <a href="{{route('professional')}}" class="fs-16 sora semi_bold dark-blue">View All <i
                            class="fa-solid fa-chevron-right icon-color ms-2"></i></a> --}}
                </div>
                <div class="position-relative pt-15">
                    <!-- Swiper -->
                    <div id="topRatedSwiper" class="swiper top-rated-swiper mySwiper cards-length  position-unset">
                        <div class="swiper-wrapper h-auto">
                            @foreach ($nearbyUsers as $user)
                                <div class="swiper-slide h-auto">
                                    <a href="{{ route('professional_profile', $user->profile->slug) }}">
                                        <div class="card top-rated-card h-100">
                                            <div class="card-header border-0 p-0 position-relative">
                                            <div class="top-rated-image">
                                                <img src="{{ asset('website').'/'.$user->profile->pic ?? '' }}"
                                                    class="h-100 w-100 img-fluid" alt="card-image">
                                            </div>
                                                <div class="fav-icon position-absolute  bottom-10 "
                                                    onclick="event.preventDefault(); event.stopPropagation(); toggleFavorite({{ $user->id }}, this);">
                                                    <i
                                                        class="fa-heart {{ auth()->check() && auth()->user()->favoriteProfessionals->contains($user->id) ? 'fa-solid text-danger' : 'fa-regular' }}"></i>
                                                </div>
                                                @if($user->is_featured == 1)
                                                <div class="rated-div position-absolute">
                                                    <p class="fs-12 sora semi_bold m-0">@include('svg.rated')<span class="ps-2">FEATURED</span></p>
                                                </div>
                                                @endif
                                            </div>
                                            <div class="card-body pb-0 p-5">
                                                <p class="fs-16 semi_bold black m-0 ">{{ $user->name ?? '' }}</p>
                                                <p class="fs-15 sora bold m-0 light-black">
                                                    {{ $user->averageRating > 0 ? $user->averageRating : 'No rating' }} <i class="fa-solid fa-star review-icon mx-1"
                                                        ></i> <span class="normal">({{ $user->totalReviews ?? '' }})</span></p>
                                                <p class="fs-14 regular light-black">
                                                    {{ $user->profile->location ?? '' }}</p>
                                            </div>
                                            <div class="card-footer border-0 pt-0 p-5">
                                                <span class="badge white-badge ">{{ $user->profile->company_name ?? '' }}</span>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                        <div class="swiper-button-next top-rated-next"></div>
                        <div class="swiper-button-prev top-rated-prev"></div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

