@push('css')
    <link href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css" rel="stylesheet">

    <style>
        /* === Main Modal Styling === */




        /* === Upload Image Area === */
        .image-input {
            position: relative;
            width: 165px;
            height: 165px;
            border-radius: 50%;
            border: 1px dashed #ccc;
            background-color: #fafafa;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            transition: border-color 0.2s;
            margin-bottom: 10px;
        }

        .image-input:hover {
            border-color: #0014C8;
        }

        .image-input-wrapper {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            display: none;
        }

        .image-input:not(.image-input-empty) .image-input-wrapper {
            display: block;
        }

        .image-label {
            position: absolute;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #0014C8;
            font-weight: 500;
            font-size: 14px;
            text-align: center;
        }

        .image-input:not(.image-input-empty) .image-label {
            display: none;
        }

        .btn-icon {
            display: none;
        }

        .image-input:not(.image-input-empty) .btn-icon {
            position: absolute;
            top: 8px;
            right: 8px;
            display: inline-flex;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            padding: 4px;
            cursor: pointer;
            height: 20px;
            width: 20px;

        }

        /* === Cropper Modal === */
        #cropperModal {
            display: none;
            position: fixed;
            z-index: 1060;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.65);
            justify-content: center;
            align-items: center;
        }

        #cropperModal.show {
            display: flex;
        }

        .cropper-modal-dialog {
            background: #fff;
            border-radius: 12px;
            max-width: 600px;
            width: 90%;
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .cropper-modal-header,
        .cropper-modal-footer {
            padding: 16px 24px;
            border: none;
        }

        .cropper-modal-header {
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .cropper-modal-body {
            flex: 1;
            padding: 20px;
            text-align: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            max-height: 450px;
            /* 🔹 Fixed height */
            overflow: hidden;
            /* Prevents scroll or overflow */
        }

        .cropper-container-wrapper {
            width: 100%;
            max-width: 100%;
            height: 350px;
            /* 🔹 Controlled cropper height */
            overflow: hidden;
            border-radius: 10px;
            background: #f8f
        }
    </style>
@endpush

<div class="modal fade card-details" id="add-certification" tabindex="-1" aria-labelledby="add-certification"
    aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <h5 class="fs-15 semi_bold sora black">
                    Add Product Certification</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="certificationForm" action="{{ route('certifications.store') }}" enctype="multipart/form-data"
                method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row row-gap-5 ">
                        <div class="col-md-12">
                            <div class="position-relative form-add-services">

                                <div class="image-input" data-kt-image-input="true">
                                    <div class="image-input-wrapper"></div>

                                    <!-- Pencil upload icon -->
                                    <label class="image-label" data-kt-image-input-action="change">
                                        <!-- <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                            stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                                            <polyline points="17 8 12 3 7 8"></polyline>
                                            <line x1="12" y1="3" x2="12" y2="15"></line>
                                        </svg> -->
                                        <input type="file" id="avatar-input" name="avatar" accept=".png, .jpg, .jpeg" />
                                    </label>

                                    <!-- Cancel (X) button -->
                                    <span class="btn-icon cancel-btn" data-kt-image-input-action="cancel">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <line x1="18" y1="6" x2="6" y2="18"></line>
                                            <line x1="6" y1="6" x2="18" y2="18"></line>
                                        </svg>
                                    </span>
                                </div>
                                <div class="form-text">Allowed file types: png, jpg, jpeg. Image will be cropped to
                                    300x300px</div>
                                <label id="avatar-error" class="error" for="avatar"></label>
                            </div>



                            <!-- Cropper Modal -->

                            <!-- Cropper Modal -->
                            <div id="cropperModal">
                                <div class="cropper-modal-dialog">
                                    <div class="cropper-modal-content">
                                        <div class="cropper-modal-header">
                                            <h5 class="cropper-modal-title">Crop photo</h5>
                                            <button type="button" class="cropper-btn-close"
                                                data-cropper-dismiss="modal">×</button>
                                        </div>
                                        <div class="cropper-modal-body">
                                            <div class="cropper-container-wrapper">
                                                <img id="cropper-image" alt="Crop" />
                                                <div class="zoom-controls">
                                                    <button class="zoom-btn" id="zoom-btn" type="button"
                                                        title="Zoom In">
                                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2">
                                                            <circle cx="11" cy="11" r="8"></circle>
                                                            <path d="m21 21-4.35-4.35"></path>
                                                            <line x1="11" y1="8" x2="11" y2="14"></line>
                                                            <line x1="8" y1="11" x2="14" y2="11"></line>
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="cropper-modal-footer">
                                            <button type="button" class="btn btn-secondary"
                                                data-cropper-dismiss="modal">Cancel</button>
                                            <button type="button" id="cropper-save"
                                                class="btn btn-primary">Apply</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>









                        <!-- <div class="image-input image-input-empty" data-kt-image-input="true">
                                    <div class="image-input-wrapper"></div>
                                    <label
                                        class="image-label  flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <span class="mt-20 pt-9">Upload Image</span>
                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                    </label>
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                </div> -->

                        <label id="avatar-error" class="error" for="avatar"> </label>


                        <div class="col-lg-12">
                            <label for="certification-name" class="form-label form-input-labels">Name</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter name" id="name"
                                name="name" value="{{ old('name') }}">
                        </div>
                        <div class="col-md-12">
                            <label for="alt-image" class="form-label form-input-labels">Alt Tag</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="ALT tag of image"
                                id="alt_tag" name="alt_tag" value="{{ old('alt_tag') }}">
                        </div>
                        <div class="col-md-12">
                            <label for="image-description" class="form-label form-input-labels">Image
                                Description</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter description"
                                id="image_description" name="image_description" value="{{ old('image_description') }}">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0">
                    <button type="button" class="cancel-btn " data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="save-btn">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('js')

    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script>
        (function () {
            let cropper;
            let originalFile;

            const input = document.getElementById("avatar-input");
            const image = document.getElementById("cropper-image");
            const saveBtn = document.getElementById("cropper-save");
            const zoomBtn = document.getElementById("zoom-btn");
            const wrapper = document.querySelector(".image-input-wrapper");
            const imageInput = document.querySelector(".image-input");
            const modal = document.getElementById("cropperModal");
            const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
            const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
            const closeModalBtns = document.querySelectorAll('[data-cropper-dismiss="modal"]');

            // Open cropper when file is selected
            input.addEventListener("change", function (e) {
                const file = e.target.files[0];
                if (!file) return;

                originalFile = file;
                const reader = new FileReader();

                reader.onload = function (event) {
                    image.src = event.target.result;
                    modal.classList.add('show');

                    if (cropper) {
                        cropper.destroy();
                    }

                    cropper = new Cropper(image, {
                        aspectRatio: 1,
                        viewMode: 1,
                        autoCropArea: 0.8,
                        movable: true,
                        zoomable: true,
                        scalable: true,
                        rotatable: false,
                        cropBoxResizable: true,
                        cropBoxMovable: true,
                        dragMode: 'move'
                    });
                };

                reader.readAsDataURL(file);
            });

            // Zoom button
            zoomBtn.addEventListener("click", function () {
                if (cropper) {
                    cropper.zoom(0.1);
                }
            });

            // Save cropped image
            saveBtn.addEventListener("click", function () {
                if (cropper) {
                    const canvas = cropper.getCroppedCanvas({
                        width: 300,
                        height: 300,
                        imageSmoothingEnabled: true,
                        imageSmoothingQuality: 'high'
                    });

                    const dataUrl = canvas.toDataURL("image/jpeg", 0.9);

                    // Display cropped image in wrapper
                    wrapper.style.backgroundImage = `url(${dataUrl})`;
                    imageInput.classList.remove("image-input-empty");

                    // Replace file input with cropped image
                    canvas.toBlob(function (blob) {
                        const file = new File([blob], "certification.jpg", {
                            type: "image/jpeg",
                            lastModified: Date.now()
                        });

                        const dt = new DataTransfer();
                        dt.items.add(file);
                        input.files = dt.files;
                    }, 'image/jpeg', 0.9);

                    // Close modal
                    modal.classList.remove('show');
                    if (cropper) {
                        cropper.destroy();
                        cropper = null;
                    }
                }
            });

            // Cancel button - revert to original
            cancelBtn.addEventListener("click", function (e) {
                e.preventDefault();
                e.stopPropagation();

                if (wrapper.style.backgroundImage && originalFile) {
                    const dt = new DataTransfer();
                    dt.items.add(originalFile);
                    input.files = dt.files;
                } else {
                    input.value = "";
                    imageInput.classList.add("image-input-empty");
                }
            });

            // Remove button - clear everything
            removeBtn.addEventListener("click", function (e) {
                e.preventDefault();
                e.stopPropagation();

                wrapper.style.backgroundImage = "";
                input.value = "";
                imageInput.classList.add("image-input-empty");
                originalFile = null;
            });

            // Close modal buttons
            closeModalBtns.forEach(btn => {
                btn.addEventListener('click', function () {
                    modal.classList.remove('show');
                    if (cropper) {
                        cropper.destroy();
                        cropper = null;
                    }
                    // Clear the file input if user cancels cropping
                    input.value = "";
                });
            });
        })();
    </script>


@endpush