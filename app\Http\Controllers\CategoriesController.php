<?php

namespace App\Http\Controllers;
use Illuminate\Support\{Str, Facades\DB, Facades\Validator, Facades\Log};
use App\Http\Controllers\Controller;
use App\Http\Requests\CategoryRequest;
use App\Services\CategoryService;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use App\Models\{Category, SubCategory};


class CategoriesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public $categoryService;
    function __construct()
    {
        $this->categoryService = new CategoryService();
        $this->middleware('permission:categories-list|categories-create|categories-edit|categories-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:categories-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:categories-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:categories-delete', ['only' => ['destroy']]);
        $this->middleware('permission:categories-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $categories = Category::orderBy('created_at', 'desc')->paginate(10);
        $subcategories = SubCategory::orderBy('created_at', 'desc')->paginate(10);
        $activeCategories = Category::where('status', 1)->get();

        // Preserve tab parameter in pagination links
        $tab = $request->get('tab', 'categories');
        $categories->appends(['tab' => 'categories']);
        $subcategories->appends(['tab' => 'subcategories']);

        return view('dashboard.admin.categories.categories', [
            'categories' => $categories,
            'subcategories' => $subcategories,
            'activeCategories' => $activeCategories,
            'currentTab' => $tab
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('categories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  CategoryRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(CategoryRequest $request)
    {
        $alreadyAdded = Category::where('name', $request->name)->exists();
        if ($alreadyAdded) {
            return back()->with([
                'type' => 'warning',
                'message' => 'You have already added this category',
                'title' => 'Already added'
            ]);
        }
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'description' => ['required', 'regex:' . config('constant.description_regex')],
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'image_description' => ['required', 'regex:' . config('constant.description_regex'), 'max:' . config('constant.image_description_length')],
            'alt_tag' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.alt_tag_length')],
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'alt_tag.required' => 'Please enter alt tag',
            'alt_tag.regex' => $messagesConfig['input']['regex'],
            'alt_tag.max' => $messagesConfig['alt_tag']['max'],
            'image_description.required' => 'Please enter image description',
            'image_description.regex' => $messagesConfig['input']['regex'],
            'image_description.max' => $messagesConfig['image_description']['max'],
            'name.required' => 'Please enter category name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['name']['max'],
            'description.required' => 'Please enter description',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $categoryData = $validator->validated();
            if ($request->hasFile("avatar")) {
                $categoryData['image'] = $this->storeImage("category-images", $request->file('avatar'));
            }
            $categoryData['slug'] = Str::slug($request->input('name'));
            Category::create($categoryData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Category Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $category = Category::where('ids', $id)->firstOrFail();
        return response()->json($category);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $category = Category::where('ids', $id)->firstOrFail();
        return response()->json($category);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  CategoryRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(CategoryRequest $request, $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'description' => ['required', 'regex:' . config('constant.description_regex')],
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'image_description' => ['required', 'regex:' . config('constant.description_regex'), 'max:' . config('constant.image_description_length')],
            'alt_tag' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.alt_tag_length')],
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'alt_tag.required' => 'Please enter alt tag',
            'alt_tag.regex' => $messagesConfig['input']['regex'],
            'alt_tag.max' => $messagesConfig['alt_tag']['max'],
            'image_description.required' => 'Please enter image description',
            'image_description.regex' => $messagesConfig['input']['regex'],
            'image_description.max' => $messagesConfig['image_description']['max'],
            'name.required' => 'Please enter category name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['name']['max'],
            'description.required' => 'Please enter description',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $category = Category::where('ids', $id)->firstOrFail();
            $categoryData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($category->image);
                $categoryData['image'] = $this->storeImage('category-images', $request->file('avatar'));
            }
            if ($category->name !== $request->input('name')) {
                $categoryData['slug'] = Str::slug($request->input('name'));
            }
            $category->update($categoryData);
            DB::commit();
            return response()->json(["type" => "success", "message" => 'Category updated successfully', "title" => "Updated"]);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $category = Category::where('ids', $id)->firstOrFail();
        $subcategories = SubCategory::where('category_id', $category->id)->get();
        foreach ($subcategories as $subcategory) {
            $this->deleteImage($subcategory->image);
            $subcategory->delete();
        }
        $this->deleteImage($category->image);
        $category->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    }

    public function updateStatus(Request $request)
    {
        $category = Category::findOrFail($request->category_id);
        $category->status = $request->status;
        $category->save();

        return response()->json(['success' => true]);
    }
    function getSubCategories($category_id)
    {
        $subcategories = $this->categoryService->getSubCategories($category_id);
        return api_response(true, "Subcategories", $subcategories);
    }

    /**
     * Search categories and subcategories
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function search(Request $request)
    {
        // Use has() and trim to properly handle "0" as valid search term
        $query = $request->has('query') ? trim($request->get('query')) : '';
        $type = $request->get('type', 'categories'); // categories or subcategories
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        // Clean up date values - remove empty strings (using !== '' for "0" compatibility)
        $dateFrom = ($dateFrom !== '' && $dateFrom !== null) ? $dateFrom : null;
        $dateTo = ($dateTo !== '' && $dateTo !== null) ? $dateTo : null;

        Log::info('Category search request', [
            'query' => $query,
            'type' => $type,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
            'has_query' => $query !== '',
            'has_dates' => $dateFrom !== null && $dateTo !== null
        ]);

        try {
            if ($type === 'categories') {
                $categoriesQuery = Category::with('subcategories');

                // Apply text search filter (check !== '' to handle "0" as valid search)
                if ($query !== '') {
                    $categoriesQuery->where(function ($q) use ($query) {
                        $q->where('name', 'LIKE', '%' . $query . '%')
                            ->orWhere('description', 'LIKE', '%' . $query . '%');
                    });
                }

                // Apply date filter
                if ($dateFrom && $dateTo) {
                    $categoriesQuery->whereDate('created_at', '>=', $dateFrom)
                        ->whereDate('created_at', '<=', $dateTo);
                }

                $categories = $categoriesQuery->orderBy('created_at', 'desc')->get();
                Log::info('Categories found', ['count' => $categories->count()]);

                $html = view('dashboard.admin.categories.partials.categories-search-results', compact('categories'))->render();

                return response()->json([
                    'success' => true,
                    'html' => $html,
                    'count' => $categories->count()
                ]);
            } else {
                $subcategoriesQuery = SubCategory::with('category');

                // Apply text search filter (check !== '' to handle "0" as valid search)
                if ($query !== '') {
                    $subcategoriesQuery->where(function ($q) use ($query) {
                        $q->where('name', 'LIKE', '%' . $query . '%')
                            ->orWhere('description', 'LIKE', '%' . $query . '%')
                            ->orWhereHas('category', function ($categoryQuery) use ($query) {
                                $categoryQuery->where('name', 'LIKE', '%' . $query . '%');
                            });
                    });
                }
                // Apply date filter
                if ($dateFrom && $dateTo) {
                    $subcategoriesQuery->whereDate('created_at', '>=', $dateFrom)->whereDate('created_at', '<=', $dateTo);
                }
                $subcategories = $subcategoriesQuery->orderBy('created_at', 'desc')->get();

                $html = view('dashboard.admin.categories.partials.subcategories-search-results', compact('subcategories'))->render();

                return response()->json([
                    'success' => true,
                    'html' => $html,
                    'count' => $subcategories->count()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Category search failed', [
                'error' => $e->getMessage(),
                'query' => $query,
                'type' => $type,
                'date_from' => $dateFrom,
                'date_to' => $dateTo,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Search failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
