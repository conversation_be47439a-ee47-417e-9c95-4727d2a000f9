@forelse ($bookings as $booking)
    <tr>
        <td data-label="Booking ID">{{ $booking->booking_number }}</td>
        <td data-label="Customer Name">
            {{ $booking->client_name ? $booking->client_name : $booking->customer->name ?? '-' }}
        </td>
        <td data-label="Service Name">{{ $booking->service->name ?? '' }}</td>
        <td data-label="Service Type">
            @if ($booking->service->category ?? false)
                {{ $booking->service->category->name ?? '' }}
            @else
                -
            @endif
        </td>
        @if (auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']))
            <td data-label="Provider">{{ $booking->service->user->name ?? '-' }}</td>
        @endif
        <td data-label="Status">
            @if ($booking->status == 0)
                @php
                    // Create booking datetime and compare with current datetime
                    $bookingDateTime = \Carbon\Carbon::parse($booking->booking_date . ' ' . $booking->booking_time);
                    $now = \Carbon\Carbon::now();
                    $isFuture = $bookingDateTime->gt($now);
                @endphp
                @if ($isFuture)
                    <span class="badge bg-warning text-dark px-3 py-2">Upcoming</span>
                @else
                    <span class="badge bg-info px-3 py-2 text-white">Ongoing</span>
                @endif
            @elseif($booking->status == 1)
                <span class="badge bg-success px-3 py-2 text-white">Completed</span>
            @elseif($booking->status == 2)
                <span class="badge bg-danger px-3 py-2 text-white">Cancelled</span>
            @elseif($booking->status == 3)
                <span class="badge bg-success px-3 py-2 text-white">Refunded</span>
            @else
                <span class="badge bg-danger px-3 py-2 text-white">Denied Refund</span>
            @endif
        </td>
        <td data-label="Date & Time">
            {{ Carbon\Carbon::parse($booking->booking_date)->format('l, d M Y') }}
            -
            {{ \Carbon\Carbon::parse($booking->booking_time)->format('H:i') }}
        </td>
        <td data-label="Amount">${{ $booking->total_amount ?? '' }}</td>
        <td data-label="Action">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    @if ($booking->booking_number && $booking->ids)
                        <li>
                            <a class="dropdown-item view fs-14 regular"
                                href="{{ route('booking.detail', ['booking_number' => $booking->booking_number, 'ids' => $booking->ids]) }}">
                                <i class="bi bi-eye view-icon"></i>
                                View Details
                            </a>
                        </li>
                    @endif
                    @if ($booking->status == 0)
                        @if ($booking->hasTimePassed())
                            <li>
                                <button class="dropdown-item complete fs-14 regular booking-action" type="button"
                                    data-booking-id="{{ $booking->id }}" data-action="complete">
                                    <i class="bi bi-check-circle complete-icon"></i>
                                    Mark as Complete
                                </button>
                            </li>
                        @endif
                        @if (!auth()->user()->hasAnyRole(['admin', 'super admin']))
                            <li>
                                @php
                                    $bookingDateTime = \Carbon\Carbon::parse(
                                        $booking->booking_date . ' ' . $booking->booking_time,
                                    );
                                @endphp
                                @if ($bookingDateTime->isFuture())
                                    <a href="" data-bs-target="#cancelBookingModalLabel" data-bs-toggle="modal"
                                        data-booking-id="{{ $booking->ids }}"
                                        class="dropdown-item cancel fs-14 regular" type="button">
                                        <i class="fa-solid fa-xmark cancel-icon"></i>
                                        Cancel & Refund
                                    </a>
                                @endif
                                {{-- <button class="dropdown-item cancel fs-14 regular booking-action" type="button"
                                data-booking-id="{{ $booking->id }}" data-action="cancel">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Cancel
                            </button> --}}
                            </li>
                        @endif
                    @endif
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="{{ auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']) ? '9' : '8' }}" class="text-center">No
            bookings found</td>
    </tr>
@endforelse
@include('dashboard.templates.modal.cancel-booking-modal')
