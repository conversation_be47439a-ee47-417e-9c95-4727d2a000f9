@extends('dashboard.layout.master')
@push('css')
<style>
    .service-hero-card {
        background: linear-gradient(135deg, var(--deep-blue) 0%, var(--light-blue) 100%);
        border-radius: 16px;
        border: none;
        box-shadow: 0 8px 32px rgba(2, 12, 135, 0.2);
        overflow: hidden;
        position: relative;
        color: white;
    }

    .service-hero-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(30%, -30%);
    }

    .service-image-container {
        position: relative;
        overflow: hidden;
        border-radius: 12px;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .service-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .service-image:hover {
        transform: scale(1.05);
    }

    .service-title {
        font-size: 32px;
        font-weight: 800;
        color: white;
        margin-bottom: 12px;
        line-height: 1.2;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .service-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 16px;
        margin-bottom: 0;
        font-weight: 500;
    }

    .price-badge {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 20px;
        font-weight: 700;
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .price-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.2);
    }

    .info-card {
        background: var(--white);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    /* .info-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--deep-blue);
    } */

    .info-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
    }

    .info-card .info-icon {
        background: var(--deep-blue) !important;
        color: white !important;
    }

    .info-card .info-icon i {
        color: white !important;
    }

    .info-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-bottom: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        background: var(--deep-blue) !important;
        color: white !important;
        box-shadow: 0 2px 8px rgba(2, 12, 135, 0.3);
    }

    .section-icon i {
        color: white !important;
    }

    .staff-section-card, .description-section-card, .availability-section-card {
        background: var(--white);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;
    }

    /* .staff-section-card::before, .description-section-card::before, .availability-section-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--deep-blue);
    } */

    /* Availability Section Styles */
    .availability-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .availability-day-card {
        background: var(--ice-blue);
        border-radius: 8px;
        padding: 16px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .availability-day-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .day-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color);
    }

    .day-name {
        color: var(--deep-blue);
        font-weight: 600;
        margin: 0;
        font-size: 16px;
    }

    .slot-count {
        background: var(--deep-blue);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .time-slots {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .time-slot {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: var(--white);
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid var(--border-color);
        font-size: 14px;
    }

    .time-range {
        color: var(--deep-blue);
        font-weight: 500;
        flex: 1;
    }

    .date-badge {
        background: var(--yellow);
        color: #FFF;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 500;
        margin-left: 8px;
    }

    .time-slot i {
        color: var(--deep-blue);
        font-size: 12px;
    }

    .info-label {
        font-size: 11px;
        font-weight: 700;
        color: var(--light-gray);
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 8px;
    }

    .info-value {
        font-size: 18px;
        font-weight: 700;
        color: var(--black);
        line-height: 1.4;
    }

    .staff-card {
        background: var(--ice-blue);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        padding: 16px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
    }

    .staff-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: var(--deep-blue);
    }

    .staff-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    .staff-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    .staff-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 12px;
        border: 2px solid white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .staff-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .staff-name {
        font-size: 14px;
        font-weight: 600;
        color: var(--black);
        margin-bottom: 2px;
    }

    .staff-email {
        font-size: 12px;
        color: var(--light-gray);
        margin-bottom: 0;
    }

    .staff-details {
        margin-top: 12px;
    }

    .staff-category, .staff-phone {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--light-black);
        margin-bottom: 6px;
    }

    .staff-category i, .staff-phone i {
        margin-right: 6px;
        color: var(--light-gray);
        width: 14px;
        font-size: 11px;
    }

    .description-content {
        font-size: 15px;
        line-height: 1.6;
        color: var(--light-black);
        background: var(--ice-blue);
        padding: 20px;
        border-radius: 8px;
        /* border-left: 4px solid var(--deep-blue); */
    }

    .back-button {
        background: var(--deep-blue);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 600;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        box-shadow: 0 2px 8px rgba(2, 12, 135, 0.3);
        font-size: 14px;
    }

    .back-button:hover {
        background: var(--light-blue);
        color: white;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(2, 12, 135, 0.4);
    }

    .page-header {
        background: var(--white);
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        border: 1px solid var(--border-color);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
    }
/* 
    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--deep-blue);
    } */

    .gradient_heading {
        background: linear-gradient(to right, var(--yellow), var(--light-blue));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>
@endpush

@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <!-- Page Header -->
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center flex-wrap row-gap-10">
                    <div>
                        <h4 class="sora black mb-2">Service <span class="black">Details</span></h4>
                        <p class="fs-14 sora light-black m-0">Complete information about your service</p>
                    </div>
                    <a href="{{ route('services.index') }}" class="back-button">
                        <i class="fas fa-arrow-left"></i>
                        Back to Services
                    </a>
                </div>
            </div>

            <!-- Service Hero Section -->
            <div class="service-hero-card p-4 mb-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="pe-4">
                            <h1 class="service-title">{{ $service->name ?? 'Service Name' }}</h1>
                            <p class="service-subtitle">
                                <i class="fas fa-clock me-2 text-white"></i>
                                {{ $service->duration ?? '0' }} minutes duration
                            </p>
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="price-badge">
                            ${{ $service->price ?? '0' }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Details Grid -->
            <div class="row g-4 mb-4">
                <!-- Duration Card -->
                <div class="col-md-6 col-lg-3">
                    <div class="info-card p-4 h-100">
                        <div class="info-icon duration">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="info-label">Duration</div>
                        <div class="info-value">{{ $service->duration ?? '0' }} minutes</div>
                    </div>
                </div>

                <!-- Category Card -->
                <div class="col-md-6 col-lg-3">
                    <div class="info-card p-4 h-100">
                        <div class="info-icon category">
                            <i class="fas fa-tags"></i>
                        </div>
                        <div class="info-label">Category</div>
                        <div class="info-value">{{ $service->category->name ?? 'Not specified' }}</div>
                    </div>
                </div>

                <!-- Subcategory Card -->
                <div class="col-md-6 col-lg-3">
                    <div class="info-card p-4 h-100">
                        <div class="info-icon subcategory">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="info-label">Subcategory</div>
                        <div class="info-value">{{ $service->subcategory->name ?? 'Not specified' }}</div>
                    </div>
                </div>

                <!-- Price Card -->
                <div class="col-md-6 col-lg-3">
                    <div class="info-card p-4 h-100">
                        <div class="info-icon description">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="info-label">Price</div>
                        <div class="info-value">${{ $service->price ?? '0' }}</div>
                    </div>
                </div>
            </div>

            <!-- Service Availability Section -->
            @if($service->availabilities && $service->availabilities->count() > 0)
            <div class="row g-4 mb-4">
                <div class="col-md-12">
                    <div class="availability-section-card p-4">
                        <div class="d-flex align-items-center mb-4">
                            <div class="section-icon me-3">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <h5 class="mb-0 black">Service Availability ({{ $service->availabilities->count() }} slots)</h5>
                        </div>

                        <div class="availability-grid">
                            @php
                                $groupedAvailabilities = $service->availabilities->groupBy('day');
                                $daysOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                            @endphp

                            @foreach($daysOrder as $day)
                                @if(isset($groupedAvailabilities[$day]))
                                <div class="availability-day-card">
                                    <div class="day-header">
                                        <h6 class="day-name">{{ $day }}</h6>
                                        <span class="slot-count">{{ $groupedAvailabilities[$day]->count() }} slot(s)</span>
                                    </div>
                                    <div class="time-slots">
                                        @foreach($groupedAvailabilities[$day] as $availability)
                                        <div class="time-slot">
                                            <i class="fas fa-clock me-2"></i>
                                            <span class="time-range">
                                                {{ date('H:i', strtotime($availability->start_time)) }} -
                                                {{ date('H:i', strtotime($availability->end_time)) }}
                                            </span>
                                            <span class="date-badge">{{ date('M d', strtotime($availability->date)) }}</span>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Assigned Staff Section -->
            @if($service->staff && $service->staff->count() > 0)
            <div class="row g-4 mb-4">
                <div class="col-md-12">
                    <div class="staff-section-card p-4">
                        <div class="d-flex align-items-center mb-4">
                            <div class="section-icon me-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <h5 class="mb-0 black">Assigned Staff ({{ $service->staff->count() }})</h5>
                        </div>

                        <div class="row g-3">
                            @foreach($service->staff as $staff)
                            <div class="col-md-6 col-lg-4">
                                <div class="staff-card">
                                    <div class="staff-header">
                                        <div class="staff-avatar">
                                            <img src="{{ asset('website/' . $staff->image) }}"
                                                 alt="{{ $staff->name }}"
                                                 onerror="this.src='{{ asset('website/assets/images/default.png') }}'">
                                        </div>
                                        <div class="staff-info">
                                            <h6 class="staff-name">{{ $staff->name }}</h6>
                                            <p class="staff-email">{{ $staff->email }}</p>
                                        </div>
                                    </div>

                                    <div class="staff-details">
                                        <div class="staff-category">
                                            <i class="fas fa-tag"></i>
                                            {{ $staff->category->name ?? 'No Category' }}
                                        </div>

                                        @if($staff->phone)
                                        <div class="staff-phone">
                                            <i class="fas fa-phone"></i>
                                            {{ $staff->phone }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Service Description -->
            <div class="row g-4">
                <div class="col-md-12">
                    <div class="description-section-card p-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="section-icon me-3">
                                <i class="fas fa-align-left"></i>
                            </div>
                            <h5 class="mb-0 black">Service Description</h5>
                        </div>
                        <div class="description-content">
                            {{ $service->description ?? 'No description available for this service.' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
