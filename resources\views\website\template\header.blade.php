<div class="fixed-theme-header">
    <div class="discount-header text-white">
        <div class="container d-flex justify-content-between flex-wrap align-items-center m-auto py-5">
            <p class="m-0 fs-13 nunito-sans regular m-xl-0 m-md-0 m-sm-auto m-auto">
                {{ setting()->header_text ?? '' }}
            </p>
            <div class="d-flex gap-4 m-xl-0 m-md-0 m-sm-auto m-auto">
                <a href="mailto:{{ setting()->email ?? '' }}" class="fs-13 text-white regular">
                    <i class="fa-solid fa-envelope me-2"></i> {{ setting()->email ?? '' }}
                </a>
                <a href="tel:{{ setting()->phone ?? '' }}" class="fs-13 text-white regular">
                    <i class="fa-solid fa-phone me-2"></i> {{ setting()->phone ?? '' }}
                </a>
            </div>
        </div>
    </div>

    <!-- Main Navbar -->
    <nav class="navbar navbar-expand-lg bg-white header">
        <div class="container py-2">
            <!-- Logo -->
            <div class="d-flex gap-2 w-400px align-items-center">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <img src="{{ asset('website') . '/' . setting()->logo ?? '' }}" alt="Logo"
                        class="h-35px w-30px">
                    <!-- <img src="{{ asset('website') }}/assets/images/logo-image.svg" alt="Logo" class="h-45px w-45px"> -->
                </a>

                <style>
                    .search-container-header {
                        position: relative;
                    }
                    .search-header-results {
                        margin-top: 5px;
                        position: absolute;
                        top: 100%;
                        left: 0;
                        width: 100%;
                        background-color: #fff;
                        border: 1px solid #e0e0e0;
                        border-radius: 8px;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                        z-index: 1000;
                        max-height: 300px;
                        overflow-y: auto;
                    }
                    .search-header-results ul {
                        list-style: none;
                        padding: 0;
                        margin: 0;
                    }
                    .search-header-results li {
                        padding: 12px 15px;
                        border-bottom: 1px solid #f0f0f0;
                        transition: background-color 0.2s ease;
                    }
                    .search-header-results li:last-child {
                        border-bottom: none;
                    }
                    .search-header-results li:hover {
                        background-color: #f8f9fa;
                    }
                    .search-header-results li:active {
                        background-color: #e9ecef;
                    }

                </style>

                <!-- Search Bar -->
                <div class="search-container-header w-100">
                    <form class="d-flex w-100 form-control rounded-pill align-items-center header-search h-50" id="headerSearchForm">
                        <i class="fa-solid fa-magnifying-glass me-3"></i>
                        <label for="searchInputToday" class="visually-hidden">Services Looking for today</label>
                        <input class="w-100 fs-14 normal sora" type="search"
                            placeholder="What service are you looking for today?" name="searchInputToday"
                            id="searchInputToday" aria-label="Search Today" autocomplete="off">
                    </form>
                    <div class="search-header-results" id="searchResults" style="display: none;">
                        <ul id="searchResultsList">
                            <!-- Dynamic search results will be populated here -->
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Toggler for mobile -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse flex-end align-items-center navbar-header gap-4 py-lg-0 py-md-5 py-sm-7 py-7" id="mainNavbar">
                @auth
                    <div class="app-navbar-item ms-1 ">
                        <div class="btn btn-icon btn-custom position-relative btn-icon-muted btn-active-light btn-active-color-primary w-15px h-15px"
                            data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                            data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                            <i class="far fa-bell"> </i>
                            <span id="notification-counter"
                                class="notification-count">
                                0
                            </span>
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                            <span class="path4"></span>
                            </i>
                        </div>
                        <div class="menu notification-dropdown menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px p-5"
                            data-kt-menu="true" id="kt_menu_notifications">

                            <p class="fs-16 bold">Notifications</p>

                            <div id="notification-list">
                                @php
                                    $recentNotifications = auth()->check()
                                        ? \App\Models\Notification::where('user_id', auth()->id())
                                            ->with('user')
                                            ->latest()
                                            ->take(4)
                                            ->get()
                                        : collect();
                                @endphp

                                @forelse ($recentNotifications as $notification)
                                    <div class="d-flex align-items-center gap-3 justify-content-center border-bottom mb-5 pb-3"
                                        data-notification-id="{{ $notification->id }}" style="cursor: pointer;">
                                        <img src="{{ asset('website').'/'.$notification?->senderUser?->profile?->pic ?? '' }}"
                                            alt="Logo" class="h-50px h-50px"
                                            style=" object-fit: cover; border-radius: 50%;">
                                        <div>
                                            <p class="fs-12 mb-0 light-black"><span
                                                    class="semi_bold">{{ $notification->title }}</span>
                                                {{ Str::limit($notification->message, 150) }}</p>
                                            <p class="fs-12 mb-0">{{ $notification->created_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                @empty
                                    <div class="text-center py-3">
                                        <p class="fs-13 mb-0 text-muted">No notifications yet</p>
                                    </div>
                                @endforelse
                            </div>

                            <a href="{{ route('notification') }}" class="see-all-btn"> See All</a>
                        </div>
                    </div>
                    @if (auth()->user()->hasRole('customer'))
                        <div class="app-navbar-item ms-1">
                            <a href="{{ route('chats.index') }}" class="position-relative text-decoration-none">
                                <i class="far fa-envelope mt-1 text-dark"></i>
                                <span class="email-count" id="envelope-counter">
                                    0
                                </span>
                            </a>
                        </div>

                        <!-- <div lass="app-navbar-item ms-1">

                            <i class="far fa-question-circle mt-1"></i>

                        </div> -->
                        <div>
                            <a class="position-relative" href="{{ route('favorite_professional') }}"
                                class="@if (request()->is('favorite_professional')) active-fav @endif" aria-label="Favourite">
                                <i class="fa-regular fa-heart"></i>
                                <!-- <span class="wish-count">02</span> -->
                            </a>
                        </div>
                        <div>
                            <a class="position-relative" href="{{ route('cart') }}" aria-label="Add to Cart">
                                <!-- <i class="fa-solid fa-cart-shopping"></i> -->
                                <i class="bi bi-cart3"></i> <span class="cart-count">
                                    {{ session()->has('booking_cards') ? count(session('booking_cards')) : 0 }}
                                </span>
                            </a>
                        </div>
                    @endif

                    <div class="app-navbar-item ms-1 " id="kt_header_user_menu_toggle">
                        <div class="cursor-pointer symbol symbol-35px "
                            data-kt-menu-trigger="{default: 'click', lg: 'hover'}" data-kt-menu-attach="parent"
                            data-kt-menu-placement="bottom-end">
                            @if (!auth()->user()->profile || auth()->user()->profile->pic == null)
                                <img src="{{ asset('website') }}/assets/media/avatars/blank.png"
                                    onerror="this.src='{{ asset('website/assets/images/default.png') }}'"
                                    class="rounded-pill" alt="user" />
                            @else
                                <img alt="Logo" src="{{ asset('website') . '/' . auth()->user()->profile->pic }}"
                                    onerror="this.src='{{ asset('website/assets/images/default.png') }}'" />
                            @endif
                        </div>

                        <div class="menu menu-sub right-sidebar-menus menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg menu-state-color fw-semibold py-4 fs-6 w-275px"
                            data-kt-menu="true">
                            <div class="menu-item px-3">
                                <div class="menu-content d-flex align-items-center px-3">
                                    <div class="symbol symbol-50px me-5">
                                        @if (!auth()->user()->profile || auth()->user()->profile->pic == null)
                                            <img src="{{ asset('website') }}/assets/media/avatars/blank.png"
                                                class="rounded-pill" alt="user" />
                                        @else
                                            <img alt="Logo"
                                                src="{{ asset('website') . '/' . auth()->user()->profile->pic }}" onerror="this.src='{{ asset('website/assets/images/default.png') }}'"/>
                                        @endif
                                    </div>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold d-flex align-items-center fs-5">{{ Auth::user()->name ?? '' }}
                                        </div>
                                        <a href="#"
                                            class="fw-semibold deep-blue fs-7">{{ Auth::user()->email ?? '' }}</a>
                                    </div>
                                </div>
                            </div>
                            <div class="separator my-2"></div>
                            @if (auth()->check() && auth()->user()->hasAnyRole(['individual', 'business', 'admin', 'super admin']))
                                <div class="menu-item px-3">
                                    <a href="{{ route('dashboard') }}" class="menu-link px-5">Dashboard</a>
                                </div>
                            @endif
                            @if (auth()->check() && auth()->user()->hasRole('customer'))
                                <div class="menu-item px-3">
                                    <a href="{{ route('profile_setting') }}" class="menu-link px-5">Profile</a>
                                </div>
                                <div class="menu-item px-3">
                                    <a href="{{ route('customer.setting') }}" class="menu-link px-5">Settings</a>
                                </div>
                            @else
                                <div class="menu-item px-3">
                                    <a href="{{ route('setting') }}" class="menu-link px-5">Profile Setting</a>
                                </div>
                            @endif

                            <div class="separator my-2"></div>
                            <div class="menu-item px-3">
                                <a href="{{ url('logout') }}" class="menu-link px-5 logout">Logout</a>
                            </div>
                        </div>
                    </div>
                @else
                    <a href="{{ url('register') }}" class="button button1 login-btn">Register / Login</a>
                @endauth

            </div>
        </div>
    </nav>

    <div class="bg-white border-top header-items">
        <div class="container">
            <ul class="nav justify-content-start py-4 gap-lg-7 gap-md-7 gap-sm-7 gap-0">
                <li class="nav-item"><a
                        class="nav-link fs-14 noraml sora semi_bold header-active    @if (request()->is('/')) active @endif"
                        href="{{ url('/') }}">Home</a>
                </li>

                <li class="nav-item dropdown mega_menu position-static">
                    <a class="nav-link dropdown-toggle fs-14 semi_bold sora header-active  @if (request()->is('services')) active @endif"
                        href="#!" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                        id="servicesLink">
                        Services
                    </a>
                    <div class="dropdown-menu service-drop-down px-3 p-0 rounded-0 shadow-none start-0">
                        {{-- Service Category and subcategory --}}
                        <x-navbar-service-component route="website_services" type="service" />
                        {{-- Service Category and subcategory  end --}}
                    </div>
                </li>

                <li class="nav-item dropdown mega_menu position-static">
                    <a class="nav-link dropdown-toggle fs-14 header-active  semi_bold sora  @if (request()->is('professional')) active @endif"
                        href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false"
                        id="professionalLink">
                        Professional
                    </a>
                    <div class="dropdown-menu professional-drop-down px-3 p-0 rounded-0 shadow-none start-0">
                        <x-navbar-service-component route="professional" type="professional" />
                    </div>
                </li>

                @foreach ($pages ?? [] as $page)
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml sora semi_bold header-active {{ request()->routeIs('page.show') ? 'active' : '' }}"
                            href="{{ route('page.show', $page->slug) }}">{{ $page->title ?? '' }}</a>
                    </li>
                @endforeach


                @if (auth()->check() && auth()->user()->hasRole('customer'))
    <li class="nav-item">
        <a class="nav-link fs-14 noraml header-active sora semi_bold {{ Route::is('friends.index') ? 'active' : '' }}"
           href="{{ route('friends.index') }}">Friends</a>
    </li>

    {{--
    <li class="nav-item">
        <a class="nav-link fs-14 noraml header-active sora semi_bold {{ Route::is('chats.index') ? 'active' : '' }}"
           href="{{ route('chats.index') }}">Chats</a>
    </li>
    --}}

    <li class="nav-item">
        <a class="nav-link fs-14 noraml header-active sora semi_bold {{ Route::is('customer_booking') ? 'active' : '' }}"
           href="{{ route('customer_booking') }}">My Booking</a>
    </li>

    <li class="nav-item">
        <a class="nav-link fs-14 noraml header-active sora semi_bold {{ Route::is('customer_transactions') ? 'active' : '' }}"
           href="{{ route('customer_transactions') }}">Transactions</a>
    </li>
@endif


                <!-- @if (auth()->check() && auth()->user()->hasRole('customer'))
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('friends.index')) active @endif"
                            href="{{ route('friends.index') }}">Friends</a></li>
                    {{-- <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('chats*')) active @endif"
                            href="{{ route('chats.index') }}">Chats</a></li> --}}
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('customer_booking')) active @endif"
                            href="{{ route('customer_booking') }}">My Booking</a></li>
                    <li class="nav-item"><a
                            class="nav-link fs-14 noraml header-active sora semi_bold @if (request()->is('customer_transactions')) active @endif"
                            href="{{ route('customer_transactions') }}">Wallet</a></li>
                @endif -->
                @guest()
                    <li class="nav-item ms-auto"><a class="nav-link blue-text fs-14 sora semi_bold pe-0"
                            href="{{ url('register') }}">Become a professional →</a></li>
                @endguest
            </ul>
        </div>
    </div>

</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInputToday');
    const searchResults = document.getElementById('searchResults');
    const searchResultsList = document.getElementById('searchResultsList');
    let searchTimeout;

    // Handle search input
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();

        // Clear previous timeout
        clearTimeout(searchTimeout);

        if (query.length === 0) {
            hideSearchResults();
            return;
        }

        // Debounce search requests
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, 300);
    });

    // Hide results when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.search-container-header')) {
            hideSearchResults();
        }
    });

    // Prevent form submission
    document.getElementById('headerSearchForm').addEventListener('submit', function(e) {
        e.preventDefault();
    });

    function performSearch(query) {
        // Show loading state
        searchResultsList.innerHTML = '<li style="padding: 15px; text-align: center; color: #666;"><i class="fas fa-spinner fa-spin" style="margin-right: 8px;"></i>Searching...</li>';
        showSearchResults();

        // Make AJAX request
        fetch(`{{ route('header_search') }}?query=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displaySearchResults(data.services);
                } else {
                    showError('Search failed. Please try again.');
                }
            })
            .catch(error => {
                console.error('Search error:', error);
                showError('Search failed. Please try again.');
            });
    }

    function displaySearchResults(services) {
        if (services.length === 0) {
            searchResultsList.innerHTML = '<li style="padding: 15px; text-align: center; color: #666; font-style: italic;">No services found</li>';
        } else {
            const resultsHtml = services.map(service => `
                <li style="cursor: pointer;" onclick="window.location.href='${service.url}'">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="flex: 1;">
                            <div style="font-weight: 600; color: #333; margin-bottom: 4px; font-size: 14px;">${service.name}</div>
                            <div style="font-size: 12px; color: #666; margin-bottom: 2px;">${service.category}${service.subcategory ? ' > ' + service.subcategory : ''}</div>
                            ${service.professional_name ? `<div style="font-size: 11px; color: #888;">by ${service.professional_name}</div>` : ''}
                        </div>
                        <div style="color: #007bff; font-weight: 500; font-size: 13px; margin-left: 10px;">${service.price}</div>
                    </div>
                </li>
            `).join('');
            searchResultsList.innerHTML = resultsHtml;
        }
        showSearchResults();
    }

    function showError(message) {
        searchResultsList.innerHTML = `<li style="padding: 10px; text-align: center; color: #dc3545;">${message}</li>`;
        showSearchResults();
    }

    function showSearchResults() {
        searchResults.style.display = 'block';
    }

    function hideSearchResults() {
        searchResults.style.display = 'none';
    }
});
</script>
