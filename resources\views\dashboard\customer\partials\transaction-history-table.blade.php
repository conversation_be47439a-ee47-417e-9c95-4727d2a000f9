@forelse ($transactions as $transaction)
    <tr>
        <td data-label="Professional Name">
            {{ $transaction->provider->name ?? '' }}</td>
        <td data-label="Service">
            {{ $transaction->service->name ?? '' }}</td>
        <td data-label="Amount">${{ $transaction->total_amount ?? '' }}</td>
        <td data-label="Status" class="paid-status status"> <span class="status-text"> Paid </span> </td>
        <td data-label="Date">{{ $transaction->booking_date }} -
            {{ \Carbon\Carbon::createFromFormat('H:i:s', $transaction->booking_time)->format('H:i') }}</td>
    </tr>
@empty
    <tr>
        <td colspan="5" class="text-center">No Transactions Found</td>
    </tr>
@endforelse
