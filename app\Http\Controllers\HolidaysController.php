<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Holiday;
use App\Http\Requests\HolidayRequest;
use App\Models\VatManagement;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use Carbon\Carbon;

class HolidaysController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:holidays-list|holidays-create|holidays-edit|holidays-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:holidays-create', ['only' => ['create', 'store', 'import', 'downloadTemplate']]);
        $this->middleware('permission:holidays-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:holidays-delete', ['only' => ['destroy']]);
        $this->middleware('permission:holidays-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $offset = 0;
        $limit = 10;
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $dateFilter = $request->get('date');

        // Build query with filters
        $query = Holiday::query();

        // Apply search filter if provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }

        // Apply date filter if provided
        if ($dateFilter) {
            try {
                $dates = explode(' - ', $dateFilter);
                if (count($dates) == 2) {
                    // Date range filter - try multiple date formats
                    $startDateStr = trim($dates[0]);
                    $endDateStr = trim($dates[1]);

                    // Try different date formats
                    $formats = ['M d, Y', 'MMM D, YYYY', 'Y-m-d'];
                    $startDate = null;
                    $endDate = null;

                    foreach ($formats as $format) {
                        try {
                            $startDate = Carbon::createFromFormat($format, $startDateStr)->format('Y-m-d');
                            $endDate = Carbon::createFromFormat($format, $endDateStr)->format('Y-m-d');
                            break;
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if ($startDate && $endDate) {
                        $query->whereBetween('date', [$startDate, $endDate]);
                    }
                } elseif (count($dates) == 1) {
                    // Single date filter
                    $dateStr = trim($dates[0]);
                    $formats = ['M d, Y', 'MMM D, YYYY', 'Y-m-d'];
                    $date = null;

                    foreach ($formats as $format) {
                        try {
                            $date = Carbon::createFromFormat($format, $dateStr)->format('Y-m-d');
                            break;
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if ($date) {
                        $query->whereDate('date', $date);
                    }
                }
            } catch (\Exception $e) {
                // Invalid date format, ignore filter
                Log::warning('Invalid date format in holiday filter: ' . $dateFilter);
            }
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get first 10 records
        $holidays = $query->offset($offset)->limit($limit)->get();
        $countries = VatManagement::where('status', 1)->get();
        return view('dashboard.admin.holidays.holidays', [
            'holidays' => $holidays,
            'countries' => $countries,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Load more holiday records via AJAX with filtering support
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadMore(Request $request)
    {
        $offset = $request->get('offset', 0);
        $limit = 10;
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $dateFilter = $request->get('date');

        // Build query with filters
        $query = Holiday::query();

        // Apply search filter if provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }

        // Apply date filter if provided
        if ($dateFilter) {
            try {
                $dates = explode(' - ', $dateFilter);
                if (count($dates) == 2) {
                    // Date range filter - try multiple date formats
                    $startDateStr = trim($dates[0]);
                    $endDateStr = trim($dates[1]);

                    // Try different date formats
                    $formats = ['M d, Y', 'MMM D, YYYY', 'Y-m-d'];
                    $startDate = null;
                    $endDate = null;

                    foreach ($formats as $format) {
                        try {
                            $startDate = Carbon::createFromFormat($format, $startDateStr)->format('Y-m-d');
                            $endDate = Carbon::createFromFormat($format, $endDateStr)->format('Y-m-d');
                            break;
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if ($startDate && $endDate) {
                        $query->whereBetween('date', [$startDate, $endDate]);
                    }
                } elseif (count($dates) == 1) {
                    // Single date filter
                    $dateStr = trim($dates[0]);
                    $formats = ['M d, Y', 'MMM D, YYYY', 'Y-m-d'];
                    $date = null;

                    foreach ($formats as $format) {
                        try {
                            $date = Carbon::createFromFormat($format, $dateStr)->format('Y-m-d');
                            break;
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if ($date) {
                        $query->whereDate('date', $date);
                    }
                }
            } catch (\Exception $e) {
                // Invalid date format, ignore filter
                Log::warning('Invalid date format in holiday loadMore: ' . $dateFilter);
            }
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get records with offset and limit
        $holidays = $query->offset($offset)->limit($limit)->get();

        $html = view('dashboard.admin.holidays.partials.holiday-table', [
            'holidays' => $holidays
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $holidays->count(),
            'total' => $totalCount,
            'offset' => $offset,
            'next_offset' => $offset + $holidays->count(),
            'has_more' => ($offset + $limit) < $totalCount
        ]);
    }

    /**
     * Filter holidays via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filter(Request $request)
    {
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $dateFilter = $request->get('date');
        $limit = 10;

        // Build query with filters
        $query = Holiday::query();

        // Apply search filter if provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }

        // Apply date filter if provided
        if ($dateFilter) {
            try {
                $dates = explode(' - ', $dateFilter);
                if (count($dates) == 2) {
                    // Date range filter - try multiple date formats
                    $startDateStr = trim($dates[0]);
                    $endDateStr = trim($dates[1]);

                    // Try different date formats
                    $formats = ['M d, Y', 'MMM D, YYYY', 'Y-m-d'];
                    $startDate = null;
                    $endDate = null;

                    foreach ($formats as $format) {
                        try {
                            $startDate = Carbon::createFromFormat($format, $startDateStr)->format('Y-m-d');
                            $endDate = Carbon::createFromFormat($format, $endDateStr)->format('Y-m-d');
                            break;
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if ($startDate && $endDate) {
                        $query->whereBetween('date', [$startDate, $endDate]);
                    }
                } elseif (count($dates) == 1) {
                    // Single date filter
                    $dateStr = trim($dates[0]);
                    $formats = ['M d, Y', 'MMM D, YYYY', 'Y-m-d'];
                    $date = null;

                    foreach ($formats as $format) {
                        try {
                            $date = Carbon::createFromFormat($format, $dateStr)->format('Y-m-d');
                            break;
                        } catch (\Exception $e) {
                            continue;
                        }
                    }

                    if ($date) {
                        $query->whereDate('date', $date);
                    }
                }
            } catch (\Exception $e) {
                // Invalid date format, ignore filter
                Log::warning('Invalid date format in holiday filter: ' . $dateFilter);
            }
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get first 10 records
        $holidays = $query->limit($limit)->get();

        $html = view('dashboard.admin.holidays.partials.holiday-table', [
            'holidays' => $holidays
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $holidays->count(),
            'total' => $totalCount,
            'offset' => 0,
            'next_offset' => $holidays->count(),
            'has_more' => $limit < $totalCount
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('holidays.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  HolidayRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(HolidayRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'country_name' => 'required|string|max:100',
            'date' => 'required|date',
        ],[
            'name.required' => 'Please enter holiday name',
            'name.regex' => 'Only letters, numbers, and spaces are allowed.',
            'name.max' => 'Holiday name cannot exceed 100 characters.',
            'country_name.required' => 'The country is required.',
            'country_name.max' => 'Country name cannot exceed 100 characters.',
            'date.required' => 'The date is required.',
            'date.date' => 'Please enter a valid date.',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $holidayData = $validator->validated();
            Holiday::create($holidayData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Holiday Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        return view('holidays.show', ['holiday' => $holiday]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        return response()->json($holiday);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  HolidayRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(HolidayRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'country_name' => 'required|string|max:100',
            'date' => 'required|date',
        ],[
            'name.required' => 'Please enter holiday name',
            'name.regex' => 'Only letters, numbers, and spaces are allowed.',
            'name.max' => 'Holiday name cannot exceed 100 characters.',
            'country_name.required' => 'The country is required.',
            'country_name.max' => 'Country name cannot exceed 100 characters.',
            'date.required' => 'The date is required.',
            'date.date' => 'Please enter a valid date.',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $holiday = Holiday::where('ids', $id)->firstOrFail();
            $holidayData = $validator->validated();
            $holiday->update($holidayData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Holiday updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $holiday = Holiday::where('ids', $id)->firstOrFail();
        $holiday->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Holiday deleted successfully'
        ]);
    }

    /**
     * Import holidays from Excel/CSV file
     *
     * @param  Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function import(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'import_file' => 'required|file|mimes:xlsx,xls,csv|max:2048',
            'country_name' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());

            if ($request->ajax()) {
                return response()->json([
                    'type' => 'error',
                    'message' => $errors,
                    'title' => 'Validation Errors',
                ], 422);
            }

            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }

        try {
            DB::beginTransaction();

            $file = $request->file('import_file');
            $countryName = $request->country_name;
            $importedCount = 0;
            $errors = [];

            // Handle CSV files
            if ($file->getClientOriginalExtension() === 'csv') {
                $importedCount = $this->importFromCsv($file, $countryName, $errors);
            } else {
                // Handle Excel files (.xlsx, .xls)
                $importedCount = $this->importFromExcel($file, $countryName, $errors);
            }

            DB::commit();

            if (!empty($errors)) {
                $errorMessage = 'Import completed with some errors:<br>' . implode('<br>', $errors);
                $response = [
                    'type' => 'warning',
                    'title' => 'Import Completed with Warnings',
                    'message' => $errorMessage . '<br>Successfully imported: ' . $importedCount . ' holidays'
                ];

                if ($request->ajax()) {
                    return response()->json($response);
                }

                return redirect()->back()->with($response);
            }

            $response = [
                'type' => 'success',
                'title' => 'Import Successful',
                'message' => 'Successfully imported ' . $importedCount . ' holidays!'
            ];

            if ($request->ajax()) {
                return response()->json($response);
            }

            return redirect()->back()->with($response);

        } catch (\Throwable $th) {
            DB::rollback();
            $response = [
                'type' => 'error',
                'message' => 'Import failed: ' . $th->getMessage(),
                'title' => 'Import Error'
            ];

            if ($request->ajax()) {
                return response()->json($response, 500);
            }

            return redirect()->back()->with($response);
        }
    }

    /**
     * Import holidays from CSV file
     */
    private function importFromCsv($file, $countryName, &$errors)
    {
        $importedCount = 0;
        $handle = fopen($file->getRealPath(), 'r');

        if ($handle !== false) {
            $isFirstRow = true;
            while (($data = fgetcsv($handle, 1000, ',')) !== false) {
                // Skip header row
                if ($isFirstRow) {
                    $isFirstRow = false;
                    continue;
                }

                if (count($data) >= 2) {
                    $name = trim($data[0]);
                    $date = trim($data[1]);
                    $statusText = isset($data[2]) ? trim($data[2]) : 'active';

                    // Convert status text to 0/1
                    $status = $this->convertStatusToNumber($statusText);

                    if (!empty($name) && !empty($date)) {
                        // Validate and normalize date format
                        $normalizedDate = $this->validateAndNormalizeDate($date);
                        if ($normalizedDate) {
                            // Check if holiday already exists
                            $existingHoliday = Holiday::where('name', $name)
                                                   ->where('date', $normalizedDate)
                                                   ->where('country_name', $countryName)
                                                   ->first();

                            if (!$existingHoliday) {
                                Holiday::create([
                                    'name' => $name,
                                    'date' => $normalizedDate,
                                    'country_name' => $countryName,
                                    'status' => $status
                                ]);
                                $importedCount++;
                            } else {
                                $errors[] = "Holiday '{$name}' on {$normalizedDate} already exists - skipped";
                            }
                        } else {
                            $errors[] = "Invalid date format for '{$name}': {$date} - skipped";
                        }
                    }
                }
            }
            fclose($handle);
        }

        return $importedCount;
    }

    /**
     * Import holidays from Excel file using Laravel Excel
     */
    private function importFromExcel($file, $countryName, &$errors)
    {
        $importedCount = 0;

        try {
            $data = Excel::toArray([], $file);

            if (empty($data) || empty($data[0])) {
                $errors[] = "Excel file is empty or could not be read";
                return 0;
            }

            $rows = $data[0]; // Get first sheet
            $isFirstRow = true;

            foreach ($rows as $rowIndex => $row) {
                // Skip header row
                if ($isFirstRow) {
                    $isFirstRow = false;
                    continue;
                }

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                $name = isset($row[0]) ? trim($row[0]) : '';
                $date = isset($row[1]) ? trim($row[1]) : '';
                $statusText = isset($row[2]) ? trim($row[2]) : 'active';
                $status = $this->convertStatusToNumber($statusText);

                if (!empty($name) && !empty($date)) {
                    $normalizedDate = $this->validateAndNormalizeDate($date);
                    if ($normalizedDate) {
                        // Check if holiday already exists
                        $existingHoliday = Holiday::where('name', $name)
                            ->where('date', $normalizedDate)
                            ->where('country_name', $countryName)
                            ->first();

                        if (!$existingHoliday) {
                            Holiday::create([
                                'name' => $name,
                                'date' => $normalizedDate,
                                'country_name' => $countryName,
                                'status' => $status
                            ]);
                            $importedCount++;
                        } else {
                            $errors[] = "Holiday '{$name}' on {$normalizedDate} already exists - skipped";
                        }
                    } else {
                        $errors[] = "Invalid date format for '{$name}': {$date} - skipped (row " . ($rowIndex + 1) . ")";
                    }
                } else {
                    $errors[] = "Missing name or date in row " . ($rowIndex + 1) . " - skipped";
                }
            }

        } catch (\Exception $e) {
            $errors[] = "Error reading Excel file: " . $e->getMessage();
        }

        return $importedCount;
    }

    /**
     * Validate and normalize date format to Y-m-d
     */
    private function validateAndNormalizeDate($date)
    {
        // More flexible date formats including single digit dates
        $formats = [
            'Y-m-d', 'Y/m/d', 'd-m-Y', 'd/m/Y', 'm-d-Y', 'm/d/Y',
            'n/j/Y', 'j/n/Y', 'Y/n/j', 'Y-n-j', 'n-j-Y', 'j-n-Y'
        ];

        foreach ($formats as $format) {
            $dateTime = \DateTime::createFromFormat($format, $date);
            if ($dateTime && $dateTime->format($format) == $date) {
                // Return normalized date in Y-m-d format
                return $dateTime->format('Y-m-d');
            }
        }

        // Try to parse common date formats more flexibly
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }

        return false;
    }

    /**
     * Convert status text to number (0 = inactive, 1 = active)
     */
    private function convertStatusToNumber($statusText)
    {
        $statusText = strtolower(trim($statusText));

        // Handle various active status formats
        if (in_array($statusText, ['active', '1', 'yes', 'true', 'enabled', 'on'])) {
            return 1;
        }

        // Handle various inactive status formats
        if (in_array($statusText, ['inactive', '0', 'no', 'false', 'disabled', 'off'])) {
            return 0;
        }

        // Default to active if unclear
        return 1;
    }

    /**
     * Get calendar data for holidays
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function calendarData(Request $request)
    {
        try {
            $start = $request->get('start');
            $end = $request->get('end');
            $filter = $request->get('filter');

            $query = Holiday::query();

            // Apply simple filter if provided
            if ($filter) {
                $today = Carbon::today();

                switch ($filter) {
                    case 'today':
                        $query->whereDate('date', $today);
                        break;

                    case 'this_month':
                        $query->whereYear('date', $today->year)
                              ->whereMonth('date', $today->month);
                        break;

                    case 'this_year':
                        $query->whereYear('date', $today->year);
                        break;
                }
            } else {
                // Filter by date range if provided (fallback for FullCalendar's default behavior)
                if ($start && $end) {
                    $query->whereBetween('date', [$start, $end]);
                }
            }

            // Only get active holidays
            $query->where('status', '1');

            $holidays = $query->get();

            $events = [];
            foreach ($holidays as $holiday) {
                $events[] = [
                    'id' => $holiday->id,
                    'title' => $holiday->name,
                    'start' => $holiday->date,
                    'allDay' => true,
                    'backgroundColor' => '#dc3545', // Red color for holidays
                    'borderColor' => '#dc3545',
                    'textColor' => '#ffffff',
                    'extendedProps' => [
                        'type' => 'holiday',
                        'country_name' => $holiday->country_name,
                        'status' => $holiday->status
                    ]
                ];
            }

            return response()->json($events);
        } catch (\Exception $e) {
            Log::error('Error fetching holiday calendar data: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to fetch calendar data'], 500);
        }
    }

    /**
     * Download sample template for import
     */
    public function downloadTemplate()
    {
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="holidays_template.csv"',
        ];

        $callback = function() {
            $file = fopen('php://output', 'w');

            // Add CSV headers
            fputcsv($file, ['Holiday Name', 'Date (YYYY-MM-DD)', 'Status (1=active, 0=inactive)']);

            // Add sample data
            fputcsv($file, ['New Year Day', '2024-01-01', '1']);
            fputcsv($file, ['Independence Day', '2024-07-04', '1']);
            fputcsv($file, ['Christmas Day', '2024-12-25', '1']);

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
