<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\ProfessionalRegistrationProgress;

class UserCheckMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = auth()->user();
        if (!auth()->check()) {
            return $next($request);
        }

        if ($request->is('/')) {
            return $next($request);
        }

        if (auth()->check()) {
            if (auth()->user()->hasRole('professional')) {
                $message = '';
                $title = '';
                $hasIssue = false;

                if ($user->approval == 0) {
                    $title = 'Account Not Approved';
                    $message = 'Your account is not yet approved. Please wait for approval.';
                    $hasIssue = true;
                }
                if ($user->status == 0) {
                    $title = 'Account Not Active';
                    $message = 'Your account is not active. Please contact support.';
                    $hasIssue = true;
                }
                if ($user->registration_completed == 0 && !$request->is('register/*')) {
                    return redirect()->route('register.user_type', 'professional');
                }

                if ($hasIssue) {
                    auth()->logout();
                    session()->flush();
                    return redirect()->route('home')->with([
                        'title' => $title,
                        'message' => $message,
                        'type' => 'error',
                    ]);
                }
            }

            if (auth()->user()->hasRole('customer')) {
                $message = '';
                $title = '';
                $hasIssue = false;

                // Allow access to home route and registration routes even with incomplete registration
                if ($user->registration_completed == 0 && !$request->is('/') && !$request->is('register/*')) {
                    return redirect()->route('register.user_type', 'customer');
                }

                if ($user->status == 0) {
                    $title = 'Account Not Active';
                    $message = 'Your account is not active. Please contact support.';
                    $hasIssue = true;
                }

                if ($hasIssue) {
                    auth()->logout();
                    session()->flush();
                    return redirect()->route('home')->with([
                        'title' => $title,
                        'message' => $message,
                        'type' => 'error',
                    ]);
                }
            }

            if (auth()->user()->hasAnyRole(['admin', 'super admin'])) {
                $message = '';
                $title = '';
                $hasIssue = false;

                if ($user->status == 0) {
                    $title = 'Account Not Active';
                    $message = 'Your admin account is not active. Please contact the system administrator.';
                    $hasIssue = true;
                }

                if ($hasIssue) {
                    auth()->logout();
                    session()->flush();
                    return redirect()->route('login')->with([
                        'title' => $title,
                        'message' => $message,
                        'type' => 'error',
                    ]);
                }
            }
        }
        return $next($request);
    }
}
