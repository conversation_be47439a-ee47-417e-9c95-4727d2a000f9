<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class FriendAddRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        if (!auth()->check()) {
            return false;
        }

        // Simple check - you can adjust this based on your role implementation
        return true; // Allow all authenticated users for now
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        $rules = [
            'role' => 'required|in:under-13,above-13',
        ];

        if ($this->role == "under-13") {
            $rules = array_merge($rules, [
                "profile_pic" => $this->isMethod('post') ? "required|mimes:png,jpg,jpeg,gif,webp|max:5048" : "nullable|mimes:png,jpg,jpeg,gif,webp|max:5048",
                "name" => "required|string|max:100|regex:/^[a-zA-Z\s]+$/",
                "relationship_under13" => "required|string|max:50|regex:/^[a-zA-Z\s]+$/",
                "service_preferences" => "nullable|array",
                "service_preferences.*" => "exists:services,id",
            ]);
        }

        if ($this->role == "above-13") {
            $rules = array_merge($rules, [
                "friend_email" => [
                    "required",
                    "email",
                    function ($attribute, $value, $fail) {
                        // Check if user is trying to add themselves as friend
                        if ($value == auth()->user()->email) {
                            $fail('You cannot add yourself as a friend.');
                        }
                    }
                ],
                "relationship_above13" => "required|string|max:50|regex:/^[a-zA-Z\s]+$/",
            ]);
        }

        return $rules;
    }

    public function messages(): array
    {
        return [
            'role.required' => 'Please select an age category.',
            'role.in' => 'Invalid age category selected.',
            'profile_pic.required' => 'Profile picture is required for under-13 friends.',
            'profile_pic.mimes' => 'Profile picture must be a valid image file (png, jpg, jpeg, gif, webp).',
            'profile_pic.max' => 'Profile picture size must not exceed 5MB.',
            'name.required' => 'Name is required.',
            'name.regex' => 'Name can only contain letters and spaces.',
            'relationship.required' => 'Relationship is required.',
            'relationship.regex' => 'Relationship can only contain letters and spaces.',
            'friend_user_id.required' => 'Please select a user.',
            'friend_user_id.exists' => 'Selected user does not exist.',
            'service_preferences.*.exists' => 'One or more selected services are invalid.',
        ];
    }
}
