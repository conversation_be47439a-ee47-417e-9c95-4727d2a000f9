<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\BookingService;
use App\Models\Service;
use App\Models\Staff;
use App\Models\Subscription;
use App\Models\User;
use App\Models\UserSubscription;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Stripe\Customer;
use Stripe\Stripe;
use Stripe\StripeClient;
use Stripe\Webhook;

class StripeController extends Controller
{
    protected $stripe;

    public function __construct()
    {
        $secretKey = config('services.stripe.secret_key');
        if ($secretKey) {
            Stripe::setApiKey($secretKey);
            $this->stripe = new StripeClient($secretKey);
        }
    }

    public function purchaseSubscription(Request $req)
    {
        $subscription_id = $req->subscription_id;
        $user = auth()->user();

        $subscription = Subscription::where("id", $subscription_id)->first();

        if (!$subscription) {
            return redirect()->to_route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Subscription not found!'
            ]);
        }

        $existingSubscription = UserSubscription::where('user_id', $user->id)->where('status', 1)->first();

        if ($existingSubscription && $existingSubscription->subscription_id == $subscription_id) {
            return redirect()->back()->with([
                'title' => 'Already Subscribed',
                'type' => 'info',
                'message' => 'You are already subscribed to this plan.'
            ]);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret_key'));

            // If user has existing subscription, handle upgrade/downgrade
            if ($existingSubscription && $existingSubscription->stripe_subscription_id) {
                $isUpgrade = $subscription->price > $existingSubscription->subscription_price;
                
                if ($isUpgrade) {
                    // Handle upgrade - immediate change with prorated billing
                    return $this->handleUpgrade($user, $subscription, $existingSubscription);
                } else {
                    // Handle downgrade - schedule for next billing cycle
                    return $this->handleDowngrade($user, $subscription, $existingSubscription);
                }
            }

            // Get or create Stripe customer
            if ($user->customer_id) {
                try {
                    $stripeCustomer = \Stripe\Customer::retrieve($user->customer_id);
                } catch (\Exception $e) {
                    $stripeCustomer = \Stripe\Customer::create([
                        'email' => $user->email,
                        'name' => $user->name,
                        'metadata' => [
                            'user_id' => $user->id
                        ]
                    ]);
                    $user->customer_id = $stripeCustomer->id;
                    $user->save();
                }
            } else {
                $stripeCustomer = \Stripe\Customer::create([
                    'email' => $user->email,
                    'name' => $user->name,
                    'metadata' => [
                        'user_id' => $user->id
                    ]
                ]);
                $user->customer_id = $stripeCustomer->id;
                $user->save();
            }

            $checkoutSession = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'line_items' => [
                    [
                        'price' => $subscription->pricing_id,
                        'quantity' => 1,
                    ],
                ],
                'mode' => 'subscription',
                'customer' => $stripeCustomer->id,
                'allow_promotion_codes' => true,
                'success_url' => route('subscription.success'),
                'cancel_url' => route('subscription.failed'),
            ]);

            session()->put([
                'checkout_id' => $checkoutSession->id,
            ]);

            return redirect($checkoutSession->url);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Failed to process subscription: ' . $e->getMessage()
            ]);
        }
    }

    public function subscriptionSuccess()
    {
        $checkout_id = session('checkout_id');
        try {
            $session = \Stripe\Checkout\Session::retrieve($checkout_id);

            if ($session->payment_status === 'paid') {
                return redirect()->route('subscriptions.index')->with([
                    'title' => 'Subscribed Successfully',
                    'type' => 'success',
                    'message' => 'Plan subscribed successfully'
                ]);
            }

            return redirect()->route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Payment failed or was not completed.'
            ]);
        } catch (\Exception $e) {
            return redirect()->route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Error processing payment: ' . $e->getMessage()
            ]);
        }
    }

    public function handleStripeWebhook(Request $request)
    {
        $secretKey = config('services.stripe.secret_key');
        if (!$secretKey) {
            return response()->json(['error' => 'Configuration error'], 500);
        }

        Stripe::setApiKey($secretKey);
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = config('services.stripe.webhook_secret');

        try {
            $event = Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
            switch ($event->type) {
                case 'invoice.payment_succeeded':
                    $invoice = $event->data->object;

                    // Get subscription ID from the correct location
                    $subscriptionId = $invoice['parent']['subscription_details']['subscription'] ?? null;
                    $customerId = $invoice['customer'] ?? null;

                    // Handle based on billing reason
                    if (in_array($invoice['billing_reason'], ['subscription_create', 'subscription_update'])) {
                        // Check if this is a subscription update that was already processed locally
                        if ($invoice['billing_reason'] === 'subscription_update') {
                            // For subscription updates, check if we already have an active subscription with this ID
                            $existingActiveSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)
                                ->where('status', 1)
                                ->first();

                            if ($existingActiveSubscription) {
                                // Already processed locally, just send notification and skip
                                $user = User::where('customer_id', $customerId)->first();
                                if ($user) {
                                    $actualAmountPaid = $invoice['amount_paid'] / 100;
                                    if ($actualAmountPaid > 0) {
                                        $this->user_notification($user->id, 'Payment Successful', "Payment of $" . number_format($actualAmountPaid, 2) . " processed successfully for your subscription upgrade!", $user->id, 'subscription');
                                    }
                                }
                                break;
                            }
                        }

                        // New subscription or subscription change that hasn't been processed
                        $this->subscriptionSucceeded($subscriptionId, $customerId, $invoice);
                    } elseif ($invoice['billing_reason'] === 'subscription_cycle') {
                        // Recurring billing - create new cycle record
                        $this->handleRecurringBilling($subscriptionId, $customerId, $invoice);
                    }
                    break;

                case 'customer.subscription.updated':
                    // Skip this webhook to avoid double processing
                    // All subscription handling is done via invoice.payment_succeeded
                    break;

                case 'customer.subscription.deleted':
                    $subscription = $event->data->object;
                    $userId = $subscription->customer;
                    $this->subscriptionCancelled($subscription, $userId);
                    break;

                case 'invoice.payment_failed':
                    $invoice = $event->data->object;
                    $customerId = $invoice['customer'];
                    $subscriptionId = $invoice['subscription'] ?? null;
                    $this->paymentFailed($invoice, $customerId, $subscriptionId);
                    break;

                case 'customer.subscription.created':
                    break;

                default:
                    return response()->json(['status' => 'ok']);
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Webhook Error: ' . $e->getMessage() . $e->getLine()], 400);
            return response()->json(['error' => 'Webhook Error: ' . $e->getMessage() . $e->getLine()], 400);
        }
    }

    protected function subscriptionSucceeded($subscriptionId, $customerId, $invoice)
    {
        $user = User::where('customer_id', $customerId)->first();
        if (!$user) {
            return;
        }

        // Check if there's already an active subscription with this stripe_subscription_id to prevent duplicates
        $existingActiveSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)
            ->where('status', 1)
            ->first();

        if ($existingActiveSubscription) {
            return; // Already have an active subscription with this ID
        }
        $existingSubscription = UserSubscription::where('user_id', $user->id)->where('status', 1)->first();

        // Get the price ID from the correct location in the invoice
        $priceId = $invoice['lines']['data'][0]['pricing']['price_details']['price'] ?? null;

        if (!$priceId) {
            return;
        }

        $subscription = Subscription::where('pricing_id', $priceId)->first();
        if (!$subscription) {
            return;
        }


        $currentRole = $user->roles->first();
        $currentRoleName = $currentRole ? $currentRole->name : null;
        $newRoleName = $subscription->type;
        if ($existingSubscription) {
            $existingSubscription->status = 0;
            $existingSubscription->save();
        }
        $role = Role::where("name", $subscription->type)->first();
        if ($role) {
            $user->syncRoles([$role->name]);
            $this->handleRoleTransition($user, $currentRoleName, $newRoleName);
        }
        $newSubscription = new UserSubscription;
        $newSubscription->user_id = $user->id;
        $newSubscription->subscription_id = $subscription->id;
        $newSubscription->subscription_type = $subscription->type;
        $newSubscription->subscription_price = $subscription->price;
        $newSubscription->subscription_booking_fee = $subscription->booking_fee;
        $newSubscription->subscription_total_service = $subscription->total_service;
        $newSubscription->stripe_subscription_id = $subscriptionId;
        $newSubscription->status = 1;
        $newSubscription->start_date = Carbon::now();
        $newSubscription->end_date = Carbon::now()->addMonth();
        $newSubscription->save();
        $actualAmountPaid = $invoice['amount_paid'] / 100;
        if ($existingSubscription) {
            $isUpgrade = $subscription->price > $existingSubscription->subscription_price;
            $changeType = $isUpgrade ? 'upgraded' : 'downgraded';
            // For subscription changes, show the actual prorated amount paid
            if ($actualAmountPaid > 0) {
                $this->user_notification($user->id, 'Payment Successful', "You have successfully paid $" . number_format($actualAmountPaid, 2) . " for {$changeType} to {$subscription->type} {$subscription->name}!", $user->id, 'subscription');
            } else {
                // If no payment was required (downgrade with credit)
                $this->user_notification($user->id, 'Subscription Updated', "You have successfully {$changeType} to {$subscription->type} {$subscription->name}!", $user->id, 'subscription');
            }
            $this->notifyAdmins('User Subscription Updated', "{$user->name} has {$changeType} to {$subscription->type} {$subscription->name}", $user->id, 'subscription');
        } else {
            // For new subscriptions, show the full amount paid
            $this->user_notification($user->id, 'Payment Successful', "You have successfully paid $" . number_format($actualAmountPaid, 2) . " for subscription to {$subscription->type} {$subscription->name}!", $user->id, 'subscription');
            $this->notifyAdmins('New Subscription', "{$user->name} has subscribed to {$subscription->type} {$subscription->name}", $user->id, 'subscription');
        }
    }

    protected function handleRecurringBilling($subscriptionId, $customerId, $invoice)
    {
        $user = User::where('customer_id', $customerId)->first();
        if (!$user) {
            return;
        }

        $currentUserSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)
            ->where('status', 1)
            ->first();

        if (!$currentUserSubscription) {
            return;
        }

        // Get the price ID from the invoice
        $priceId = $invoice['lines']['data'][0]['pricing']['price_details']['price'] ?? null;
        $subscriptionPlan = Subscription::where('pricing_id', $priceId)->first();

        if (!$subscriptionPlan) {
            return;
        }

        // Mark current subscription as inactive
        $currentUserSubscription->status = 0;
        $currentUserSubscription->save();

        // Create new subscription record for the new billing cycle
        $newUserSubscription = new UserSubscription();
        $newUserSubscription->user_id = $user->id;
        $newUserSubscription->subscription_id = $subscriptionPlan->id;
        $newUserSubscription->subscription_type = $subscriptionPlan->type;
        $newUserSubscription->subscription_name = $subscriptionPlan->name;
        $newUserSubscription->subscription_price = $subscriptionPlan->price;
        $newUserSubscription->subscription_booking_fee = $subscriptionPlan->booking_fee;
        $newUserSubscription->subscription_total_service = $subscriptionPlan->total_service;
        $newUserSubscription->stripe_subscription_id = $subscriptionId;
        $newUserSubscription->status = 1;
        $newUserSubscription->start_date = Carbon::now();
        $newUserSubscription->end_date = Carbon::now()->addMonth();
        $newUserSubscription->save();

        // Send renewal notification
        $actualAmountPaid = $invoice['amount_paid'] / 100;
        $this->user_notification(
            $user->id,
            'Subscription Renewed',
            "Your {$subscriptionPlan->type} {$subscriptionPlan->name} subscription has been renewed! Amount paid: $" . number_format($actualAmountPaid, 2),
            $user->id,
            'subscription'
        );

        // Notify admin
        $this->notifyAdmins(
            'Subscription Renewed',
            "{$user->name}'s {$subscriptionPlan->type} {$subscriptionPlan->name} subscription has been renewed",
            $user->id,
            'subscription'
        );
    }



    protected function subscriptionCancelled($subscription, $customerId)
    {
        $subscriptionRecord = UserSubscription::where('stripe_subscription_id', $subscription->id)->first();
        if ($subscriptionRecord) {
            $subscriptionRecord->status = 0;
            $subscriptionRecord->save();

            $user = User::find($subscriptionRecord->user_id);
            if ($user) {
                $this->user_notification($user->id, 'Subscription cancelled', 'Your subscription has been cancelled.', $user->id, 'subscription');
            }
        }
    }

    public function cancel_subscription(Request $req)
    {
        $user = auth()->user();
        $stripe_subscription_id = $req->stripe_subscription_id;
        try {
            $subscription = \Stripe\Subscription::retrieve($stripe_subscription_id);
            $subscriptionRecord = UserSubscription::where('stripe_subscription_id', $stripe_subscription_id)->first();
            if ($subscriptionRecord) {
                $subscriptionRecord->status = 0;
                $subscriptionRecord->save();
            }
            $user->syncRoles(['professional']);
            if (!$subscription || $subscription->status !== 'active') {
                return redirect()->route('subscriptions.index')->with(['title' => 'Error', 'type' => 'error', 'message' => 'Subscription not found or not active!']);
            }
            $subscription->cancel();
            return redirect()->route('subscriptions.index')->with(['title' => 'Success', 'type' => 'success', 'message' => 'Subscription canceled successfully!']);
        } catch (\Exception $e) {
            return redirect()->route('subscriptions.index')->with(['title' => 'Error', 'type' => 'error', 'message' => 'Error canceling subscription: ' . $e->getMessage()]);
        }
    }

    protected function paymentFailed($invoice, $customerId, $subscriptionId)
    {
        $user = User::where('customer_id', $customerId)->first();

        if ($user && $subscriptionId) {
            // Check if this is the final attempt (Stripe usually tries 3-4 times)
            $attemptCount = $invoice['attempt_count'] ?? 1;

            if ($attemptCount >= 3) {
                // Final failure - downgrade user immediately
                $userSubscription = UserSubscription::where('stripe_subscription_id', $subscriptionId)
                    ->where('status', 1)
                    ->first();

                if ($userSubscription) {
                    // Mark subscription as expired
                    $userSubscription->status = 0;
                    $userSubscription->save();

                    // Switch user role to professional (free tier)
                    $user->syncRoles(['professional']);

                    // Notify user about downgrade
                    $this->user_notification(
                        $user->id,
                        'Subscription Cancelled',
                        'Your subscription has been cancelled due to payment failure. You have been moved to the free professional plan.',
                        null,
                        'subscription_cancelled'
                    );

                    // Notify admin
                    $this->notifyAdmins(
                        'User Subscription Failed',
                        "{$user->name}'s subscription payment failed and they have been downgraded to free plan.",
                        $user->id,
                        'subscription_cancelled'
                    );
                }
            } else {
                // Early failure attempts - just notify
                $this->user_notification(
                    $user->id,
                    'Payment Failed',
                    "Your subscription payment failed (attempt {$attemptCount}). Please update your payment method to avoid service interruption.",
                    null,
                    'payment_failed'
                );
            }
        }
    }

    public function subscriptionFailed()
    {
        return redirect()->route('subscriptions.index');
    }



    protected function handleRoleTransition($user, $currentRoleName, $newRoleName)
    {
        // Skip if no role change or if roles are the same
        if ($currentRoleName === $newRoleName) {
            return;
        }

        // Individual to Business: Activate services (no staff changes needed)
        if ($currentRoleName === 'individual' && $newRoleName === 'business') {
            Service::where('user_id', $user->id)->update(['status' => 0]);
            $staff = Staff::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'email' => $user->email
                ],
                [
                    'name' => $user->name,
                    'status' => 1
                ]
            );
            $bookings = Booking::where('provider_id', $user->id)->where('status', 0)->get();
            if ($bookings) {
                foreach ($bookings as $booking) {
                    BookingService::create([
                        'booking_id' => $booking->id,
                        'service_id' => $booking->service_id,
                        'staff_id' => $staff->id,
                        'user_id' => $user->id,
                    ]);
                }
            }
        }

        // Business to Individual: Deactivate services and staff
        elseif ($currentRoleName === 'business' && $newRoleName === 'individual') {
            Service::where('user_id', $user->id)->update(['status' => 0]);
            Staff::where('user_id', $user->id)->update(['status' => 0]);
            $staff = Staff::firstOrCreate(
                [
                    'user_id' => $user->id,
                    'email' => $user->email
                ],
                [
                    'name' => $user->name,
                    'status' => 1
                ]
            );
            $bookings = Booking::where('provider_id', $user->id)->where('status', 0)->get();
            if ($bookings) {
                foreach ($bookings as $booking) {
                    BookingService::updateOrCreate(
                        [
                            'booking_id' => $booking->id,
                        ],
                        [
                            'service_id' => $booking->service_id,
                            'staff_id' => $staff->id,
                            'user_id' => $user->id,
                        ]
                    );
                }
            }
        }
    }

    /**
     * Update payment method for existing customer
     */
    public function updatePaymentMethod(Request $request)
    {
        $user = auth()->user();
        
        if (!$user->customer_id) {
            return redirect()->back()->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'No customer account found. Please contact support.'
            ]);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret_key'));

            // Create setup session for updating payment method
            $setupSession = \Stripe\Checkout\Session::create([
                'payment_method_types' => ['card'],
                'mode' => 'setup',
                'customer' => $user->customer_id,
                'success_url' => route('payment.method.success'),
                'cancel_url' => route('payment.method.cancel'),
            ]);

            session()->put([
                'setup_session_id' => $setupSession->id,
            ]);

            return redirect($setupSession->url);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Failed to update payment method: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Handle successful payment method update
     */
    public function paymentMethodSuccess()
    {
        $setupSessionId = session('setup_session_id');
        
        try {
            $session = \Stripe\Checkout\Session::retrieve($setupSessionId);
            
            if ($session->setup_intent) {
                $setupIntent = \Stripe\SetupIntent::retrieve($session->setup_intent);
                
                if ($setupIntent->status === 'succeeded') {
                    // Payment method has been successfully attached to customer
                    return redirect()->route('subscriptions.index')->with([
                        'title' => 'Payment Method Updated',
                        'type' => 'success',
                        'message' => 'Your payment method has been updated successfully. All future payments will be charged to this new card.'
                    ]);
                }
            }

            return redirect()->route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Payment method update was not completed.'
            ]);
        } catch (\Exception $e) {
            return redirect()->route('subscriptions.index')->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Error processing payment method update: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Handle cancelled payment method update
     */
    public function paymentMethodCancel()
    {
        return redirect()->route('subscriptions.index')->with([
            'title' => 'Cancelled',
            'type' => 'info',
            'message' => 'Payment method update was cancelled.'
        ]);
    }

    /**
     * Get current payment method information
     */
    public function getCurrentPaymentMethod()
    {
        $user = auth()->user();
        
        if (!$user->customer_id) {
            return response()->json(['error' => 'No customer account found'], 404);
        }

        try {
            Stripe::setApiKey(config('services.stripe.secret_key'));
            
            $customer = \Stripe\Customer::retrieve($user->customer_id);
            $defaultPaymentMethod = null;
            
            if ($customer->invoice_settings->default_payment_method) {
                $defaultPaymentMethod = \Stripe\PaymentMethod::retrieve(
                    $customer->invoice_settings->default_payment_method
                );
            }

            return response()->json([
                'has_payment_method' => $defaultPaymentMethod !== null,
                'payment_method' => $defaultPaymentMethod ? [
                    'type' => $defaultPaymentMethod->type,
                    'card' => $defaultPaymentMethod->card ? [
                        'brand' => $defaultPaymentMethod->card->brand,
                        'last4' => $defaultPaymentMethod->card->last4,
                        'exp_month' => $defaultPaymentMethod->card->exp_month,
                        'exp_year' => $defaultPaymentMethod->card->exp_year,
                    ] : null
                ] : null
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Failed to retrieve payment method: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Handle subscription upgrade - immediate change with prorated billing
     */
    protected function handleUpgrade($user, $subscription, $existingSubscription)
    {
        try {
            $stripeSubscription = \Stripe\Subscription::retrieve($existingSubscription->stripe_subscription_id);
            $subscriptionItemId = $stripeSubscription->items->data[0]->id;

            \Stripe\Subscription::update(
                $existingSubscription->stripe_subscription_id,
                [
                    'items' => [
                        [
                            'id' => $subscriptionItemId,
                            'price' => $subscription->pricing_id,
                        ],
                    ],
                    'proration_behavior' => 'always_invoice',
                ]
            );

            // Update database immediately for instant UX
            $existingSubscription->status = 0;
            $existingSubscription->save();

            // Create new subscription record
            $newSubscription = new UserSubscription;
            $newSubscription->user_id = $user->id;
            $newSubscription->subscription_id = $subscription->id;
            $newSubscription->subscription_type = $subscription->type;
            $newSubscription->subscription_name = $subscription->name;
            $newSubscription->subscription_price = $subscription->price;
            $newSubscription->subscription_booking_fee = $subscription->booking_fee;
            $newSubscription->subscription_total_service = $subscription->total_service;
            $newSubscription->stripe_subscription_id = $existingSubscription->stripe_subscription_id;
            $newSubscription->status = 1;
            $newSubscription->start_date = Carbon::now();
            $newSubscription->end_date = Carbon::now()->addMonth();
            $newSubscription->save();

            // Update user role
            $role = Role::where("name", $subscription->type)->first();
            if ($role) {
                $user->syncRoles([$role->name]);
            }

            return redirect()->back()->with([
                'title' => 'Upgrade Successful',
                'type' => 'success',
                'message' => "Subscription upgraded successfully! You've been charged the prorated difference."
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Failed to upgrade subscription: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Handle subscription downgrade - schedule for next billing cycle
     */
    protected function handleDowngrade($user, $subscription, $existingSubscription)
    {
        try {
            $stripeSubscription = \Stripe\Subscription::retrieve($existingSubscription->stripe_subscription_id);
            $subscriptionItemId = $stripeSubscription->items->data[0]->id;

            // Schedule the downgrade for the next billing cycle
            \Stripe\Subscription::update(
                $existingSubscription->stripe_subscription_id,
                [
                    'items' => [
                        [
                            'id' => $subscriptionItemId,
                            'price' => $subscription->pricing_id,
                        ],
                    ],
                    'proration_behavior' => 'none', // No proration - full amount charged
                    'billing_cycle_anchor' => 'unchanged', // Keep current billing cycle
                ]
            );

            // Update database immediately for instant UX
            $existingSubscription->status = 0;
            $existingSubscription->save();

            // Create new subscription record
            $newSubscription = new UserSubscription;
            $newSubscription->user_id = $user->id;
            $newSubscription->subscription_id = $subscription->id;
            $newSubscription->subscription_type = $subscription->type;
            $newSubscription->subscription_name = $subscription->name;
            $newSubscription->subscription_price = $subscription->price;
            $newSubscription->subscription_booking_fee = $subscription->booking_fee;
            $newSubscription->subscription_total_service = $subscription->total_service;
            $newSubscription->stripe_subscription_id = $existingSubscription->stripe_subscription_id;
            $newSubscription->status = 1;
            $newSubscription->start_date = Carbon::now();
            $newSubscription->end_date = Carbon::now()->addMonth();
            $newSubscription->save();

            // Update user role
            $role = Role::where("name", $subscription->type)->first();
            if ($role) {
                $user->syncRoles([$role->name]);
            }

            return redirect()->back()->with([
                'title' => 'Downgrade Scheduled',
                'type' => 'success',
                'message' => "Subscription downgrade scheduled! You've been charged the full amount for the current billing period. The downgrade will take effect at your next billing cycle."
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'title' => 'Error',
                'type' => 'error',
                'message' => 'Failed to downgrade subscription: ' . $e->getMessage()
            ]);
        }
    }
}
