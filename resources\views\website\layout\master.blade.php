<!DOCTYPE html>
<html lang="en">

<head>
    <base href="" />
    <title>{{ setting()->title ?? '' }}</title>
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="{{ setting()->title ?? '' }}" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="{{ setting()->title ?? '' }}" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="{{ asset('website') . '/' . setting()->favicon ?? '' }}" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    {{--
    <link href="{{ asset('website') }}/assets/css/style.bundle.css" rel="stylesheet" type="text/css" /> --}}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" />
    <link href="{{ asset('website') }}/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/rangeslider.js/2.3.3/rangeslider.css">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css" />
    <!-- International Telephone Input CSS -->
    <link href="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/css/intlTelInput.min.css" rel="stylesheet">
    <!-- Dashboard CSS for professional profile and other dashboard-specific styling -->
    <link href="{{ asset('website') }}/assets/css/dashboard.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('website') }}/assets/css/dashboard-responsive.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('website') }}/assets/css/style.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('website') }}/assets/css/responsive.css" rel="stylesheet" type="text/css" />
    @stack('css')
</head>

<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu"
    class="bg-body position-relative app-blank @if (auth()->check() && auth()->user()->hasRole('customer')) customer_dashboard @endif">
    <div id="secLoader">
        <div class="logo-loader">
            <div class="logo-container circle">
                <!-- <div class="circle">
                    <img src="{{ asset('website') . '/' . setting()->loader_logo }}" class="h-100 w-100 object-fit-contain"
                        alt="logo">
                </div> -->
                <img src="{{ asset('website') }}/assets/images/header_primary.svg" class="h-200 w-200 object-fit-contain"
                    alt="logo">
            </div>
        </div>
    </div>
    <div class="d-flex flex-column flex-root" id="kt_app_root">

        @include('website.template.header')
        @yield('content')
        @include('website.template.footer')

    </div>
    <!-- <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="fa-solid fa-chevron-up">
            <span class="path1"></span>
            <span class="path2"></span>
        </i>
    </div> -->
    <div id="kt_scrolltop" class="scrolltop web" data-kt-scrolltop="true">
        <i class="fa-solid fa-chevron-up pe-3"></i>
    </div>
    <script src="{{ asset('website') }}/assets/js/scripts.bundle.js"></script>

    <script>
        var hostUrl = "{{ asset('website') }}/assets/";
    </script>
    <!-- jQuery (required) -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script>
    <script src="{{ asset('website') }}/assets/js/scripts.bundle.js"></script>
    <!-- Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <!-- sweetalert JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <!-- chartjs - using UMD version to avoid module import issues -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- International Telephone Input JS -->
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/intlTelInput.min.js"></script>
    <!-- Form Repeater JS -->
    <script src="{{ asset('website') }}/assets/plugins/custom/formrepeater/formrepeater.bundle.js"></script>
    <script>
        {!! strip_tags(html_entity_decode(setting()->code_snippet ?? '')) !!}
    </script>
    <!-- International Telephone Input Initialization -->
    <script>
        const input = document.querySelector("#phone");
        if (input) {
            const iti = window.intlTelInput(input, {
                separateDialCode: true,
                initialCountry: "auto",
                utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js",
                geoIpLookup: function(success, failure) {
                    fetch("https://ipinfo.io/json?token=YOUR_TOKEN")
                        .then(res => res.json())
                        .then(data => success(data.country))
                        .catch(() => success("us"));
                },
            });
        }
    </script>

    @stack('js')
    <script>
        function swiperScript() {
            if ($(".service-swipper").length) {
                var topRatedSwiper = new Swiper(".service-swipper", {
                    loop: true,
                    slidesPerView: 4,
                    spaceBetween: 30,
                    navigation: {
                        nextEl: ".top-rated-next",
                        prevEl: ".top-rated-prev",
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true,
                    },
                    breakpoints: {
                        320: {
                            slidesPerView: 1,
                            spaceBetween: 20
                        },
                        768: {
                            slidesPerView: 2,
                            spaceBetween: 30
                        },
                        1024: {
                            slidesPerView: 3,
                            spaceBetween: 30
                        }
                    }
                });
            }
            if ($(".mySwiper2").length) {
                var mySwiper2 = new Swiper(".mySwiper2", {
                    slidesPerView: 4,
                    spaceBetween: 20,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }
            if ($(".review-swiper").length) {
                var reviewSwiper = new Swiper(".review-swiper", {
                    slidesPerView: 4,
                    spaceBetween: 15,
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }

            if ($(".cert-swiper").length) {
                var certSwiper = new Swiper(".cert-swiper", {
                    slidesPerView: 4,
                    spaceBetween: 10,
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }

            if ($(".meet-the-team-swiper").length) {
                var teamSwiper = new Swiper(".meet-the-team-swiper", {
                    slidesPerView: 4,
                    spaceBetween: 15,
                    freeMode: true,
                    loop: true,
                    navigation: {
                        nextEl: ".swiper-button-next",
                        prevEl: ".swiper-button-prev",
                    },
                });
            }
        }

        // Initialize swipers when document is ready
        $(document).ready(function () {
            swiperScript();
        });
    </script>
    <script type="text/javascript">
        @if (session()->has('message'))
            Swal.fire({
                title: "{{ session()->get('title') ?? 'success!' }}",
                html: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 600000,
                buttons: false,
            });
        @endif
        @if (session()->has('flash_message'))
            Swal.fire({
                title: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 600000,
                buttons: false,
            });
        @endif
        @if (session()->has('status'))
            Swal.fire({
                title: "Password Reset Successful!",
                text: "{{ session('status') }}",
                icon: "success",
                confirmButtonText: "OK"
            });
        @endif
    @if (session()->has('social_validation'))
        Swal.fire({
            title: "{{ session()->get('social_validation')['title'] ?? 'Success!' }}",
            html: "{{ session()->get('social_validation')['message'] }}",
            icon: "{{ session()->get('social_validation')['type'] ?? 'success' }}",
            timer: 600000,
            buttons: false,
        });
    @endif

        //delete button confirm swal dynamic.
        function showDeleteConfirmation(button) {
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to delete this item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    button.closest('.delete-form').submit();
                }
            });
        }

        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>
    <script>

        // select2
        $(document).ready(function () {

            $('.select2-filter').select2({
                minimumResultsForSearch: Infinity,
                width: 'auto',
                dropdownAutoWidth: true
            });
        });

        $(document).ready(function () {
            const navLinks = $('.table-content');
            const sections = $('.content > div[id]');

            // Scroll on click
            navLinks.on('click', function (e) {
                e.preventDefault();
                const targetId = $(this).attr('href');
                const offsetTop = $(targetId).offset().top - 20;

                $('html, body').stop().animate({
                    scrollTop: offsetTop
                }, 400);

                navLinks.removeClass('active');
                $(this).addClass('active');
            });

            // Scroll Spy
            $(window).on('scroll', function () {
                const scrollMiddle = $(window).scrollTop() + $(window).height() / 2;
                let currentId = '';

                sections.each(function () {
                    const sectionTop = $(this).offset().top;
                    const sectionBottom = sectionTop + $(this).outerHeight();

                    if (scrollMiddle >= sectionTop && scrollMiddle <= sectionBottom) {
                        currentId = $(this).attr('id');
                        return false; // break loop
                    }
                });

                if (currentId) {
                    navLinks.removeClass('active');
                    $('.table-content[href="#' + currentId + '"]').addClass('active');
                }
            });


            $(window).trigger('scroll');
        });
        // loader
        var loader = document.getElementById("secLoader");
        window.addEventListener("load", function () {
            loader.style.display = "none"
        });

        // Scroll to top functionality
        $(document).ready(function() {
            var scrollTop = $('#kt_scrolltop');
            if (scrollTop.length) {
                $(window).scroll(function() {
                    if ($(this).scrollTop() > 300) {
                        scrollTop.fadeIn();
                    } else {
                        scrollTop.fadeOut();
                    }
                });

                scrollTop.click(function() {
                    $('html, body').animate({
                        scrollTop: 0
                    }, 600);
                    return false;
                });
            }
        });

        // Inline calendar initialization for booking pages
        $(document).ready(function() {
            if ($('#inline-calendar').length) {
                var selectedRange = [];
                var calendarInstance = flatpickr("#inline-calendar", {
                    inline: true,
                    mode: "range",
                    dateFormat: "Y-m-d",
                    defaultDate: "today",
                    onChange: function(selectedDates, dateStr, instance) {
                        selectedRange = selectedDates;
                        console.log('Date range selected:', selectedDates);
                    },
                    onReady: function(selectedDates, dateStr, instance) {
                        console.log('Inline calendar ready');
                    }
                });

                // Make selectedRange and calendarInstance available globally for booking page
                window.selectedRange = selectedRange;
                window.calendarInstance = calendarInstance;
            }
        });

        flatpickr("#datePicker", {
            dateFormat: "Y-m-d",
            minDate: "today"
        });

        flatpickr("#timePicker", {
            enableTime: true,
            noCalendar: true,
            dateFormat: "h:i K",
            time_24hr: false
        });

        $(document).ready(function () {
            // Assign data-sub-category attributes to category boxes
            $('.service-drop-down #tab-personal-trainers .category-box').each(function (index) {
                const dataAttr = 'services-personal-trainer-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            $('.service-drop-down #tab-makeup-artists .category-box').each(function (index) {
                const dataAttr = 'services-makeup-artist-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            $('.professional-drop-down #tab-personal-trainers .category-box').each(function (index) {
                const dataAttr = 'professional-personal-trainer-subcategory-' + (index + 1);
                $(this).attr('data-sub-category', dataAttr);
            });

            // Redirect on category-box click (only if not already on target URL)
            $(document).on('click', '.category-box', function () {
                const $this = $(this);
                const subcategorySlug = $this.data('sub-category');
                const categoryPane = $this.closest('.tab-pane').attr('id');
                let targetUrl = '';

                if ($this.closest('.service-drop-down').length) {
                    targetUrl = '/services?category-name=' + categoryPane + '&subcategory-name=' +
                        subcategorySlug;
                } else if ($this.closest('.professional-drop-down').length) {
                    targetUrl = '/professional?category-name=' + categoryPane + '&subcategory-name=' +
                        subcategorySlug;
                }

                const fullTargetUrl = window.location.origin + targetUrl;

                if (targetUrl && window.location.href !== fullTargetUrl) {
                    window.location.href = targetUrl;
                }
            });

            // On page load: activate tab and scroll or click subcategory
            const params = new URLSearchParams(window.location.search);
            const categoryName = params.get('category-name');
            const subcategoryName = params.get('subcategory-name');

            if (categoryName) {
                const $mainTabBtn = $('[data-bs-target="#' + categoryName + '"]');
                const $subcategoryBox = $('[data-sub-category="' + subcategoryName + '"]');

                if ($mainTabBtn.length) {
                    $mainTabBtn.trigger('click');

                    if ($subcategoryBox.length) {
                        setTimeout(() => {
                            if ($subcategoryBox.hasClass('nav-link')) {
                                $subcategoryBox.trigger('click');
                            } else {
                                $subcategoryBox[0].scrollIntoView({
                                    behavior: 'smooth'
                                });
                            }

                            $subcategoryBox.addClass('active');
                            setTimeout(() => $subcategoryBox.removeClass('highlight'), 2000);
                        }, 300);
                    }
                }
            }
        });

        // DataTables initialization
        $(document).ready(function () {
            if ($(".responsiveTable").length) {
                try {
                    // Skip tables that are on specific pages that handle their own initialization
                    var skipPages = ['/my-booking', '/customer-booking'];
                    var currentPath = window.location.pathname;
                    var shouldSkip = skipPages.some(page => currentPath.includes(page));

                    if (shouldSkip) {
                        console.log('Skipping DataTable initialization for this page');
                        return;
                    }

                    var table = $(".responsiveTable").DataTable({
                        dom: "rtip",
                        responsive: true,
                        scrollX: false,
                        paging: true,
                        pageLength: 8,
                        lengthChange: false,
                        initComplete: function () {
                            // Set data-label attributes from header text
                            this.api()
                                .columns()
                                .header()
                                .each(function (header) {
                                    var title = $(header).text();
                                    $(header).attr("data-label", title);
                                });
                        },
                    });
                    // Update data-labels when table changes
                    table.on("draw", function () {
                        $("th").each(function () {
                            var title = $(this).text();
                            $(this).attr("data-label", title);
                        });
                    });

                    $("#customSearchInput").on("keyup", function () {
                        table.search(this.value).draw();
                    });
                } catch (error) {
                    console.error('DataTable initialization error:', error);
                }
            }
        });

        // favourite functionality
        // $(document).ready(function () {
        //     $('.fav-icon i').on('click', function () {
        //         const $icon = $(this);

        //         // Toggle class
        //         $icon.toggleClass('fa-solid fa-regular text-danger');

        //         // Show toast only if it's being added
        //         if ($icon.hasClass('fa-solid')) {
        //             $('#toast').stop(true, true).fadeIn(300).delay(1500).fadeOut(400);
        //         }
        //     });
        // });


        $(document).ready(function () {
            $('.fav-icon i').on('click', function () {
                const $icon = $(this);

                // Switch between fa-regular and fa-solid
                if ($icon.hasClass('fa-regular')) {
                    $icon.removeClass('fa-regular').addClass('fa-solid text-danger');
                    $('#toast').text('Added to wishlist')
                        .stop(true, true).fadeIn(300).delay(1500).fadeOut(400);
                } else {
                    $icon.removeClass('fa-solid text-danger').addClass('fa-regular');
                    $('#toast').text('Removed from wishlist')
                        .stop(true, true).fadeIn(300).delay(1500).fadeOut(400);
                }
            });
        });

    </script>

    <!-- Pusher for Global Delivery Notifications -->
    @auth
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <script>
        $(document).ready(function() {
            // Check if Pusher is loaded
            if (typeof Pusher === 'undefined') {
                return;
            }

            // Check environment variables
            var pusherKey = '61adaf56059734aefbeb';
            var pusherCluster = 'ap2';

            if (!pusherKey || !pusherCluster) {
                return;
            }

            try {
                // Initialize Pusher
                window.Pusher = Pusher;
                window.pusher = new Pusher(pusherKey, {
                    cluster: pusherCluster,
                    encrypted: true,
                    auth: {
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        }
                    },
                    authEndpoint: '/broadcasting/auth'
                });

                // Set up delivery status handler
                var currentUserId = '{{ auth()->id() }}';

                if (!currentUserId) {
                    return;
                }

                // Subscribe to user-specific channel for delivery notifications and notifications
                var globalUserChannel = window.pusher.subscribe('user.' + currentUserId);

                // Listen for new notifications
                globalUserChannel.bind('notification.sent', function(data) {
                    console.log('New notification received:', data);

                    // Increment notification counter immediately
                    incrementNotificationCounter();

                    // Add notification to dropdown
                    addNotificationToDropdown(data);

                    // Show toast notification for customers
                    if (typeof toastr !== 'undefined') {
                        toastr.info(data.message, data.title);
                    } else if (Notification.permission === 'granted') {
                        new Notification(data.title, {
                            body: data.message,
                            icon: data.sender_image ?
                                '{{ asset("website") }}/' + data.sender_image :
                                '{{ asset("website/no_avatar.jpg") }}'
                        });
                    }
                });

                globalUserChannel.bind('message.received', function(data) {
                    console.log('🔔 FRONTEND: Global message.received event:', data);
                    // Only handle delivery status for messages not from current user
                    if (data.message.sender.id != currentUserId) {
                        console.log(`🔔 FRONTEND: Marking message ${data.message.id} as delivered`);
                        // Mark as delivered via AJAX
                        $.ajax({
                            url: '/chats/messages/' + data.message.id + '/delivered',
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                console.log(`🔔 FRONTEND: Successfully marked message ${data.message.id} as delivered`);
                            },
                            error: function(xhr, status, error) {
                                console.error(`🔔 FRONTEND: Error marking message ${data.message.id} as delivered:`, error);
                            }
                        });

                        // Check if this conversation is currently open (only on chat page)
                        if (window.location.pathname.includes('/chats')) {
                            // Use ChatApp internal tracking instead of URL
                            var isConversationOpen = (typeof ChatApp !== 'undefined' &&
                                                    ChatApp.currentConversationIds === data.conversation_ids);

                            if (isConversationOpen) {
                                $.ajax({
                                    url: '/chats/mark-read/' + data.message.conversation_id,
                                    method: 'POST',
                                    data: {
                                        _token: $('meta[name="csrf-token"]').attr('content')
                                    }
                                });
                            }
                        }

                        // Update envelope counter directly
                        if (typeof ChatApp !== 'undefined' && ChatApp.updateEnvelopeCounterDirect) {
                            ChatApp.updateEnvelopeCounterDirect();
                        } else {
                            // Fallback for non-chat pages
                            updateEnvelopeCounter();
                        }

                        // Update sidebar if on chat page
                        if (window.location.pathname.includes('/chats') && typeof ChatApp !== 'undefined') {
                            ChatApp.loadConversations();
                        }
                    }
                });

                // Listen for account status changes (deactivation/deletion)
                globalUserChannel.bind('account.status_changed', function(data) {
                    console.log('Account status change received:', data);
                    
                    const isDeleted = data.action === 'deleted';
                    const title = isDeleted ? 'Account Deleted' : 'Account Deactivated';
                    const icon = isDeleted ? 'error' : 'warning';
                    
                    // Show SweetAlert notification
                    Swal.fire({
                        title: title,
                        text: data.message || (isDeleted ? 'Your account has been deleted' : 'Your account has been deactivated'),
                        icon: icon,
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        timer: 1500,
                        timerProgressBar: true
                    });
                    
                    // Auto logout after 1.5 seconds
                    setTimeout(() => {
                        // Create a form and submit it to logout
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '/logout';
                        
                        const csrfToken = document.createElement('input');
                        csrfToken.type = 'hidden';
                        csrfToken.name = '_token';
                        csrfToken.value = $('meta[name="csrf-token"]').attr('content');
                        
                        form.appendChild(csrfToken);
                        document.body.appendChild(form);
                        form.submit();
                    }, 1500);
                });
            } catch (error) {
                // Silent error handling
            }
        });

        // Envelope counter functions
        function updateEnvelopeCounter() {
            $.ajax({
                url: '/chats/unread-count',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        const count = response.count;
                        const counter = $('#envelope-counter');

                        if (count > 0) {
                            counter.text(count > 99 ? '99+' : count).show();
                        } else {
                            counter.hide();
                        }
                    }
                }
            });
        }

        // Notification counter functions
        function updateNotificationCounter() {
            $.ajax({
                url: '/notifications/unread-count',
                method: 'GET',
                success: function(response) {
                    if (response.success) {
                        const count = response.count;
                        const counter = $('#notification-counter');

                        if (count > 0) {
                            counter.text(count > 99 ? '99+' : count).show();
                        } else {
                            counter.hide();
                        }
                    }
                }
            });
        }

        // Increment notification counter
        function incrementNotificationCounter() {
            const counter = $('#notification-counter');
            let currentCount = parseInt(counter.text()) || 0;
            currentCount++;

            if (currentCount > 0) {
                counter.text(currentCount > 99 ? '99+' : currentCount).show();
            }
        }

        // Decrement notification counter
        function decrementNotificationCounter() {
            const counter = $('#notification-counter');
            let currentCount = parseInt(counter.text()) || 0;
            currentCount = Math.max(0, currentCount - 1);

            if (currentCount > 0) {
                counter.text(currentCount > 99 ? '99+' : currentCount).show();
            } else {
                counter.hide();
            }
        }

        // Add notification to dropdown
        function addNotificationToDropdown(data) {
            const notificationList = $('#notification-list');
            const imageUrl = data.sender_image ?
                '{{ asset("website") }}/' + data.sender_image :
                '{{ asset("website/no_avatar.jpg") }}';

            const notificationHtml = `
                <div class="d-flex align-items-center gap-3 justify-content-center border-bottom mb-5 pb-3"
                     data-notification-id="${data.id}"
                     style="cursor: pointer;">
                    <img src="${imageUrl}" alt="Logo" class="h-50px" style="width: 50px; object-fit: cover; border-radius: 50%;">
                    <div>
                        <p class="fs-12 mb-0 light-black"><span class="semi_bold">${data.title}</span> ${data.message.substring(0, 50)}${data.message.length > 50 ? '...' : ''}</p>
                        <p class="fs-12 mb-0">${data.created_at}</p>
                    </div>
                </div>
            `;

            // Add to top of list
            notificationList.prepend(notificationHtml);

            // Remove "No notifications yet" message if it exists
            notificationList.find('.text-center').remove();

            // Keep only latest 4 notifications in dropdown
            const notifications = notificationList.children('.d-flex');
            if (notifications.length > 4) {
                notifications.last().remove();
            }
        }

        // Initialize counters on page load
        $(document).ready(function() {
            @auth
            updateEnvelopeCounter();
            updateNotificationCounter();

            // Request notification permission
            if ('Notification' in window && Notification.permission === 'default') {
                Notification.requestPermission();
            }

            // Handle notification dropdown clicks to mark as read
            $(document).on('click', '#notification-list .d-flex', function() {
                const notificationElement = $(this);
                const notificationId = notificationElement.data('notification-id');

                if (notificationId) {
                    $.ajax({
                        url: '/notifications/' + notificationId + '/mark-read',
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                        },
                        success: function(response) {
                            if (response.success) {
                                decrementNotificationCounter();
                                notificationElement.addClass('opacity-50'); // Visual feedback
                            }
                        }
                    });
                }
            });
            @endauth
        });
    </script>

    <!-- Pusher for Global Delivery Notifications -->
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <script>
        // Initialize Pusher globally for delivery notifications
        if (typeof Pusher !== 'undefined') {
            window.Pusher = Pusher;
            window.pusher = new Pusher('61adaf56059734aefbeb', {
                cluster: 'ap2',
                encrypted: true,
                auth: {
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                authEndpoint: '/broadcasting/auth'
            });

        }
    </script>
    @endauth

</body>
</html>
