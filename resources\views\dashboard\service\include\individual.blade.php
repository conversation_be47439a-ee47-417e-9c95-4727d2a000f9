<div>
    <form
        action="{{ isset($service) ? route('services.update', ['service' => $service->ids, 'type' => 'individual']) : route('services.store', ['type' => 'individual']) }}"
        method="POST" enctype="multipart/form-data" class="form-add-services" id="individualServiceForm">
        @csrf
        @isset($service)
            @method('PUT')
        @endisset
        <div class="row row-gap-5">
            {{-- Service Name --}}
            <div class="col-md-12">
                <label for="service-name" class="form-label form-input-labels">
                    Service Name<span class="text-danger">*</span>
                </label>
                <input type="text" class="form-control form-inputs-field" placeholder="Enter Service name"
                    id="service-name" name="name" value="{{ old('name', $service->name ?? '') }}">
                @error('name')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Name End --}}


            {{-- Category Name --}}
            <div class="col-md-6">
                <label for="category" class="form-label form-input-labels">Category<span
                        class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" id="category" name="category_id">
                    <option></option>
                    @forelse ($categories as $category)
                        <option value="{{ $category->id }}"
                            {{ old('category_id', $service->category->id ?? '') == $category->id ? 'selected' : '' }}>
                            {{ $category->name }}
                        </option>
                    @empty
                        <option value="">No categories found</option>
                    @endforelse
                </select>
                @error('category_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror

                <label id="category-error" class="error" for="category"></label>
            </div>
            {{-- Category Name End --}}


            {{-- Subcategory --}}
            <div class="col-md-6">
                <label for="subcategory" class="form-label form-input-labels">Sub
                    Category<span class="text-danger">*</span></label>
                <select class="form-select form-select-field" data-control="select2" data-dropdown-css-class="w-619px"
                    data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true"
                    id="subcategory" name="subcategory_id">
                    <option value="">Select Subcategory</option>
                    @if (isset($service) && $service->category)
                        @forelse ($service->category->subcategories as $subcategory)
                            <option value="{{ $subcategory->id }}"
                                {{ old('subcategory_id', $service->subcategory->id ?? '') == $subcategory->id ? 'selected' : '' }}>
                                {{ $subcategory->name }}
                            </option>
                        @empty
                        @endforelse
                    @endif
                </select>
                @error('subcategory_id')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                <label id="subcategory-error" class="error" for="subcategory"></label>
            </div>
            {{-- Subcategory End --}}


            @if (auth()->check() && auth()->user()->hasRole('business'))
                <div class="col-md-12">
                    <label for="staff-member" class="form-label form-input-labels">Assign Staff
                        Member<span class="text-danger">*</span></label>
                    <select class="form-select form-select-field" data-control="select2"
                        data-dropdown-css-class="w-619px" data-close-on-select="false"
                        data-placeholder="Select staff members" data-allow-clear="true" multiple="multiple"
                        id="staff-member" name="staff_ids[]">
                        @forelse ($staffs as $staff)
                            <option value="{{ $staff->id }}"
                                {{ isset($service) && $service->staff->contains('id', $staff->id) ? 'selected' : '' }}>
                                {{ $staff->name }}
                            </option>
                        @empty
                            <option disabled>No staff members available</option>
                        @endforelse
                    </select>
                    @error('staff_ids')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror

                    <label id="staff-member-error" class="error" for="staff-member"></label>
                </div>
            @endif

            {{-- Availability --}}
            <div class="col-md-12">
                <label class="form-label form-input-labels">Availability<span class="text-danger">*</span></label>
                <div class="col-md-12">
                    <a class="form-control form-inputs-field d-flex justify-content-between align-items-center" data-bs-toggle="modal"
                        data-bs-target="#availabilityModal" id="availabilityField">
                        <div class="d-flex align-items-center">
                            <span>Select availability</span>
                        </div>
                        <i class="fa-solid fa-chevron-down"></i>
                    </a>
                </div>
                {{-- Hidden field for availability data --}}
                <input type="hidden" name="availabilities_dates" id="availabilities_dates" value="">
            </div>
            {{-- Availability End --}}

            {{-- Service Duration --}}
            <div class="col-md-6">
                <label for="duration" class="form-label form-input-labels ">Service Duration<span
                        class="text-danger">*</span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Services Duration (in minutes)"
                    id="duration" name="duration" value="{{ old('duration', $service->duration ?? '') }}" min="10">
                </select>
                @error('duration')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Service Duration End --}}


            {{-- Price --}}
            <div class="col-md-3">
                <label for="price" class="form-label form-input-labels">
                    Price <span class="normal opacity-6 light-black">(Inclusive VAT)<span
                            class="text-danger">*</span></span>
                </label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter price"
                    id="price" name="price" value="{{ old('price', $service->price ?? '') }}" min="1">
                @error('price')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Price End --}}

            {{-- Additional Costs --}}
            <div class="col-md-3">
                <label for="tax" class="form-label form-input-labels">Additional Costs<span
                        class="text-danger">*</span></label>
                <input type="number" class="form-control form-inputs-field" placeholder="Enter additional cost"
                    id="tax" name="additional_cost"
                    value="{{ old('additional_cost', $service->additional_cost ?? '') }}">
                @error('additional_cost')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Additional Costs End --}}

            {{-- Required Items --}}
            <div class="col-md-12">
                <label for="required-items" class="form-label form-input-labels">Required
                    Items For Service</label>
                <input type="text" class="form-control form-inputs-field"
                    placeholder="Enter required items (separate with comma)" id="required-items"
                    name="required_items" value="{{ old('required_items', $service->required_items ?? '') }}">
                @error('required_items')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Required Items End --}}

            {{-- Description --}}
            <div class="col-md-12">
                <label for="description" class="form-label form-input-labels">Description<span
                        class="text-danger">*</span></label>
                <textarea class="form-control form-inputs-field form-textarea-field" id="description" name="description"
                    rows="4" placeholder="Enter Description here">{{ old('description', $service->description ?? '') }}</textarea>
                @error('description')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Description End --}}

            {{-- Onsite & Customer Location --}}
            <div class="col-md-12">
                <label class="form-label form-input-labels">Service Location<span class="text-danger">*</span></label>
                <div class="d-flex gap-4">
                    <div>
                        <label class="styled-checkbox d-flex gap-3">
                            <input type="checkbox" name="is_onsite" value="1" id="onsite-secondary"
                                @checked(old('is_onsite', $service->is_onsite ?? false))>
                            <span class="fs-14 light-black normal">On-site</span>
                        </label>
                    </div>
                    <label class="styled-checkbox d-flex gap-3">
                        <input type="checkbox" name="is_customer_location" value="1"
                            @checked(old('is_customer_location', $service->is_customer_location ?? false)) id="customer-location-secondary">
                        <span class="fs-14 light-black normal">Customer Location</span>
                    </label>
                </div>
                @error('is_onsite')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
                <div class="service-location-error"></div>
            </div>

            {{-- Onsite & Customer Location End --}}
            <div class="row row-gap-5">
                {{-- Physical Location --}}
                <div class="col-md-12 form-hide-box" id="physical-location-field">
                    <label for="pac-input" class="form-label form-input-labels">
                        Physical Location
                    </label>
                    <input type="text" class="form-control form-inputs-field form-textarea-field" id="pac-input"
                        name="physical_location"
                        value="{{ old('physical_location', $service->physical_location ?? '') }}"
                        placeholder="Your registered business location">

                    <input type="hidden" name="lat" value="{{ old('lat', $service->lat ?? '') }}"
                        id="latitude">

                    <input type="hidden" name="lng" value="{{ old('lng', $service->lng ?? '') }}"
                        id="longitude">

                    <div class="custom_loc mt-2">
                        <div id="map" style="height: 300px;"></div>
                    </div>

                    @error('physical_location')
                        <p class="text-danger">{{ $message }}</p>
                    @enderror
                </div>

                {{-- Physical Location End --}}

                {{-- Radius, Travel Time, Additional Service Charges --}}
                <div class="col-md-4 form-hide-box" id="radius-field">
                    <label for="radius" class="form-label form-input-labels">Radius</label>
                    <input type="number" class="form-control form-inputs-field" placeholder="Enter radius"
                        value="{{ old('radius', $service->radius ?? '') }}" id="radius" name="radius">
                    @error('radius')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="traveltime-field">
                    <label for="traveltime" class="form-label form-input-labels">Travel
                        Time (minutes)</label>
                    <input type="number" class="form-control form-inputs-field"
                        value="{{ old('travel_time', $service->travel_time ?? '') }}" placeholder="Enter travel time"
                        id="traveltime" name="travel_time">
                    @error('travel_time')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>

                <div class="col-md-4 form-hide-box" id="servicecharges-field">
                    <label for="servicecharges" class="form-label form-input-labels">Additional
                        Service Charges</label>
                    <input type="number" class="form-control form-inputs-field"
                        placeholder="Enter additional service charges" id="servicecharges" name="service_charges"
                        value="{{ old('service_charges', $service->service_charges ?? '') }}" min="1">
                    @error('service_charges')
                        <p class="text-danger">
                            {{ $message }}
                        </p>
                    @enderror
                </div>
            </div>

            {{-- Thumbnail Image --}}
            <div class="col-md-4 add-service-thumbnail">
                <label for="thumbnail-secondary" class="form-label form-input-labels ">Thumbnail Image<span
                        class="text-danger">*</span></label>

                <div class="position-relative form-add-category">
                    <div class="image-input {{ isset($service) ? ($service->image ? 'image-input-changed' : 'image-input-empty') : 'image-input-empty' }}"
                        data-kt-image-input="true">
                        <!-- Preview -->
                        <div class="image-input-wrapper"
                            style="background-image: url('{{ isset($service) ? ($service->image ? asset('website/' . $service->image) : '') : '' }}');">
                        </div>
                        <!-- Upload (Change) -->
                        <label
                            class="image-label flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                            data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Change avatar">
                            <i class="bi bi-upload upload-icon"></i>
                            <span>Upload Image</span>
                            <span>Upto 256KB</span>
                            <input type="file" name="thumbnail" accept=".png, .jpg, .jpeg" />
                            <!-- Hidden input required for remove -->
                            <input type="hidden" name="thumbnail_remove" value="0" />
                        </label>
                        <!-- Remove -->
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>

                        <!-- Cancel -->
                        <span
                            class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                            data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">
                            <i class="ki-outline ki-cross fs-3"></i>
                        </span>

                    </div>
                </div>
                <label id="thumbnail-error" class="error" for="thumbnail"></label>
                @error('thumbnail')
                    <p class="text-danger">
                        {{ $message }}
                    </p>
                @enderror
            </div>
            {{-- Thumbnail Image End --}}




            <div class="">
                <button type="submit" class="add-btn">
                    {{ $btn_text ?? 'Add' }}
                </button>
            </div>
        </div>
    </form>
</div>

{{-- Include Availability Modal --}}
@include('dashboard.service.include.availability-modal')




@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            setTimeout(function() {
                if (typeof $.fn.validate !== 'undefined') {
                    $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                        if (element.files.length === 0) {
                            return true; // no file selected, let 'required' rule handle this
                        }
                        const fileSizeKB = element.files[0].size / 1024; // size in KB
                        return fileSizeKB <= maxSizeKB;
                    }, 'File size must be less than {0} KB.');
                    // Add custom validation method for role-based requirements
                    $.validator.addMethod('requiredForRole', function(value, element, params) {
                        const userRole = params.role;
                        const currentUserRole =
                            '{{ auth()->user()->getRoleNames()->first() }}';

                        // If current user has the specified role, field is required
                        if (currentUserRole === userRole) {
                            return value && value.length > 0;
                        }
                        // If user doesn't have the role, field is not required
                        return true;
                    }, 'This field is required for your role.');

                    // Add custom validation method for conditional requirements based on checkboxes
                    $.validator.addMethod('requiredIfChecked', function(value, element, params) {
                        const checkboxSelector = params.checkbox;
                        const isChecked = $(checkboxSelector).is(':checked');

                        // If checkbox is checked, field is required
                        if (isChecked) {
                            return value && value.trim().length > 0;
                        }
                        // If checkbox is not checked, field is not required
                        return true;
                    }, 'This field is required when the related option is selected.');



                    // Add validation for multiple roles
                    $.validator.addMethod('requiredForRoles', function(value, element, params) {
                        const requiredRoles = params.roles; // Array of roles
                        const currentUserRole =
                            '{{ auth()->user()->getRoleNames()->first() }}';

                        // If current user has any of the specified roles, field is required
                        if (requiredRoles.includes(currentUserRole)) {
                            return value && value.length > 0;
                        }
                        return true;
                    }, 'This field is required for your role.');

                    // Add custom validation method for service location checkboxes
                    $.validator.addMethod('serviceLocationRequired', function(value, element) {
                            const isOnsiteChecked = $('#onsite-secondary').is(':checked');
                            const isCustomerLocationChecked = $('#customer-location-secondary').is(
                                ':checked');

                            // At least one checkbox must be checked (onsite OR customer location)
                            return isOnsiteChecked || isCustomerLocationChecked;
                        },
                        'Please select at least one service location option (On-site or Customer Location)'
                    );
                    // Add custom form submit handler for checkbox validation

                    $("#individualServiceForm").validate({
                        submitHandler: function(form) {
                            // Check if getSelectedAvailability function exists (from modal)
                            if (typeof getSelectedAvailability === 'function') {
                                // Update availability data
                                if (typeof saveCurrentWeekData === 'function') {
                                    saveCurrentWeekData();
                                }
                                var selectedData = getSelectedAvailability();
                                // Add availability data to form
                                var availabilityInput = $('<input>').attr({
                                    type: 'hidden',
                                    name: 'availabilities_dates',
                                    value: JSON.stringify(selectedData)
                                });
                                $(form).find('input[name="availabilities_dates"]').remove();
                                $(form).append(availabilityInput);

                                // Update availability field status
                                if (typeof updateAvailabilityFieldStatus === 'function') {
                                    updateAvailabilityFieldStatus();
                                }
                            }
                            form.submit();
                        },
                        rules: {
                            thumbnail: {
                                required: true,
                                maxFileSize: 256
                            },
                            is_onsite: {
                                serviceLocationRequired: true
                            },
                            name: {
                                required: true,
                                minlength: 2
                            },
                            category_id: {
                                required: true
                            },
                            subcategory_id: {
                                required: true
                            },
                            // Role-based validation for staff members
                            'staff_ids[]': {
                                requiredForRole: {
                                    role: 'business'
                                }
                            },

                            // Conditional validation for location fields
                            physical_location: {
                                requiredIfChecked: {
                                    checkbox: '#onsite-secondary'
                                }
                            },
                            radius: {
                                requiredIfChecked: {
                                    checkbox: '#customer-location-secondary'
                                },
                                number: true,
                                min: 1
                            },
                            travel_time: {
                                requiredIfChecked: {
                                    checkbox: '#customer-location-secondary'
                                },
                                number: true,
                                min: 1
                            },
                            service_charges: {
                                requiredIfChecked: {
                                    checkbox: '#customer-location-secondary'
                                },
                                number: true,
                                min: 1
                            },
                            duration: {
                                required: true,
                                number: true,
                                min: 10
                            },
                            price: {
                                required: true,
                                number: true,
                                min: 1
                            },
                            additional_cost: {
                                required: true,
                                number: true,
                                min: 0
                            },
                            required_items: {
                                maxlength: 1000
                            },
                            description: {
                                required: true,
                                maxlength: 1000
                            }
                        },
                        messages: {
                            thumbnail: {
                                required: "Please upload a thumbnail",
                                maxFileSize: "Image size must not exceed 256KB"
                            },
                            name: {
                                required: "Service name is required",
                                minlength: "Service name must be at least 2 characters"
                            },
                            is_onsite: {
                                serviceLocationRequired: "Please select at least one service location option (On-site or Customer Location)"
                            },
                            category_id: {
                                required: "Please select a category"
                            },
                            subcategory_id: {
                                required: "Please select a subcategory"
                            },
                            'staff_ids[]': {
                                requiredForRole: "Please assign at least one staff member to this service"
                            },

                            physical_location: {
                                requiredIfChecked: "Physical location is required"
                            },
                            radius: {
                                requiredIfChecked: "Radius is required",
                                number: "Please enter a valid radius",
                                min: "Radius must be at least 1"
                            },
                            travel_time: {
                                requiredIfChecked: "Travel time is required",
                                number: "Please enter a valid travel time",
                                min: "Travel time must be at least 1 minute"
                            },
                            service_charges: {
                                requiredIfChecked: "Service charges are required",
                                number: "Please enter a valid service charge",
                                min: "Service charges cannot be 0"
                            },
                            duration: {
                                required: "Please select service duration",
                                number: "Please enter a valid duration",
                                min: "Duration must be at least 10 minutes"
                            },
                            price: {
                                required: "Service price is required",
                                number: "Please enter a valid price",
                                min: "Price cannot be 0"
                            },
                            additional_cost: {
                                required: "Additional cost is required",
                                number: "Please enter a valid additional cost",
                                min: "Additional cost cannot be negative"
                            },
                            required_items: {
                                maxlength: "Required items cannot exceed 1000 characters"
                            },
                            description: {
                                required: "Description is required",
                                maxlength: "Description cannot exceed 1000 characters"
                            }
                        },
                        errorPlacement: function(error, element) {
                            if (element.attr('name') === 'is_onsite') {
                              // Error message hamesha .service-location div me dikhayen
                              error.appendTo($('#individualServiceForm').find('.service-location-error'));
                            } else {
                              error.insertAfter(element);
                            }
                        }
                    });

                    $('#onsite-secondary, #customer-location-secondary').on('change', function() {
                        $('#pac-input, #radius, #traveltime, #servicecharges').removeClass('error');
                        $('.error[for="physical_location"], .error[for="radius"], .error[for="travel_time"], .error[for="service_charges"]')
                            .remove();

                        // Clear service location validation error
                        $('#onsite-secondary').removeClass('error');
                        $('label[for="is_onsite"].error').remove();
                    });

                    // Clear validation errors when category changes
                    $('#category').on('change', function() {
                        $(this).removeClass('error');
                        $('#category-error').text('').hide();
                    });

                    // Clear validation errors when subcategory changes
                    $('#subcategory').on('change', function() {
                        $(this).removeClass('error');
                        $('#subcategory-error').text('').hide();
                    });

                    // Clear validation errors when staff member is selected
                    $('#staff-member').on('change', function() {
                        $(this).removeClass('error');
                        $('#staff-member-error').text('').hide();
                    });

                    // Clear validation errors when thumbnail is uploaded
                    $('input[name="thumbnail"]').on('change', function() {
                        if (this.files && this.files.length > 0) {
                            $(this).removeClass('error');
                            $('#thumbnail-error').text('').hide();
                        }
                    });


                } else {
                    console.error('jQuery validate is not available');
                }
            }, 500);
        });
        // Initialize Google Maps for Individual Service
        function initIndividualServiceMap() {
            // Only use existing coordinates if we have them in the form fields (from old input or editing)
            const existingLat = document.getElementById('latitude').value;
            const existingLng = document.getElementById('longitude').value;
            const existingLocation = document.getElementById('pac-input').value;

            const userLat = existingLat && existingLat !== '' ? parseFloat(existingLat) : null;
            const userLng = existingLng && existingLng !== '' ? parseFloat(existingLng) : null;
            const userLocation = existingLocation || '';



            let map, marker;

            // Only initialize map if we have existing coordinates
            if (userLat !== null && userLng !== null) {

                // Initialize map with existing coordinates
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: {
                        lat: userLat,
                        lng: userLng
                    }
                });

                // Create marker at existing location
                marker = new google.maps.Marker({
                    position: {
                        lat: userLat,
                        lng: userLng
                    },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Your Business Location'
                });

                // Set the form values
                document.getElementById('latitude').value = userLat;
                document.getElementById('longitude').value = userLng;
            } else {

                // Show placeholder instead of map
                const mapElement = document.getElementById('map');
                mapElement.innerHTML =
                    '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px;"></i><br>Map will appear after you search for a location</div></div>';
            }

            // Initialize autocomplete
            const input = document.getElementById('pac-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            // Only bind to map bounds if map exists
            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    return;
                }

                // Initialize map if it doesn't exist yet
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    // Add drag listener for new marker
                    marker.addListener('dragend', function() {
                        const position = marker.getPosition();
                        document.getElementById('latitude').value = position.lat();
                        document.getElementById('longitude').value = position.lng();

                        // Reverse geocoding to update address
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({
                            location: position
                        }, function(results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('pac-input').value = results[0]
                                    .formatted_address;
                            }
                        });
                    });
                } else {
                    // Update existing map and marker
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                // Update hidden inputs
                document.getElementById('latitude').value = place.geometry.location.lat();
                document.getElementById('longitude').value = place.geometry.location.lng();
            });

            // Handle marker drag (only if marker exists)
            if (marker) {
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    document.getElementById('latitude').value = position.lat();
                    document.getElementById('longitude').value = position.lng();

                    // Reverse geocoding to update address
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: position
                    }, function(results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('pac-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize when Google Maps is ready
        $(document).ready(function() {
            // Wait for Google Maps to load, then initialize
            function waitForGoogleMaps() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    initIndividualServiceMap();
                } else {
                    setTimeout(waitForGoogleMaps, 100);
                }
            }
            waitForGoogleMaps();
        });
    </script>
@endpush
