@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center flex-wrap">
                    <div>
                        <h6 class="sora black">Product Certifications</h6>
                        <p class="fs-14 sora light-black m-0">Upload and manage product certifications to ensure compliance. </p>
                    </div>
                    @can('certifications-create')
                        <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-certification">
                            <i class="fa-solid fa-plus me-3"></i> Add Product Certification
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <div class="search_box">
                                <label for="certificationSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="certificationSearchInput"
                                    placeholder="Search..." />
                            </div>
                        </div>
                        <div class="row row-gap-5 mt-5" id="certificationCardsContainer">

                            @include('dashboard.admin.certifications.partials.certification-cards', [
                                'certifications' => $certifications,
                            ])
                        </div>

                        <!-- Load More Button -->
                        <div class="text-center mt-4" id="loadMoreContainer"
                            style="{{ $totalCount > 10 ? '' : 'display: none;' }}">
                            <button type="button" class="btn add-btn" id="loadMoreBtn">
                                <i class="fas fa-plus me-2"></i>Load More
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.certifications.modal.add-certification-modal')
    @include('dashboard.admin.certifications.modal.edit-certification-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true; // no file selected, let 'required' rule handle this
                }
                const fileSizeKB = element.files[0].size / 1024; // size in KB
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            // Add custom method for edit image validation
            $.validator.addMethod('editImageRequired', function(value, element) {
                var imageInput = $('#edit-certification .image-input[data-kt-image-input="true"]');
                var hasNewFile = element.files.length > 0;

                // If user selected a new file, it's valid
                if (hasNewFile) {
                    return true;
                }

                // Check if there's an existing image displayed
                var backgroundImage = imageInput.find('.image-input-wrapper').css('background-image');
                var hasExistingImage = imageInput.hasClass('image-input-changed') &&
                                     backgroundImage &&
                                     backgroundImage !== 'none' &&
                                     backgroundImage !== '' &&
                                     !imageInput.hasClass('image-input-empty');

                return hasExistingImage;
            }, 'Please upload an image');
            let currentOffset = 10; // Start from 10 since first 10 are already loaded
            let isLoading = false;
            let currentSearch = ''; // Store current search term
            let searchTimeout; // Store search timeout for debounce

            // Add form validation
            $("#certificationForm").validate({
                errorClass: "error",
                errorElement: "label",
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                rules: {
                    avatar: {
                        required: true,
                        maxFileSize: 256
                    },
                    name: {
                        required: true,
                        maxlength: 100,
                    },
                    alt_tag: {
                        required: true,
                        maxlength: 100,
                    },
                    image_description: {
                        required: true,
                        maxlength: 100,
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image",
                        maxFileSize: "Image size must not exceed 256KB"
                    },
                    name: {
                        required: "Please enter category name",
                        maxlength: "Certification name cannot exceed 100 characters",
                    },
                    alt_tag: {
                        required: "Please enter alt tag",
                        maxlength: "Alt tag cannot exceed 100 characters",
                    },
                    image_description: {
                        required: "Please enter image description",
                        maxlength: "Image description cannot exceed 255 characters",
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });

            // Search functionality with debounce
            $('#certificationSearchInput').on('keyup', function() {
                let search = $(this).val().trim();
                console.log('Certification search input changed:', search);

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounce (300ms delay)
                searchTimeout = setTimeout(function() {
                    currentSearch = search;
                    currentOffset = 0; // Reset offset for search
                    searchCertifications(); // Search function
                }, 300);
            });

            // Function to search certification records
            function searchCertifications() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object - only include non-empty values
                let data = {
                    offset: currentOffset
                };

                // Only add search if it's not empty (but "0" is valid)
                if (currentSearch !== '') {
                    data.search = currentSearch;
                }
                $.ajax({
                    url: "{{ route('certifications.index') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        console.log('Certification search response:', response);
                        if (response.success) {
                            // Replace cards content for search
                            $('#certificationCardsContainer').html(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();

                            console.log('Certification cards updated with', response.count, 'records');

                            // Show success toast with result count only when searching
                            if (data.search) {
                                if (response.count === 0) {
                                    showToast('No results found for your search criteria', 'error');
                                } else {
                                    showToast(`Found ${response.count} result(s)`, 'success');
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Certification search error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Search failed. Please check your connection.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Load more functionality
            $('#loadMoreBtn').on('click', function() {
                console.log('Load more clicked, current offset:', currentOffset);
                loadMoreCertifications();
            });

            // Function to load more certification records
            function loadMoreCertifications() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object
                let data = {
                    offset: currentOffset
                };

                // Only add search if it's not empty (but "0" is valid)
                if (currentSearch !== '') {
                    data.search = currentSearch;
                }
                $.ajax({
                    url: "{{ route('certifications.load-more') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        console.log('Load more response:', response);
                        if (response.success) {
                            // Append content for load more
                            $('#certificationCardsContainer').append(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();

                            console.log('Certification cards appended with', response.count, 'more records');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load more error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Failed to load more records. Please try again.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Function to update load more button visibility
            function updateLoadMoreButton(response) {
                console.log('Load more button update:', {
                    has_more: response.has_more,
                    offset: response.offset,
                    count: response.count,
                    total: response.total
                });

                // Show/hide load more button based on whether there are more records
                if (response.has_more && response.count > 0) {
                    $('#loadMoreContainer').show();
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            // Function to bind edit buttons (needed after AJAX content load)
            function bindEditButtons() {
                $('.edit-certification').off('click').on('click', function(e) {
                    e.preventDefault();
                    var certificationId = $(this).data('id');
                    $.ajax({
                        url: '/admin/certifications/' + certificationId + '/edit',
                        method: 'GET',
                        success: function(data) {
                            $('#certificationId').val(data.ids);
                            $('#edit-name').val(data.name);
                            $('#edit-alt_tag').val(data.alt_tag);
                            $('#edit-image_description').val(data.image_description);
                            if (data.image) {
                                var baseImageUrl = $('meta[name="asset-url"]').attr(
                                        'content') ||
                                    '/website';
                                var imageUrl = baseImageUrl + '/' + data.image;
                                var imageInput = $('.image-input[data-kt-image-input="true"]');
                                var wrapper = imageInput.find('.image-input-wrapper');
                                wrapper.css('background-image', 'url(' + imageUrl + ')');
                                imageInput.removeClass('image-input-empty').addClass(
                                    'image-input-changed');
                                imageInput.find('[data-kt-image-input-action="remove"]')
                                    .removeClass('d-none');
                                imageInput.find('[data-kt-image-input-action="cancel"]')
                                    .removeClass('d-none');
                            } else {
                                var imageInput = $('.image-input[data-kt-image-input="true"]');
                                imageInput.addClass('image-input-empty').removeClass(
                                    'image-input-changed');
                                imageInput.find('.image-input-wrapper').css('background-image',
                                    'none');
                            }
                            $('#edit-certification').modal('show');
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching certification data:', error);
                            alert('Error loading data. Please try again.');
                        }
                    });
                });
            }

            // Initial binding of edit buttons
            bindEditButtons();

            // Function to show toast notifications
            function showToast(message, type) {
                const toastClass = type === 'success' ? 'bg-success' : type === 'warning' ? 'bg-warning' : 'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                $('body').append(toast);
                const toastElement = $('.toast').last();
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    delay: 3000
                });
                bsToast.show();

                // Remove toast element after it's hidden
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

        });
    </script>

    <script>
        $(document).ready(function() {

            // Edit form validation
            $("#editCertificationForm").validate({
                errorClass: "error",
                errorElement: "label",
                errorPlacement: function(error, element) {
                    if (element.attr('name') === 'avatar') {
                        error.insertAfter(element.closest('.image-input'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    avatar: {
                        editImageRequired: true,
                        maxFileSize: 250
                    },
                    name: {
                        required: true,
                        maxlength: 100,
                    },
                    alt_tag: {
                        required: true,
                        maxlength: 100,
                    },
                    image_description: {
                        required: true,
                        maxlength: 100,
                    }
                },
                messages: {
                    avatar: {
                        editImageRequired: "Please upload an image",
                        maxFileSize: "Image size must not exceed 250KB"
                    },
                    name: {
                        required: "Please enter certification name",
                        maxlength: "Certification name is too long try something shorter",
                    },
                    alt_tag: {
                        required: "Please enter alt tag",
                        maxlength: "Alt tag cannot exceed 100 characters",
                    },
                    image_description: {
                        required: "Please enter image description",
                        maxlength: "Image description cannot exceed 255 characters",
                    }
                },
                submitHandler: function(form) {
                    var certificationId = $('#certificationId').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/admin/certifications/' + certificationId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-certification').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                }
            });

            // Reset add certification modal when opened
            $('#add-certification').on('show.bs.modal', function() {
                // Reset form
                $('#certificationForm')[0].reset();

                // Clear any existing validation errors
                if ($("#certificationForm").data('validator')) {
                    $("#certificationForm").validate().resetForm();
                }
                
                // Remove error classes from all form fields
                $('#certificationForm .form-control').removeClass('error is-invalid');
                $('#certificationForm .error').remove();

                // Reset image input for add modal
                var addImageInput = $('#add-certification .image-input[data-kt-image-input="true"]');
                var addWrapper = addImageInput.find('.image-input-wrapper');
                addWrapper.css('background-image', '');
                addImageInput.removeClass('image-input-changed').addClass('image-input-empty');
                
                // Clear file input
                addImageInput.find('input[type="file"]').val('');
            });

            // Reset edit modal validation when opened
            $('#edit-certification').on('show.bs.modal', function() {
                // Clear any existing validation errors
                if ($("#editCertificationForm").data('validator')) {
                    $("#editCertificationForm").validate().resetForm();
                }
                
                // Remove error classes from all form fields
                $('#editCertificationForm .form-control').removeClass('error is-invalid');
                $('#editCertificationForm .error').remove();

                // Reset image input for edit modal (but don't clear existing image)
                var editImageInput = $('#edit-certification .image-input[data-kt-image-input="true"]');
                // Only reset if no image is loaded from server
                if (!editImageInput.hasClass('image-input-changed')) {
                    var editWrapper = editImageInput.find('.image-input-wrapper');
                    editWrapper.css('background-image', '');
                    editImageInput.removeClass('image-input-changed').addClass('image-input-empty');
                }
            });

            // Handle image removal in edit modal
            $(document).on('click', '#edit-certification [data-kt-image-input-action="remove"]', function() {
                setTimeout(function() {
                    // Trigger validation after image is removed
                    $('#edit-avatar').valid();
                }, 100);
            });

            // Handle image cancel in edit modal
            $(document).on('click', '#edit-certification [data-kt-image-input-action="cancel"]', function() {
                setTimeout(function() {
                    // Trigger validation after image is cancelled
                    $('#edit-avatar').valid();
                }, 100);
            });

            // Handle file input change in edit modal
            $(document).on('change', '#edit-avatar', function() {
                // Trigger validation when file is selected
                $(this).valid();
            });

            // Ensure modals are properly isolated when hidden
            $('#add-certification').on('hidden.bs.modal', function() {
                // Reset form completely
                $('#certificationForm')[0].reset();
                
                // Clear any existing validation errors
                if ($("#certificationForm").data('validator')) {
                    $("#certificationForm").validate().resetForm();
                }
                
                // Remove error classes from all form fields
                $('#certificationForm .form-control').removeClass('error is-invalid');
                $('#certificationForm .error').remove();
                
                // Reset add modal image input completely
                var addImageInput = $('#add-certification .image-input[data-kt-image-input="true"]');
                var addWrapper = addImageInput.find('.image-input-wrapper');
                addWrapper.css('background-image', '');
                addImageInput.removeClass('image-input-changed').addClass('image-input-empty');
                
                // Clear file input
                addImageInput.find('input[type="file"]').val('');
            });

            $('#edit-certification').on('hidden.bs.modal', function() {
                // Clear any existing validation errors
                if ($("#editCertificationForm").data('validator')) {
                    $("#editCertificationForm").validate().resetForm();
                }
                
                // Remove error classes from all form fields
                $('#editCertificationForm .form-control').removeClass('error is-invalid');
                $('#editCertificationForm .error').remove();
                
                // Clear any temporary changes in edit modal
                var editImageInput = $('#edit-certification .image-input[data-kt-image-input="true"]');
                // Only clear if it was a temporary upload, not the original image
                if (editImageInput.find('input[type="file"]').val()) {
                    editImageInput.find('input[type="file"]').val('');
                }
            });

        });
    </script>
@endpush
