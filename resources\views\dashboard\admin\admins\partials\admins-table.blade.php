@if ($admins->count() > 0)
    @foreach ($admins as $admin)
        <tr>
            <td data-label="ADMIN">
                <div class="d-flex align-items-center gap-3">
                    <div class="card-header p-0 border-0 align-items-start">
                        @if(isset($admin->profile) && $admin->profile->pic)
                            <img src="{{ asset('website').'/'.$admin->profile->pic }}"
                                class="h-60px w-60px rounded-3 object-fit-cover" alt="admin-image" />
                        @else
                            <div class="h-60px w-60px rounded-3 bg-secondary d-flex align-items-center justify-content-center">
                                <i class="fas fa-user-shield text-white fs-4"></i>
                            </div>
                        @endif
                    </div>
                    <div class="card-body p-0">
                        <p class="fs-16 regular black m-0 pb-1">{{ $admin->name }}</p>
                        <p class="fs-12 text-muted m-0">
                            @if($admin->roles->isNotEmpty())
                                {{ ucfirst($admin->roles->first()->name) }}
                            @else
                                Admin
                            @endif
                        </p>
                    </div>
                </div>
            </td>
            <td data-label="EMAIL ADDRESS">{{ $admin->email }}</td>
            @if ($admin->status == 1)
                <td data-label="STATUS" class="professional-status status paid-status">
                   <span class="status-text" >  Active </span>
                </td>
            @else
                <td data-label="STATUS" class="professional-status status unpaid-status">
                   <span class="status-text">  Inactive </span>
                </td>
            @endif
            <td data-label="JOINED DATE">
                {{ $admin->created_at->format('M d, Y') }}
            </td>
            <td data-label="ACTION">
                <div class="dropdown">
                    <button class="drop-btn" type="button" id="dropdownMenuButton{{ $admin->id }}" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ $admin->id }}">
                        <li>
                            <a class="dropdown-item fs-14 regular"
                                href="{{ route('admin.change_status', $admin->ids) }}">
                                <i class="bi bi-arrow-repeat me-2"></i>
                                {{ $admin->status == 1 ? 'Deactivate' : 'Activate' }}
                            </a>
                        </li>
                        <li>
                            <a href="javascript:void(0);" class="dropdown-item fs-14 regular cancel"
                               onclick="confirmDelete('{{ route('admin.delete', $admin->ids) }}')">
                                <i class="bi bi-trash me-2 cancel-icon"></i>
                                Delete
                            </a>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>
    @endforeach
@else
    <tr>
        <td colspan="5" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <i class="fas fa-user-shield fa-3x text-muted mb-3"></i>
                <h5 class="text-muted mb-2">No admins found</h5>
                <p class="text-muted">Try adjusting your search criteria or filters</p>
            </div>
        </td>
    </tr>
@endif
