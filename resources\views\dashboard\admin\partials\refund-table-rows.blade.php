@forelse ($refunds as $refund)
    <tr>
        <td data-label="BOOKING ID">{{ $refund->booking_number ?? 'N/A' }}</td>
        <td data-label="CUSTOMER NAME">{{ $refund->user_name ?? 'N/A' }}</td>
        <td data-label="EMAIL ADDRESS">{{ $refund->user_email ?? 'N/A' }}</td>
        <td data-label="SERVICE NAME">{{ $refund->service->name ?? 'N/A' }}</td>
        <td data-label="AMOUNT">${{ number_format($refund->total_amount ?? 0, 2) }}</td>
        <td data-label="REQUEST DATE">{{ \Carbon\Carbon::parse($refund->updated_at)->format('d M, Y') }}</td>
        <td data-label="REASON">
            @if($refund->cancel_reason)
                {{ Str::limit($refund->cancel_reason, 30) }}
            @else
                N/A
            @endif
        </td>

        {{-- STATUS --}}
        <td data-label="STATUS"
            class="request-status status
            @if ($refund->status == 3) paid-status
            @elseif($refund->status == 2) pending-status
            @elseif($refund->status == 4) denied-status unpaid-status status
            @else unpaid-status @endif">
            @if ($refund->status == 2)
             <span class="status-text">  Pending </span>
            @elseif($refund->status == 3)
               <span class="status-text"> Refunded </span>
            @elseif($refund->status == 4)
                <span class="status-text"> Denied </span>
            @else
                $refund->status_text
            @endif
        </td>

        {{-- ACTION --}}
        <td data-label="ACTION">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton{{ $refund->id }}"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ $refund->id }}">
                    @if ($refund->status == 2)
                        <li>
                            <button class="dropdown-item approve-refund fs-14 regular" type="button"
                                data-booking-id="{{ $refund->id }}">
                                <i class="bi bi-check-circle complete-icon"></i>
                                Approve Refund
                            </button>
                        </li>
                        <li>
                            <button class="dropdown-item deny-refund fs-14 regular" type="button"
                                data-booking-id="{{ $refund->id }}">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Deny Refund
                            </button>
                        </li>
                    @endif
                    @if ($refund->id)
                        <li>
                            <a class="dropdown-item view fs-14 regular"
                                href="{{ route('refund_request.detail', $refund->id) }}">
                                <i class="bi bi-eye view-icon"></i>
                                View Details
                            </a>
                        </li>
                    @endif
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="9" class="text-center">No Refund Requests Found</td>
    </tr>
@endforelse
