@forelse ($refunds as $refund)
    <tr>
        <td data-label="Professional Name">
            {{ $refund->provider->name ?? '' }}
        </td>
        <td data-label="Service">{{ $refund->service->name ?? '' }}
        </td>
        <td data-label="Amount">${{ $refund->total_amount ?? '' }}</td>
        <td data-label="Status" class="paid-status status"> <span class="status-text"> Refunded </span></td>
        <td data-label="Date">{{ $refund->booking_date }} -
            {{ \Carbon\Carbon::createFromFormat('H:i:s', $refund->booking_time)->format('H:i') }}</td>
    </tr>
@empty
    <tr>
        <td colspan="5" class="text-center">No Refunds Found</td>
    </tr>
@endforelse
