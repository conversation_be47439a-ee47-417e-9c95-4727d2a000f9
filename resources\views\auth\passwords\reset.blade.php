@extends('layouts.app')
@section('content')
    <div class="container-fluid reset-pass">
        <div class="row">
            <div class="col-md-6  d-flex justify-content-center align-items-center">
                <div class="card w-md-75 w-sm-100">
                    <h4 class="card-header fs-20 py-8 fw-semibold">{{ __('Reset Password') }}</h4>

                    <div class="card-body">
                        <form method="POST" action="{{ route('password.update') }}">
                            @csrf

                            <input type="hidden" name="token" value="{{ $token }}">

                            <div class="row mb-3">
                                <label for="email"
                                    class="col-md-4 col-form-label text-md-end">{{ __('Email Address') }}</label>

                                <div class="col-md-6">
                                    <input id="email" type="email"
                                        class="form-control @error('email') is-invalid @enderror" name="email"
                                        value="{{ $email ?? old('email') }}" required autocomplete="email" autofocus
                                        readonly>
                                    @error('email')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="password"
                                    class="col-md-4 col-form-label text-md-end">{{ __('Password') }}</label>

                                <div class="col-md-6 position-relative">
                                    <input id="password" type="password"
                                        class="form-control" name="password"
                                        required autocomplete="new-password">

                                    @error('password')
                                        <span class="invalid-feedback" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror

                                    <span id="toggle-password"
                                        class="btn-sm btn-icon position-absolute translate-middle mb-8 end-0 pb-12 pe-4">
                                        <i class="fa-solid fa-eye"></i>
                                        <i class="fa-solid fa-eye-slash d-none"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <label for="password-confirm"
                                    class="col-md-4 col-form-label text-md-end">{{ __('Confirm Password') }}</label>

                                <div class="col-md-6 position-relative">
                                    <input id="password-confirm" type="password" class="form-control"
                                        name="password_confirmation" required autocomplete="new-password">

                                    <span id="toggle-password-confirm"
                                        class="btn-sm btn-icon position-absolute translate-middle mb-8 end-0 pb-12 pe-4">
                                        <i class="fa-solid fa-eye"></i>
                                        <i class="fa-solid fa-eye-slash d-none"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6 offset-md-4">
                                    <button type="submit" class="blue-btn">
                                        {{ __('Reset Password') }}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6 login-side-image">
                <img src="{{ asset('website') }}/assets/images/login-banner.png" alt="icon">
            </div>
        </div>
    </div>
    @push('scripts')
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
        <script>
            $(document).ready(function() {
                $('#toggle-password').on('click', function() {
                    var passwordField = $('#password');
                    var passwordFieldType = passwordField.attr('type');

                    if (passwordFieldType === 'password') {
                        passwordField.attr('type', 'text');
                        $(this).find('.fa-eye-slash').removeClass('d-none');
                        $(this).find('.fa-eye').addClass('d-none');
                    } else {
                        passwordField.attr('type', 'password');
                        $(this).find('.fa-eye').removeClass('d-none');
                        $(this).find('.fa-eye-slash').addClass('d-none');
                    }
                });

            });

            $(document).ready(function() {
                $('#toggle-password-confirm').on('click', function() {
                    var passwordField = $('#password-confirm');
                    var passwordFieldType = passwordField.attr('type');

                    if (passwordFieldType === 'password') {
                        passwordField.attr('type', 'text');
                        $(this).find('.fa-eye-slash').removeClass('d-none');
                        $(this).find('.fa-eye').addClass('d-none');
                    } else {
                        passwordField.attr('type', 'password');
                        $(this).find('.fa-eye').removeClass('d-none');
                        $(this).find('.fa-eye-slash').addClass('d-none');
                    }
                });

            });
        </script>
        <script>
            $(document).ready(function() {
                // Check for validation errors and show SweetAlert
                @if ($errors->any())
                    Swal.fire({
                        title: "Error",
                        text: "{{ $errors->first() }}",
                        icon: "error"
                    });
                @endif
            });
        </script>
    @endpush
@endsection
