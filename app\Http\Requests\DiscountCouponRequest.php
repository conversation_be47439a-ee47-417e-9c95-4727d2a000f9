<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DiscountCouponRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'sometimes|string|max:100',
            'ids' => 'sometimes|string|max:255',
            'coupon_code' => 'sometimes|string|max:50',
            'discount' => 'sometimes|integer',
            'user_limit' => 'sometimes|integer',
            'applies_to' => 'sometimes|in:service,category',
            'discount_type' => 'sometimes|in:service,subscription',
            'type' => 'sometimes|in:amount,percentage',
            'user_id' => 'sometimes|exists:users,id',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date',
            'status' => 'sometimes|integer|in:0,1',
        ];
    }
}
