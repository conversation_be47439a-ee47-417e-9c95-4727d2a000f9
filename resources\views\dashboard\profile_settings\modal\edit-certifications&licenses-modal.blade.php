<div class="modal fade card-details edit-certificationsmodal" id="edit-certifications&licenses-modal"
    aria-labelledby="edit-certifications&licenses-modal" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0" id="modal-title">Edit Certifications & Licenses</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="edit-certifications&licenses-form" action="{{ route('certificates.save') }}" method="POST"
                enctype="multipart/form-data">
                @csrf
                <div class="modal-body share-certificates">
                    <div class="row row-gap-5">
                        @if (auth()->user()->certificates->count() == 0)
                            <div class="gray-card mt-5 file-upload-group">
                                <div class="row">
                                    <h6 class="mb-8 text-center"> Certificate #1 </h6>
                                    <div class="col-md-12 mb-4">
                                        <label class="fieldlabels">Certification Title*</label>
                                        <input class="w-100" type="text" name="certificates[0][title]"
                                            placeholder="Enter certification title">
                                    </div>

                                    <div class="col-md-12 mb-4">
                                        <label class="fieldlabels">Issued by*</label>
                                        <input class="w-100" type="text" name="certificates[0][issued_by]"
                                            placeholder="Enter name">
                                    </div>

                                    <div class="col-md-6">
                                        <label class="fieldlabels">Issued Date*</label>
                                        <input class="w-100 issued-date" type="date"
                                            name="certificates[0][issued_date]" placeholder="Enter certification title"
                                            data-index="0">
                                        <label class="error text-danger" style="display: none;"></label>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="fieldlabels">End Date*</label>
                                        <input class="w-100 end-date" type="date" name="certificates[0][end_date]"
                                            placeholder="Enter certification title" data-index="0" disabled
                                            style="background-color: #f8f9fa; color: #6c757d;" novalidate>
                                        <label class="error text-danger" style="display: none;"></label>
                                    </div>
                                    <div class="col-md-12 form-border">
                                        <p class="manrope fw-600 light-black">Share Certificates</p>
                                        <div class="file-upload-group">
                                            <div class="file-upload-group">
                                                <label class="upload-box">
                                                   
                                                    <img src="{{ asset('website/assets/images/upload.svg') }}"
                                                                alt="Upload Icon">
                                                    
                                                    <p>Upload Certificate</p>
                                                    <p class="mb-0">Maximum file size: 2 MB</p>
                                                    <p>Supported format: JPG and PNG</p>
                                                    <span class="add-file">
                                                        <p class="upload-cert-btn no_validate">Upload</p>
                                                    </span>
                                                    <input type="file" name="certificates[0][image]"
                                                        class="file-input no_validate" hidden="">
                                                </label>
                                                <div class="preview-container"></div>
                                            </div>
                                        </div>
                                        <div class="exception-checkbox">
                                            <label class="cert-excep">
                                                <input type="checkbox" name="certificates[0][exception]"
                                                    id="exceptionToggle">
                                                <span class="">Certificate Exception</span>
                                            </label>

                                            <div class="exception-textarea">
                                                <label class="mb-2 w-100 bold" for="w3review">Reason for
                                                    Exception</label>
                                                <textarea class="mb-0 no_validate w-100" id="w3review" name="certificates[0][exception_reason]" rows="4"
                                                    cols="50" placeholder="Write reason for exception"></textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-3 d-flex justify-content-between">
                                        <button type="button" class="delete-block">Delete This
                                            Block</button>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="col-md-12">
                            <div id="certifications-wrapper">
                                @foreach (auth()->user()->certificates ?? [] as $index => $certificate)
                                    <div class=" gray-card my-5 file-upload-group">
                                        <h6 class="mb-8">Certificate #1 </h6>
                                        <!-- Hidden field for certificate ID -->
                                        <input type="hidden" name="certificates[{{ $index }}][id]" value="{{ $certificate->id }}">
                                        <div class="row">
                                            <div class="col-md-12 mb-4">
                                                <label class="fieldlabels"> Certification Title*</label>
                                                <input class="w-100" type="text"
                                                    name="certificates[{{ $index }}][title]"
                                                    placeholder="Enter certification title"
                                                    value="{{ $certificate->title }}">
                                            </div>

                                            <div class="col-md-12 mb-4">
                                                <label class="fieldlabels">Issued by*</label>
                                                <input class="no_validate w-100" type="text"
                                                    name="certificates[{{ $index }}][issued_by]"
                                                    value="{{ $certificate->issued_by }}" placeholder="Enter name">
                                            </div>

                                            <div class="col-md-6">
                                                <label class="fieldlabels">Issued Date*</label>
                                                <input class="no_validate w-100 issued-date" type="date"
                                                    name="certificates[{{ $index }}][issued_date]"
                                                    value="{{ $certificate->issued_date }}"
                                                    placeholder="Enter issued date" data-index="{{ $index }}">
                                            </div>

                                            <div class="col-md-6">
                                                <label class="fieldlabels">End Date*</label>
                                                <input class="no_validate w-100 end-date" type="date"
                                                    name="certificates[{{ $index }}][end_date]"
                                                    value="{{ $certificate->end_date }}" placeholder="Enter end date"
                                                    data-index="{{ $index }}" novalidate>
                                            </div>

                                            <div class="col-md-12 form-border">
                                                <p class="manrope fw-600 light-black">Share Certificates</p>
                                                <div class="share-certificates">
                                                    <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans"
                                                        style="cursor:pointer;">
                                                        @if ($certificate->image)
                                                            <img src="{{ asset('website') . '/' . $certificate->image }}"
                                                                alt="Certificate Image"
                                                                style="max-width: 100px; max-height: 100px; object-fit: contain;"
                                                                onerror="this.src='{{ asset('website/assets/images/upload.svg') }}'">
                                                        @else
                                                            <img src="{{ asset('website/assets/images/upload.svg') }}"
                                                                alt="Upload Icon">
                                                        @endif
                                                        <p>Upload Certificate</p>
                                                        <p class="mb-0">Maximum file size: 2 MB</p>
                                                        <p>Supported format: JPG and PNG</p>
                                                        <span class="add-file">
                                                            <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                                        </span>
                                                        <input class="no_validate"
                                                            name="certificates[{{ $index }}][image]"
                                                            type="file" hidden="">
                                                        @if ($certificate->image)
                                                            <input type="hidden"
                                                                name="certificates[{{ $index }}][old_image]"
                                                                value="{{ $certificate->image }}">
                                                        @endif
                                                    </label>
                                                </div>
                                                <div class="preview-container">
                                                    @if ($certificate->image)
                                                        <div class="preview-box existing-image">
                                                            <img src="{{ asset('website') . '/' . $certificate->image }}"
                                                                alt="Certificate"
                                                                onerror="this.parentElement.style.display='none'">
                                                            <button type="button" class="remove-image"
                                                                onclick="removeExistingImage(this, '{{ $index }}')">×</button>
                                                        </div>
                                                    @endif
                                                </div>

                                                <div class="exception-checkbox mt-5">
                                                    <label class="cert-excep">
                                                        <input class="no_validate" type="checkbox"
                                                            id="exceptionToggle"
                                                            name="certificates[{{ $index }}][exception]"
                                                            {{ $certificate->exception ? 'checked' : '' }}>
                                                        <span class="checkmark">Certificate Exception</span>
                                                    </label>

                                                    <div class="exception-textarea">
                                                        <label class="mb-2" for="w3review_1">Reason for
                                                            Exception</label>
                                                        <textarea class="mb-0 no_validate w-100" id="w3review_1" name="certificates[{{ $index }}][exception_reason]"
                                                            rows="4" cols="50" placeholder="Write reason for exception"> {{ $certificate->exception_reason }}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mt-3 d-flex justify-content-between">
                                                <button type="button" class="delete-block">Delete This Block</button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <button type="button" id="addMoreBtn" class="addMoreBtn blue-text mt-3">
                                <span><i class="fas fa-plus"></i></span> Add More
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="trans-button" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    // Wait for jQuery to be available
    function waitForJQuery(callback) {
        if (typeof jQuery !== 'undefined') {
            callback();
        } else {
            setTimeout(function() {
                waitForJQuery(callback);
            }, 50);
        }
    }

    // Initialize when jQuery is ready
    waitForJQuery(function() {
        $(document).ready(function() {
        // Function to handle issued date changes and control end date field
        function handleIssuedDateChange(issuedDateInput) {
            const $issuedInput = $(issuedDateInput);
            const index = $issuedInput.data('index');
            const $endDateInput = $(`input[name="certificates[${index}][end_date]"]`);
            const issuedDate = $issuedInput.val();

            if (issuedDate) {
                // Enable end date field and restore normal styling
                $endDateInput.prop('disabled', false);
                $endDateInput.removeClass('disabled-field');
                $endDateInput.css({
                    'background-color': '#ffffff',
                    'color': '#495057',
                    'cursor': 'text'
                });

                // COMBINED APPROACH: Set min attribute AND JavaScript validation
                const issueDateParts = issuedDate.split('-');
                const year = parseInt(issueDateParts[0]);
                const month = parseInt(issueDateParts[1]) - 1; // Month is 0-indexed
                const day = parseInt(issueDateParts[2]);

                // Create date object for TWO days after issue date to ensure issue date is disabled
                const dayAfterIssue = new Date(year, month, day + 2);
                const minDateString = dayAfterIssue.toISOString().split('T')[0];

                // Set min attribute to disable issue date and earlier dates in dropdown
                $endDateInput.attr('min', minDateString);
                $endDateInput[0].setAttribute('min', minDateString);

                // ALSO store issue date for JavaScript validation as backup
                $endDateInput.data('issue-date', issuedDate);

                console.log('Issue Date:', issuedDate);
                console.log('Min Date Set (day after issue):', minDateString);
                console.log('This disables issue date (' + issuedDate + ') and all earlier dates in dropdown');

                // Allow manual typing to override min restriction
                $endDateInput.get(0).setCustomValidity('');

                // Validate end date if it exists
                const currentEndDate = $endDateInput.val();
                if (currentEndDate && currentEndDate <= issuedDate) {
                    $endDateInput.next('.error').text('End date must be after issued date').show();
                } else {
                    $endDateInput.next('.error').text('').hide();
                }
            } else {
                // Disable and gray out end date field when no issued date is set
                $endDateInput.prop('disabled', true);
                $endDateInput.addClass('disabled-field');
                $endDateInput.css({
                    'background-color': '#e9ecef',
                    'color': '#6c757d',
                    'cursor': 'not-allowed'
                });
                $endDateInput.val('');
                $endDateInput.removeAttr('min');
                $endDateInput.next('.error').text('').hide();
            }
        }

        // Handle issued date changes for all certificates
        $(document).on('change', 'input[name*="[issued_date]"]', function() {
            handleIssuedDateChange(this);
        });

        // Initialize existing certificates when modal opens (for edit mode)
        $('#edit-certifications&licenses-modal').on('shown.bs.modal', function() {
            // Check all existing issued date fields and initialize their end date fields
            $('input[name*="[issued_date]"]').each(function() {
                const issuedDate = $(this).val();
                if (issuedDate) {
                    // If issue date exists, enable and configure end date field
                    handleIssuedDateChange(this);
                }
            });
        });

        // Prevent any interference while typing in end date fields
        $(document).on('input keyup keydown keypress', 'input[name*="[end_date]"]', function(e) {
            // Allow all typing - don't interfere with user input
            e.stopPropagation();
        });

        // Disable keyboard input for BOTH issue date and end date - only allow dropdown selection
        $(document).on('keydown keypress keyup input',
            'input[name*="[issued_date]"], input[name*="[end_date]"]',
            function(e) {
                e.preventDefault(); // Prevents all keyboard input
                return false;
            });

        // ADDITIONAL APPROACH: Monitor for any value changes and validate immediately
        $(document).on('input propertychange paste', 'input[name*="[issued_date]"], input[name*="[end_date]"]',
            function() {
                // Block any manual input attempts
                const $input = $(this);
                const inputName = $input.attr('name');

                if (inputName && inputName.includes('end_date')) {
                    const endDate = $input.val();
                    const storedIssueDate = $input.data('issue-date');

                    if (storedIssueDate && endDate && endDate <= storedIssueDate) {
                        // Immediately clear invalid selections
                        $input.val('');
                        $input.next('.error').text('Issue date (' + storedIssueDate +
                            ') and all earlier dates are blocked!').show();
                    } else {
                        $input.next('.error').text('').hide();
                    }
                }
            });

        // STRICT VALIDATION: Block any date selection up to and including issue date
        $(document).on('change input', 'input[name*="[end_date]"]', function() {
            const $endInput = $(this);
            const endDate = $endInput.val();
            const storedIssueDate = $endInput.data('issue-date');

            console.log('End date selected:', endDate);
            console.log('Stored issue date:', storedIssueDate);

            if (storedIssueDate && endDate) {
                // Convert both dates to Date objects for accurate comparison
                const issueDateObj = new Date(storedIssueDate);
                const endDateObj = new Date(endDate);

                console.log('Comparing:', endDateObj, '<=', issueDateObj);

                if (endDateObj <= issueDateObj) {
                    console.log('BLOCKED: End date is same or before issue date');
                    // BLOCK the selection - clear the field and show error
                    $endInput.val('');
                    $endInput.next('.error').text('Cannot select issue date (' + storedIssueDate +
                        ') or any earlier date. Please select a date AFTER the issue date.').show();

                    // Focus back to the field to force user to select a valid date
                    setTimeout(() => {
                        $endInput.focus();
                    }, 100);
                } else {
                    console.log('ALLOWED: End date is after issue date');
                    $endInput.next('.error').text('').hide();
                }
            } else {
                // Clear error if no issue date is set
                $endInput.next('.error').text('').hide();
            }
            }
        });

        // Prevent focus to disable typing completely
        $(document).on('focus', 'input[name*="[end_date]"]', function() {
            $(this).blur(); // Remove focus to prevent typing
        });

        // Prevent focus on disabled end date field to stop typing completely
        $(document).on('focus', 'input[name*="[end_date]"]', function() {
            $(this).blur(); // Remove focus to prevent typing
        });

        // Initialize all certificate fields when modal is opened
        $('#edit-certifications\\&licenses-modal').on('shown.bs.modal', function() {
            // Initialize existing certificates - this handles both enabling/disabling end dates
            $('input[name*="[issued_date]"]').each(function() {
                handleIssuedDateChange(this);
            });
        });

        // Handle dynamically added certificates (Add More button)
        $(document).on('click', '#addMoreBtn', function() {
            setTimeout(function() {
                const $newIssuedInputs = $('input[name*="[issued_date]"]').not(
                    '[data-initialized="true"]');
                const $newEndInputs = $('input[name*="[end_date]"]').not(
                    '[data-initialized="true"]');

                // Mark as initialized and set up new issued date fields
                $newIssuedInputs.each(function() {
                    $(this).attr('data-initialized', 'true');
                    handleIssuedDateChange(this);
                });

                // Mark as initialized and disable new end date fields
                $newEndInputs.each(function() {
                    const $endInput = $(this);
                    $endInput.attr('data-initialized', 'true');
                    $endInput.prop('disabled', true);
                    $endInput.addClass('disabled-field');
                    $endInput.css({
                        'background-color': '#e9ecef',
                        'color': '#6c757d',
                        'cursor': 'not-allowed'
                    });
                });
            }, 200);
        });

        // Form validation on submit
        $('#edit-certifications\\&licenses-form').on('submit', function(e) {
            let isValid = true;
            let errorMessage = '';

            // Clear all previous error messages first
            $('.error').text('').hide();

            $('input[name*="[issued_date]"]').each(function() {
                const $issuedInput = $(this);
                const index = $issuedInput.data('index');
                const $endInput = $(`input[name="certificates[${index}][end_date]"]`);
                const issuedDate = $issuedInput.val();
                const endDate = $endInput.val();

                // Check if both dates are provided
                if (issuedDate && endDate) {
                    if (endDate <= issuedDate) {
                        isValid = false;
                        errorMessage = 'End date must be after issued date';
                        $endInput.next('.error').text(errorMessage).show();
                        $endInput.focus();
                    } else {
                        $endInput.next('.error').text('').hide();
                    }
                } else if (issuedDate && !endDate) {
                    isValid = false;
                    errorMessage = 'End date is required when issued date is provided';
                    $endInput.next('.error').text(errorMessage).show();
                    $endInput.focus();
                }
            });

            if (!isValid) {
                e.preventDefault();
                // Show alert with error message
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: errorMessage,
                        confirmButtonColor: '#006AA0'
                    });
                } else {
                    alert(errorMessage);
                }
                return false;
            }
        });

        // Handle certificate exception checkbox
        $(document).on('change', 'input[name*="[exception]"]', function() {
            const $checkbox = $(this);
            const $exceptionContainer = $checkbox.closest('.exception-checkbox');
            const $textarea = $exceptionContainer.find('.exception-textarea');
            const $uploadBox = $(this).closest('.gray-card').find('.upload-box');
            const $group = $(this).closest('.gray-card');
            const $previewArea = $group.find('.preview-container');

            if ($checkbox.is(':checked')) {
                // Show textarea with expand effect
                $textarea.show();
                $exceptionContainer.addClass('expanded');
                $uploadBox.addClass('disabled');

                // Mark existing images for deletion when exception is checked
                const $oldImageInput = $group.find('input[name*="[old_image]"]');
                if ($oldImageInput.length && $oldImageInput.val()) {
                    // Get the index from the input name
                    const inputName = $oldImageInput.attr('name');
                    const indexMatch = inputName.match(/certificates\[(\d+)\]/);
                    if (indexMatch) {
                        const index = indexMatch[1];
                        // Clear old image value
                        $oldImageInput.val('');
                        // Add delete flag
                        if (!$group.find(`input[name="certificates[${index}][delete_image]"]`).length) {
                            $group.append(
                                `<input type="hidden" name="certificates[${index}][delete_image]" value="1">`
                            );
                        }
                    }
                }

                // Remove all preview images (both existing and newly uploaded)
                $previewArea.find('.preview-box').remove();
                $previewArea.empty();

                // Clear file input
                $group.find('input[type="file"]').val('');
            } else {
                // Hide textarea
                $textarea.hide();
                $exceptionContainer.removeClass('expanded');
                $textarea.find('textarea').val('');
                $uploadBox.removeClass('disabled');

                // Remove delete flag when unchecking exception
                $group.find('input[name*="[delete_image]"]').remove();
            }
        });

        // Initialize exception textarea visibility on modal open
        $('#edit-certifications\\&licenses-modal').on('shown.bs.modal', function() {
            $('input[name*="[exception]"]').each(function() {
                const $checkbox = $(this);
                const $exceptionContainer = $checkbox.closest('.exception-checkbox');
                const $textarea = $exceptionContainer.find('.exception-textarea');
                const $uploadBox = $checkbox.closest('.gray-card').find('.upload-box');

                if ($checkbox.is(':checked')) {
                    $textarea.show();
                    $exceptionContainer.addClass('expanded');
                    $uploadBox.addClass('disabled');
                } else {
                    $textarea.hide();
                    $exceptionContainer.removeClass('expanded');
                    $uploadBox.removeClass('disabled');
                }
            });
        });

        // Add CSS for disabled field styling
        if (!$('#certificate-date-styles').length) {
            $('<style id="certificate-date-styles">')
                .text(`
                .disabled-field {
                    background-color: #e9ecef !important;
                    color: #6c757d !important;
                    cursor: not-allowed !important;
                    opacity: 0.7;
                }
                .disabled-field:focus {
                    box-shadow: none !important;
                    border-color: #ced4da !important;
                }
                input[name*="[end_date]"]:disabled {
                    background-color: #e9ecef !important;
                    color: #6c757d !important;
                }
                .upload-box.disabled {
                    opacity: 0.5;
                    pointer-events: none;
                }
                .exception-textarea {
                    display: none;
                }
                .exception-checkbox.expanded .exception-textarea {
                    display: block;
                }
            `)
                .appendTo('head');
        }
        });
    });
</script>

</div>
