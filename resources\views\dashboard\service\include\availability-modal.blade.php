
<style>
    .service-availibility-calendar .modal-dialog {
        max-width: 500px;
    }

    .service-availibility-calendar .modal-content {
        border-radius: 12px;
        border: none;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    }

    .service-availibility-calendar .modal-header {
        border-bottom: none;
        padding: 24px 24px 0;
    }

    .service-availibility-calendar .modal-body {
        padding: 20px 24px;
    }

    .service-availibility-calendar .modal-footer {
        border-top: none;
        padding: 0 24px 24px;
        justify-content: space-between;
    }

    .service-availibility-calendar .day-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        margin-bottom: 8px;
    }

    .service-availibility-calendar .day-row .day-label {
        font-weight: 500;
        color: #333;
        margin: 0;
        flex: 1;
    }

    .service-availibility-calendar .day-row .day-status {
        color: #6c757d;
        font-size: 14px;
    }

    .service-availibility-calendar .form-check-input {
        margin-right: 12px;
        margin-top: 3px;
    }

    .service-availibility-calendar .btn-link {
        color: #6c757d;
        text-decoration: none;
    }

    .service-availibility-calendar .btn-link:hover {
        color: #495057;
    }

    .service-availibility-calendar .time-inputs {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .service-availibility-calendar .time-inputs select {
        width: 60px;
        font-size: 12px;
        padding: 4px 6px;
        text-align: center;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
    }

    .service-availibility-calendar .time-inputs select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .service-availibility-calendar .time-inputs .time-group {
        display: flex;
        align-items: center;
        gap: 2px;
    }

    .service-availibility-calendar .time-inputs .time-separator {
        font-weight: bold;
        color: #666;
        margin: 0 2px;
    }

    /* Legacy support for text inputs */
    .service-availibility-calendar .time-inputs input[type="text"] {
        width: 80px;
        font-size: 12px;
        padding: 4px 8px;
        text-align: center;
        border: 1px solid #ced4da;
        border-radius: 4px;
    }

    .service-availibility-calendar .time-inputs input[type="text"]:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .service-availibility-calendar #customWeeksInput input {
        border-radius: 6px;
        border: 1px solid #ced4da;
    }

    .form-check .recurring{ margin-left: -11px;}

    .service-availibility-calendar .modal-dialog {  max-width: 600px;}
</style>

<div class="modal fade service-availibility-calendar" id="availabilityModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content p-3">
            <div class="modal-header">
                <h5 class="modal-title fs-18 ">Availability</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="availability-calendar form-add-services">
                    <!-- Week Navigation -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <button type="button" class="btn btn-link p-0" id="prevWeek">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <h6 class="mb-0 fw-bold fs-18" id="weekRange">11 Aug 2025 - 17 Aug 2025</h6>
                        <button type="button" class="btn btn-link p-0" id="nextWeek">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>

                    <!-- Staff Selection for Business Role -->
                    @if(auth()->check() && auth()->user()->hasRole('business'))
                    <div class="staff-selection-section mb-4" id="staffSelectionSection" style="display: block;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0 fw-bold fs-16">Staff Member</h6>
                            <div class="staff-navigation" id="staffNavigation" style="display: none;">
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="prevStaff">
                                    <i class="fas fa-chevron-left"></i>
                                </button>
                                <span class="mx-2" id="staffCounter">1 of 1</span>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="nextStaff">
                                    <i class="fas fa-chevron-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="staff-info" id="staffInfo" style="display: none;">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                <span id="staffInfoText">Please assign staff members to this service first</span>
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Days Container -->
                    <div id="weekDaysContainer" class="mb-4">
                        <!-- Default days structure -->
                        <div class="day-row" data-day="Monday" data-date="">
                            <div class="d-flex align-items-center">
                                <input  id="day-mon" type="checkbox" class="form-check-input day-checkbox" data-day="Monday">
                                <label for="day-mon" class="day-label">Monday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Tuesday" data-date="">
                            <div class="d-flex align-items-center">
                                <input id="day-tues" type="checkbox" class="form-check-input day-checkbox" data-day="Tuesday">
                                <label for="day-tues" class="day-label">Tuesday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Wednesday" data-date="">
                            <div class="d-flex align-items-center">
                                <input id="day-wednes" type="checkbox" class="form-check-input day-checkbox" data-day="Wednesday">
                                <label for="day-wednes" class="day-label">Wednesday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Thursday" data-date="">
                            <div class="d-flex align-items-center">
                                <input id="day-thurs" type="checkbox" class="form-check-input day-checkbox" data-day="Thursday">
                                <label for="day-thurs" class="day-label">Thursday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Friday" data-date="">
                            <div class="d-flex align-items-center">
                                <input id="day-fri" type="checkbox" class="form-check-input day-checkbox" data-day="Friday">
                                <label for="day-fri" class="day-label">Friday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Saturday" data-date="">
                            <div class="d-flex align-items-center">
                                <input id="day-sat" type="checkbox" class="form-check-input day-checkbox" data-day="Saturday">
                                <label for="day-sat" class="day-label">Saturday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                        <div class="day-row" data-day="Sunday" data-date="">
                            <div class="d-flex align-items-center">
                                <input id="day-sun" type="checkbox" class="form-check-input day-checkbox" data-day="Sunday">
                                <label for="day-sun" class="day-label">Sunday</label>
                            </div>
                            <span class="day-status">Closed</span>
                        </div>
                    </div>

                    <!-- Recurring Checkbox -->
                    <div class="d-flex justify-content-between mb-4 w-100">
                        <label class="form-check">
                            <input class="form-check-input" type="checkbox" id="recurringCheckbox">
                            <span class="form-check-label recurring">Recurring</span>
                        </label>

                        <!-- Recurring Options (Hidden by default) -->
                        <div id="recurringOptions" class="w-md-75 w-sm-100  mb-4" style="display: none;">
                            <div class="d-flex flex-wrap flex-end gap-3 ms-4">
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="recurringWeeks"
                                        id="recurring4weeks" value="4">
                                    <label class="form-check-label" for="recurring4weeks">
                                        4 weeks
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="recurringWeeks"
                                        id="recurring8weeks" value="8">
                                    <label class="form-check-label" for="recurring8weeks">
                                        8 weeks
                                    </label>
                                </div>
                                <div class="form-check mb-2">
                                    <input class="form-check-input" type="radio" name="recurringWeeks"
                                        id="recurringCustom" value="custom">
                                    <label class="form-check-label" for="recurringCustom">
                                        Custom
                                    </label>
                                </div>
                                <div id="customWeeksInput" class="mt-2" style="display: none;">
                                    <div class="d-flex align-items-center gap-2">
                                        <input type="number" class="form-control form-control-sm" id="customWeeksNumber"
                                            placeholder="Enter weeks" min="1" max="52" style="width: 150px;">
                                        <button type="button" class="save-btn btn-sm"
                                            id="applyCustomWeeks">Apply</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>



                    <!-- JSON Output (Hidden) -->
                    <textarea id="jsonOutput" name="availability_data" style="display: none;"></textarea>
                </div>
            </div>
            <div class="modal-footer d-flex justify-content-end gap-3">
                <button type="button" class="cancel-btn" id="cancelAvailability">Cancel</button>
                <button type="button" class="save-btn" id="saveAvailability">Done</button>
            </div>
        </div>
    </div>
</div>

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Wait for jQuery and moment.js to be available
        $(document).ready(function () {
            let isDone = false;
            // Check if moment.js is available
            if (typeof moment === 'undefined') {
                console.error('Moment.js is not loaded');
                return;
            }

            // Wrap everything in IIFE to prevent variable conflicts
            (function () {
                const weekData = {};
                let currentWeekIndex = 0;
                const baseStartDate = moment().startOf('isoWeek'); // This will set the start date to the current week's Monday
                
                // Staff-related variables for business role
                let assignedStaff = @json(isset($service) && $service->staff ? $service->staff : []);
                let currentStaffIndex = 0;
                let staffAvailabilityData = {}; // Store availability data per staff member
                let userHasSetRecurring = false; // Track if user has manually set recurring settings

                // Staff-related functions for business role (moved to top to avoid hoisting issues)
                // Function to toggle all availability-related inputs
                const toggleAvailabilityInputs = (enable) => {
                    $('.day-checkbox').prop('disabled', !enable);
                    $('.time-input').prop('disabled', !enable);
                    $('#recurringCheckbox').prop('disabled', !enable);
                    $('input[name="recurringWeeks"]').prop('disabled', !enable);
                    $('#customWeeksNumber').prop('disabled', !enable);
                    $('#prevWeek, #nextWeek').prop('disabled', !enable);
                    $('#saveAvailability').prop('disabled', !enable);
                };

                // Function to clear all availability data
                const clearAllAvailabilityData = () => {
                    // Clear week data
                    Object.keys(weekData).forEach(key => {
                        delete weekData[key];
                    });
                    
                    // Clear staff availability data
                    staffAvailabilityData = {};
                    
                    // Reset UI
                    updateWeekUI();
                    updateJsonOutput();
                    
                    // Only reset recurring options if no days are selected
                    // This prevents resetting when user is actively working
                    if (!hasSelectedDays()) {
                        $('#recurringCheckbox').prop('checked', false);
                        $('#recurringOptions').hide();
                        $('#customWeeksInput').hide();
                        $('input[name="recurringWeeks"]').prop('checked', false);
                        $('#customWeeksNumber').val('');
                    }
                    
                    console.log('All availability data cleared');
                };

                const initializeStaffSection = () => {
                    // Always set the correct text immediately to prevent flicker
                    if (assignedStaff.length === 0) {
                        $('#staffSelectionSection').show();
                        $('#staffInfo').show();
                        $('#staffInfoText').html('<i class="fas fa-exclamation-triangle me-2 text-warning"></i><strong>Please assign staff members to this service first</strong><br><small class="text-muted">Go back to the form and select staff members before setting availability.</small>');
                        $('#staffNavigation').hide();
                        toggleAvailabilityInputs(false);
                        return;
                    }

                    $('#staffSelectionSection').show();
                    $('#staffInfo').show();
                    updateStaffInfo();
                    updateStaffNavigation();
                    toggleAvailabilityInputs(true);
                };

                const updateStaffInfo = () => {
                    if (assignedStaff.length === 0) {
                        $('#staffInfoText').text('Please assign staff members to this service first');
                        return;
                    }

                    // Check if currentStaffIndex is within bounds
                    if (currentStaffIndex >= assignedStaff.length) {
                        // Reset to last available staff if index is out of bounds
                        currentStaffIndex = Math.max(0, assignedStaff.length - 1);
                    }

                    const currentStaff = assignedStaff[currentStaffIndex];
                    if (!currentStaff) {
                        $('#staffInfoText').text('Please assign staff members to this service first');
                        return;
                    }

                    const staffName = currentStaff.name || 'Unknown Staff';
                    $('#staffInfoText').text('Setting availability for: ' + staffName);
                };

                const updateStaffNavigation = () => {
                    if (assignedStaff.length <= 1) {
                        $('#staffNavigation').hide();
                        return;
                    }

                    // Check if currentStaffIndex is within bounds
                    if (currentStaffIndex >= assignedStaff.length) {
                        currentStaffIndex = Math.max(0, assignedStaff.length - 1);
                    }

                    $('#staffNavigation').show();
                    $('#staffCounter').text((currentStaffIndex + 1) + ' of ' + assignedStaff.length);
                    $('#prevStaff').prop('disabled', currentStaffIndex === 0);
                    $('#nextStaff').prop('disabled', currentStaffIndex === assignedStaff.length - 1);
                };

                const switchToStaff = (staffIndex) => {
                    if (staffIndex < 0 || staffIndex >= assignedStaff.length) return;

                    console.log('Switching from staff', currentStaffIndex, 'to staff', staffIndex);

                    // Save current staff's data
                    saveCurrentStaffData();

                    // Switch to new staff
                    currentStaffIndex = staffIndex;
                    updateStaffInfo();
                    updateStaffNavigation();

                    // Reset recurring flag when switching staff
                    userHasSetRecurring = false;

                    // Load new staff's data
                    loadStaffData();
                    
                    console.log('Switched to staff:', assignedStaff[currentStaffIndex].name, 'ID:', assignedStaff[currentStaffIndex].id);
                };

                const saveCurrentStaffData = () => {
                    if (assignedStaff.length === 0) return;

                    const currentStaff = assignedStaff[currentStaffIndex];
                    const staffId = currentStaff.id;
                    
                    // Save current week data for this staff
                    const weekKey = baseStartDate.clone().add(currentWeekIndex * 7, "days").format("YYYY-MM-DD");
                    console.log('Saving data for staff:', currentStaff.name, 'ID:', staffId, 'Week:', weekKey);
                    console.log('Current weekData to save:', weekData[weekKey]);
                    
                    if (!staffAvailabilityData[staffId]) {
                        staffAvailabilityData[staffId] = {};
                    }
                    
                    // Create a deep copy to prevent reference issues between staff
                    if (weekData[weekKey]) {
                        staffAvailabilityData[staffId][weekKey] = JSON.parse(JSON.stringify(weekData[weekKey]));
                        console.log('Saved week data for staff:', staffId, staffAvailabilityData[staffId][weekKey]);
                    } else {
                        console.log('No week data to save for week:', weekKey);
                    }
                    
                    // Only update recurring settings if they don't exist for this staff
                    // This preserves the original recurring settings detected from the database
                    if (!staffAvailabilityData[staffId].recurring) {
                        staffAvailabilityData[staffId].recurring = {
                            enabled: $('#recurringCheckbox').is(':checked'),
                            weeks: $('input[name="recurringWeeks"]:checked').val(),
                            customWeeks: $('#customWeeksNumber').val()
                        };
                        console.log('Created new recurring settings for staff:', staffId, staffAvailabilityData[staffId].recurring);
                    } else {
                        console.log('Preserved existing recurring settings for staff:', staffId, staffAvailabilityData[staffId].recurring);
                    }
                };

                // Function to update recurring settings when user changes them
                const updateRecurringSettings = () => {
                    if (assignedStaff.length === 0) return;

                    const currentStaff = assignedStaff[currentStaffIndex];
                    const staffId = currentStaff.id;
                    
                    if (!staffAvailabilityData[staffId]) {
                        staffAvailabilityData[staffId] = {};
                    }
                    
                    // Update recurring settings with current form values
                    staffAvailabilityData[staffId].recurring = {
                        enabled: $('#recurringCheckbox').is(':checked'),
                        weeks: $('input[name="recurringWeeks"]:checked').val(),
                        customWeeks: $('#customWeeksNumber').val()
                    };
                    
                    console.log('Updated recurring settings for staff:', staffId, staffAvailabilityData[staffId].recurring);
                };

                const loadStaffData = () => {
                    if (assignedStaff.length === 0) return;

                    const currentStaff = assignedStaff[currentStaffIndex];
                    const staffId = currentStaff.id;
                    const weekKey = baseStartDate.clone().add(currentWeekIndex * 7, "days").format("YYYY-MM-DD");

                    console.log('Loading data for staff:', currentStaff.name, 'ID:', staffId);
                    console.log('Week key:', weekKey);
                    console.log('Staff availability data for this staff:', staffAvailabilityData[staffId]);

                    // Clear global weekData first to prevent cross-contamination between staff
                    Object.keys(weekData).forEach(key => {
                        delete weekData[key];
                    });
                    console.log('Cleared global weekData to prevent staff overlap');

                    // Load staff's data for current week
                    console.log('Checking for data - staffId:', staffId, 'weekKey:', weekKey);
                    console.log('staffAvailabilityData[staffId]:', staffAvailabilityData[staffId]);
                    console.log('staffAvailabilityData[staffId][weekKey]:', staffAvailabilityData[staffId] ? staffAvailabilityData[staffId][weekKey] : 'staffId not found');
                    
                    if (staffAvailabilityData[staffId] && staffAvailabilityData[staffId][weekKey]) {
                        console.log('Loading existing data for week:', weekKey);
                        const existingData = staffAvailabilityData[staffId][weekKey];
                        console.log('Existing data found:', existingData);
                        // Create a deep copy to prevent reference issues
                        if (existingData) {
                            weekData[weekKey] = JSON.parse(JSON.stringify({
                                Monday: existingData.Monday || { enabled: false, start: "10:00", end: "19:00" },
                                Tuesday: existingData.Tuesday || { enabled: false, start: "10:00", end: "19:00" },
                                Wednesday: existingData.Wednesday || { enabled: false, start: "10:00", end: "19:00" },
                                Thursday: existingData.Thursday || { enabled: false, start: "10:00", end: "19:00" },
                                Friday: existingData.Friday || { enabled: false, start: "10:00", end: "19:00" },
                                Saturday: existingData.Saturday || { enabled: false, start: "10:00", end: "19:00" },
                                Sunday: existingData.Sunday || { enabled: false, start: "10:00", end: "19:00" }
                            }));
                        }
                        console.log('Loaded week data for staff:', weekData[weekKey]);
                    } else {
                        console.log('No existing data, initializing empty week data');
                        // Initialize empty week data for this staff
                        weekData[weekKey] = {
                            Monday: { enabled: false, start: "10:00", end: "19:00" },
                            Tuesday: { enabled: false, start: "10:00", end: "19:00" },
                            Wednesday: { enabled: false, start: "10:00", end: "19:00" },
                            Thursday: { enabled: false, start: "10:00", end: "19:00" },
                            Friday: { enabled: false, start: "10:00", end: "19:00" },
                            Saturday: { enabled: false, start: "10:00", end: "19:00" },
                            Sunday: { enabled: false, start: "10:00", end: "19:00" }
                        };
                    }

                    console.log('Final weekData for UI:', weekData[weekKey]);
                    console.log('About to call updateWeekUI() for staff:', staffId, 'week:', weekKey);
                    updateWeekUI();
                    console.log('updateWeekUI() completed for staff:', staffId);
                    
                    // Restore recurring settings from detected patterns or reset if none
                    if (staffAvailabilityData[staffId] && staffAvailabilityData[staffId].recurring && staffAvailabilityData[staffId].recurring.enabled) {
                        console.log('Restoring recurring settings for staff:', staffId, staffAvailabilityData[staffId].recurring);
                        
                        // Restore recurring checkbox
                        $('#recurringCheckbox').prop('checked', true);
                        $('#recurringOptions').show();
                        
                        // Restore recurring weeks selection
                        const recurringWeeks = staffAvailabilityData[staffId].recurring.weeks;
                        const customWeeks = staffAvailabilityData[staffId].recurring.customWeeks;
                        
                        if (recurringWeeks === 'custom' && customWeeks) {
                            $('#recurringCustom').prop('checked', true);
                            $('#customWeeksInput').show();
                            $('#customWeeksNumber').val(customWeeks);
                        } else if (recurringWeeks === '4') {
                            $('#recurring4weeks').prop('checked', true);
                            $('#customWeeksInput').hide();
                        } else if (recurringWeeks === '8') {
                            $('#recurring8weeks').prop('checked', true);
                            $('#customWeeksInput').hide();
                        } else {
                            // Default to 4 weeks if we have recurring but no specific weeks
                            $('#recurring4weeks').prop('checked', true);
                            $('#customWeeksInput').hide();
                        }
                        
                        console.log('Recurring settings restored for staff:', staffId);
                    } else if (!userHasSetRecurring) {
                        console.log('No recurring pattern detected, resetting recurring settings for staff:', staffId);
                        $('#recurringCheckbox').prop('checked', false);
                        $('#recurringOptions').hide();
                        $('input[name="recurringWeeks"]').prop('checked', false);
                        $('#customWeeksNumber').val('');
                        $('#customWeeksInput').hide();
                    } else {
                        console.log('Preserving user-set recurring settings for staff:', staffId);
                    }
                };

                // Function to update staff data from form
                const updateStaffFromForm = () => {
                    console.log('updateStaffFromForm called');
                    
                    // Get staff data from the form - try both individual and group service forms
                    const staffSelectIndividual = document.getElementById('staff-member');
                    const staffSelectGroup = document.getElementById('staff-member-secondary');
                    const staffSelect = staffSelectIndividual || staffSelectGroup;
                    
                    console.log('Staff select element found:', staffSelect);
                    
                    // If no staff select element found, return early
                    if (!staffSelect) {
                        console.log('No staff select element found - this might be an individual service without staff');
                        return;
                    }
                    
                    // Try different methods to get selected values
                    let selectedValues = [];
                    let selectedTexts = [];
                    let $staffSelect = null;
                    
                    if (staffSelect) {
                        $staffSelect = $(staffSelect);
                        
                        // Method 1: Try selectedOptions
                        if (staffSelect.selectedOptions && staffSelect.selectedOptions.length > 0) {
                            selectedValues = Array.from(staffSelect.selectedOptions).map(option => option.value);
                            selectedTexts = Array.from(staffSelect.selectedOptions).map(option => option.text);
                            console.log('Method 1 - selectedOptions:', selectedValues, selectedTexts);
                        }
                        
                        // Method 2: Try jQuery val() for Select2
                        if (selectedValues.length === 0) {
                            selectedValues = $staffSelect.val() || [];
                            console.log('Method 2 - jQuery val():', selectedValues);
                            
                            // Get text for selected values
                            if (selectedValues.length > 0) {
                                selectedTexts = selectedValues.map(value => {
                                    const option = staffSelect.querySelector(`option[value="${value}"]`);
                                    return option ? option.text : value;
                                });
                            }
                        }
                        
                        // Method 3: Try Select2 data
                        if (selectedValues.length === 0 && $staffSelect.data('select2')) {
                            const select2Data = $staffSelect.select2('data');
                            selectedValues = select2Data.map(item => item.id);
                            selectedTexts = select2Data.map(item => item.text);
                            console.log('Method 3 - Select2 data:', selectedValues, selectedTexts);
                        }
                    }
                    
                    console.log('Final selected values:', selectedValues);
                    console.log('Final selected texts:', selectedTexts);
                    
                    if (selectedValues && selectedValues.length > 0) {
                        const selectedStaff = selectedValues.map((value, index) => ({
                            id: parseInt(value),
                            name: selectedTexts[index] || value
                        }));
                        assignedStaff = selectedStaff;
                        console.log('Updated staff from form:', assignedStaff);
                        
                        // Reset currentStaffIndex if it's out of bounds
                        if (currentStaffIndex >= assignedStaff.length) {
                            currentStaffIndex = Math.max(0, assignedStaff.length - 1);
                            console.log('Reset currentStaffIndex to:', currentStaffIndex);
                        }
                        
                        // Reinitialize staff section
                        initializeStaffSection();
                        if (assignedStaff.length > 0) {
                            loadStaffData();
                        } else {
                            // If no staff selected, disable all inputs
                            toggleAvailabilityInputs(false);
                        }
                    } else {
                        console.log('No staff select found or no options selected');
                        console.log('Available staff selects:', {
                            individual: staffSelectIndividual,
                            group: staffSelectGroup
                        });
                        // Clear assigned staff and show message
                        assignedStaff = [];
                        currentStaffIndex = 0; // Reset index when no staff
                        // Clear all availability data when no staff are assigned
                        clearAllAvailabilityData();
                        initializeStaffSection();
                    }
                };

                // 🎯 JSON DATA INITIALIZATION - Pass your array JSON data here
                const initializeDataFromJSON = () => {
                    // 📋 YOUR JSON DATA - Replace this array with your actual API response
                    const availabilityArray = @json(isset($service) ? $service->availabilities : []);

                    // Check if we have existing data (edit mode)
                    const hasExistingData = availabilityArray && availabilityArray.length > 0;

                    console.log('=== initializeDataFromJSON START ===');
                    console.log('initializeDataFromJSON - availabilityArray:', availabilityArray);
                    console.log('initializeDataFromJSON - assignedStaff:', assignedStaff);
                    console.log('initializeDataFromJSON - hasExistingData:', hasExistingData);
                    console.log('initializeDataFromJSON - staffAvailabilityData before processing:', staffAvailabilityData);

                    // For business role, we need to handle both assigned staff and staff with availability records
                    if (hasExistingData) {
                        // First, collect all unique staff IDs from availability records
                        const staffIdsFromAvailability = [...new Set(availabilityArray.map(item => item.staff_id).filter(id => id))];
                        console.log('Staff IDs from availability records:', staffIdsFromAvailability);

                        // Merge staff from availability records with assigned staff
                        if (staffIdsFromAvailability.length > 0) {
                            console.log('Found staff in availability records, merging with assigned staff');
                            // Get staff details from availability records
                            const staffMap = {};
                            
                            // Add existing assigned staff to map
                            assignedStaff.forEach(staff => {
                                staffMap[staff.id] = staff;
                            });
                            
                            // Add staff from availability records
                    availabilityArray.forEach(item => {
                                if (item.staff_id && item.staff) {
                                    staffMap[item.staff_id] = {
                                        id: item.staff_id,
                                        name: item.staff.name || `Staff ${item.staff_id}`
                                    };
                                }
                            });
                            
                            assignedStaff = Object.values(staffMap);
                            console.log('Merged assignedStaff:', assignedStaff);
                        }

                        // Process availability data
                        availabilityArray.forEach(item => {
                            const staffId = item.staff_id;
                            
                            // For business role with staff
                            if (staffId) {
                        const date = moment(item.date);
                                const weekStart = date.clone().startOf('isoWeek');
                        const weekKey = weekStart.format("YYYY-MM-DD");
                        const dayName = item.day;

                        // Convert time format from "HH:MM:SS" to "HH:MM"
                                const startTime = item.start_time.substring(0, 5);
                                const endTime = item.end_time.substring(0, 5);

                                // Initialize staff data if it doesn't exist
                                if (!staffAvailabilityData[staffId]) {
                                    staffAvailabilityData[staffId] = {};
                                }

                                // Initialize week for this staff if it doesn't exist
                                if (!staffAvailabilityData[staffId][weekKey]) {
                                    staffAvailabilityData[staffId][weekKey] = {
                                        Monday: { enabled: false, start: "10:00", end: "19:00" },
                                        Tuesday: { enabled: false, start: "10:00", end: "19:00" },
                                        Wednesday: { enabled: false, start: "10:00", end: "19:00" },
                                        Thursday: { enabled: false, start: "10:00", end: "19:00" },
                                        Friday: { enabled: false, start: "10:00", end: "19:00" },
                                        Saturday: { enabled: false, start: "10:00", end: "19:00" },
                                        Sunday: { enabled: false, start: "10:00", end: "19:00" }
                                    };
                                }

                                // Set the specific day data for this staff
                                staffAvailabilityData[staffId][weekKey][dayName] = {
                                    enabled: true,
                                    start: startTime,
                                    end: endTime,
                                    id: item.id,
                                    service_id: item.service_id
                                };

                                console.log(`Loaded availability for staff ${staffId}, week ${weekKey}, day ${dayName}:`, {
                                    enabled: true,
                                    start: startTime,
                                    end: endTime
                                });
                            } else {
                                // For individual role or no staff, use original logic
                                const date = moment(item.date);
                                const weekStart = date.clone().startOf('isoWeek');
                                const weekKey = weekStart.format("YYYY-MM-DD");
                                const dayName = item.day;

                                // Convert time format from "HH:MM:SS" to "HH:MM"
                                const startTime = item.start_time.substring(0, 5);
                                const endTime = item.end_time.substring(0, 5);

                        // Initialize week if it doesn't exist
                        if (!weekData[weekKey]) {
                            weekData[weekKey] = {
                                Monday: { enabled: false, start: "10:00", end: "19:00" },
                                Tuesday: { enabled: false, start: "10:00", end: "19:00" },
                                Wednesday: { enabled: false, start: "10:00", end: "19:00" },
                                Thursday: { enabled: false, start: "10:00", end: "19:00" },
                                Friday: { enabled: false, start: "10:00", end: "19:00" },
                                Saturday: { enabled: false, start: "10:00", end: "19:00" },
                                Sunday: { enabled: false, start: "10:00", end: "19:00" }
                            };
                        }

                                // Set the specific day data
                        weekData[weekKey][dayName] = {
                            enabled: true,
                            start: startTime,
                            end: endTime,
                                    id: item.id,
                                    service_id: item.service_id
                                };

                                console.log(`Loaded availability for individual, week ${weekKey}, day ${dayName}:`, {
                                    enabled: true,
                                    start: startTime,
                                    end: endTime
                                });
                            }
                        });

                        // Detect recurring patterns for each staff member
                        Object.keys(staffAvailabilityData).forEach(staffId => {
                            const staffData = staffAvailabilityData[staffId];
                            const weeks = Object.keys(staffData).filter(key => key !== 'recurring');
                            
                            console.log(`Processing staff ${staffId} with weeks:`, weeks);
                            
                            if (weeks.length > 1) {
                                // Sort weeks by date
                                weeks.sort((a, b) => moment(a).diff(moment(b)));
                                console.log(`Sorted weeks for staff ${staffId}:`, weeks);
                                
                                // Check if all weeks have the same pattern
                                const firstWeek = weeks[0];
                                const firstWeekData = staffData[firstWeek];
                                let isRecurring = true;
                                
                                console.log(`First week data for staff ${staffId}:`, firstWeekData);
                                
                                // Check if all subsequent weeks have the same enabled days and times
                                for (let i = 1; i < weeks.length; i++) {
                                    const weekData = staffData[weeks[i]];
                                    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                    
                                    console.log(`Comparing week ${weeks[i]} with first week for staff ${staffId}`);
                                    
                                    for (const day of days) {
                                        if (firstWeekData[day].enabled !== weekData[day].enabled ||
                                            firstWeekData[day].start !== weekData[day].start ||
                                            firstWeekData[day].end !== weekData[day].end) {
                                            console.log(`Pattern mismatch for staff ${staffId} on ${day} in week ${weeks[i]}`);
                                            console.log(`First week: enabled=${firstWeekData[day].enabled}, start=${firstWeekData[day].start}, end=${firstWeekData[day].end}`);
                                            console.log(`Current week: enabled=${weekData[day].enabled}, start=${weekData[day].start}, end=${weekData[day].end}`);
                                            isRecurring = false;
                                            break;
                                        }
                                    }
                                    if (!isRecurring) break;
                                }
                                
                                if (isRecurring) {
                                    // Set recurring settings
                                    staffData.recurring = {
                                        enabled: true,
                                        weeks: weeks.length.toString(),
                                        customWeeks: weeks.length.toString()
                                    };
                                    console.log(`✅ Detected recurring pattern for staff ${staffId}:`, staffData.recurring);
                                } else {
                                    console.log(`❌ No recurring pattern detected for staff ${staffId} - weeks have different patterns`);
                                }
                            } else {
                                console.log(`Staff ${staffId} has only ${weeks.length} week(s) - no recurring pattern possible`);
                            }
                        });

                        console.log('Final staffAvailabilityData:', staffAvailabilityData);
                        console.log('Final weekData:', weekData);
                        
                        // Debug: Show recurring settings for each staff
                        Object.keys(staffAvailabilityData).forEach(staffId => {
                            const staffData = staffAvailabilityData[staffId];
                            if (staffData.recurring) {
                                console.log(`Staff ${staffId} recurring settings:`, staffData.recurring);
                            } else {
                                console.log(`Staff ${staffId} has no recurring settings`);
                            }
                        });
                    }

                    console.log('=== initializeDataFromJSON END ===');
                    console.log('Final staffAvailabilityData:', staffAvailabilityData);
                    console.log('Final assignedStaff:', assignedStaff);
                    console.log('Returning hasExistingData:', hasExistingData);
                    
                    // Return whether we have existing data
                    return hasExistingData;
                };

                // 🎯 EXTRACT SELECTED AVAILABILITY IN YOUR DESIRED FORMAT
                const getSelectedAvailability = () => {
                    const selectedAvailability = [];

                    // For business role with staff, collect all staff availability data
                    if (assignedStaff.length > 0) {
                        // Save current staff data first
                        saveCurrentStaffData();

                        // Loop through all staff members
                        assignedStaff.forEach(staff => {
                            const staffId = staff.id;
                            const staffWeekData = staffAvailabilityData[staffId] || {};
                            const staffRecurring = staffAvailabilityData[staffId]?.recurring || {};

                            // Loop through all weeks for this staff
                            Object.keys(staffWeekData).forEach(weekKey => {
                                // Skip if weekKey is 'recurring' or not a valid date
                                if (weekKey === 'recurring' || !moment(weekKey, 'YYYY-MM-DD', true).isValid()) {
                                    return;
                                }
                                
                                const weekStart = moment(weekKey); // Monday of the week
                                const weekDays = staffWeekData[weekKey];

                                // Skip if weekDays is not valid
                                if (!weekDays || typeof weekDays !== 'object') {
                                    return;
                                }

                                // Check each day of the week
                                Object.keys(weekDays).forEach(dayName => {
                                    const dayData = weekDays[dayName];

                                    // Only include enabled days (with null check)
                                    if (dayData && dayData.enabled) {
                                        // Calculate the actual date for this day
                                        const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].indexOf(dayName);
                                        const actualDate = weekStart.clone().add(dayIndex, 'days');

                                        // Add to result array with staff_id and recurring settings
                                        selectedAvailability.push({
                                            "date": actualDate.format("YYYY-MM-DD"),
                                            "day": dayName,
                                            "start": dayData.start,
                                            "end": dayData.end,
                                            "staff_id": staffId,
                                            "recurring": staffRecurring.enabled || false,
                                            "recurring_weeks": staffRecurring.weeks || null,
                                            "custom_weeks": staffRecurring.customWeeks || null
                                        });
                                    }
                                });
                            });
                        });
                    } else {
                        // For individual role or no staff, use current weekData
                        const recurringEnabled = $('#recurringCheckbox').is(':checked');
                        const recurringWeeks = $('input[name="recurringWeeks"]:checked').val();
                        const customWeeks = $('#customWeeksNumber').val();
                        
                    Object.keys(weekData).forEach(weekKey => {
                        // Skip if weekKey is not a valid date
                        if (!moment(weekKey, 'YYYY-MM-DD', true).isValid()) {
                            return;
                        }
                        
                        const weekStart = moment(weekKey); // Monday of the week
                        const weekDays = weekData[weekKey];

                            // Skip if weekDays is not valid
                            if (!weekDays || typeof weekDays !== 'object') {
                                return;
                            }

                        // Check each day of the week
                        Object.keys(weekDays).forEach(dayName => {
                            const dayData = weekDays[dayName];

                                // Only include enabled days (with null check)
                                if (dayData && dayData.enabled) {
                                // Calculate the actual date for this day
                                const dayIndex = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"].indexOf(dayName);
                                const actualDate = weekStart.clone().add(dayIndex, 'days');

                                // Add to result array in your desired format
                                selectedAvailability.push({
                                    "date": actualDate.format("YYYY-MM-DD"),
                                    "day": dayName,
                                    "start": dayData.start,
                                    "end": dayData.end,
                                        "recurring": recurringEnabled,
                                        "recurring_weeks": recurringWeeks,
                                        "custom_weeks": customWeeks
                                });
                            }
                        });
                    });
                    }

                    // Sort by date for better organization
                    selectedAvailability.sort((a, b) => moment(a.date).diff(moment(b.date)));
                    return selectedAvailability;
                };

                // 📝 UPDATE TEXTAREA WITH JSON OUTPUT
                const updateJsonOutput = () => {
                    const selectedData = getSelectedAvailability();
                    const jsonString = JSON.stringify(selectedData, null, 2);
                    console.log('updateJsonOutput - selectedData:', selectedData);
                    console.log('updateJsonOutput - jsonString length:', jsonString.length);
                    $("#jsonOutput").val(jsonString);
                    console.log('updateJsonOutput - textarea value set');
                };

                // Update availability field status
                const updateAvailabilityFieldStatus = () => {
                    const selectedData = getSelectedAvailability();
                    const availabilityField = document.getElementById('availabilityField');
                    const availabilityText = document.getElementById('availabilityText');
                    
                    if (selectedData && selectedData.length > 0) {
                        // Show success status
                        if (availabilityText) {
                            // For group service form
                            availabilityText.innerHTML = '<i class="fas fa-check text-success me-2" style="vertical-align: middle;"></i>Availability Set';
                            availabilityText.className = 'text-success fw-bold d-flex align-items-center';
                        } else if (availabilityField) {
                            // For individual service form
                            const contentDiv = availabilityField.querySelector('div');
                            if (contentDiv) {
                                contentDiv.innerHTML = '<i class="fas fa-check text-success me-2" style="vertical-align: middle;"></i>Availability Set';
                            }
                            availabilityField.className = 'form-control form-inputs-field d-flex justify-content-between align-items-center text-success fw-bold';
                        }
                    } else {
                        // Show default status
                        if (availabilityText) {
                            // For group service form
                            availabilityText.innerHTML = 'Select availability';
                            availabilityText.className = 'text-muted d-flex align-items-center';
                        } else if (availabilityField) {
                            // For individual service form
                            const contentDiv = availabilityField.querySelector('div');
                            if (contentDiv) {
                                contentDiv.innerHTML = 'Select availability';
                            }
                            availabilityField.className = 'form-control form-inputs-field d-flex justify-content-between align-items-center';
                        }
                    }
                };

                const updateWeekUI = () => {
                    const startOfWeek = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                    const weekDays = Array.from({ length: 7 }, (_, i) => startOfWeek.clone().add(i, "days"));
                    const weekRange = weekDays[0].format("DD MMM YYYY") + " - " + weekDays[6].format("DD MMM YYYY");
                    const weekKey = startOfWeek.format("YYYY-MM-DD");
                    const week = weekData[weekKey] || {};

                    $("#weekRange").text(weekRange);

                    // Update navigation buttons
                    $("#prevWeek").prop('disabled', currentWeekIndex <= 0);

                    const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
                    const today = moment().startOf('day');

                    // Update existing day rows with saved data
                    dayNames.forEach((day, index) => {
                        const date = weekDays[index];
                        const val = week[day] || { start: "10:00", end: "19:00", enabled: false };
                        const isPastDate = date.isBefore(today, 'day');

                        const $dayRow = $(`.day-row[data-day="${day}"]`);
                        const $checkbox = $dayRow.find('.day-checkbox');

                        // Set data-date attribute
                        $dayRow.attr('data-date', date.format("YYYY-MM-DD"));

                        // Update checkbox state without triggering change event
                        $checkbox.off('change');
                        $checkbox.prop('checked', val.enabled);
                        // Don't disable past dates - they can still be checked

                        // Update content based on state
                        if (val.enabled) {
                            // Show time inputs
                            const $status = $dayRow.find('.day-status');
                            if ($status.length > 0) {
                                $status.replaceWith(createTimeInputs(day, val.start, val.end));
                            } else {
                                // Update existing dropdowns
                                const [startHour, startMinute] = val.start.split(':');
                                const [endHour, endMinute] = val.end.split(':');
                                $dayRow.find('.start-hour').val(startHour);
                                $dayRow.find('.start-minute').val(startMinute);
                                $dayRow.find('.end-hour').val(endHour);
                                $dayRow.find('.end-minute').val(endMinute);
                            }
                        } else {
                            // Show status text - always show "Closed" regardless of past/future date
                            const statusText = "Closed";
                            const $timeInputs = $dayRow.find('.time-inputs');
                            if ($timeInputs.length > 0) {
                                $timeInputs.replaceWith(`<span class="day-status">${statusText}</span>`);
                            } else {
                                $dayRow.find('.day-status').text(statusText);
                            }
                        }

                        // Re-attach change event
                        $checkbox.on('change', function () {
                            const $dayRow = $(this).closest('.day-row');
                            const day = $(this).data('day');
                            const isChecked = $(this).is(':checked');

                            if (isChecked) {
                                const $status = $dayRow.find('.day-status');
                                if ($status.length > 0) {
                                    $status.replaceWith(createTimeInputs(day, '10:00', '19:00'));
                                }
                            } else {
                                const $timeInputs = $dayRow.find('.time-inputs');
                                if ($timeInputs.length > 0) {
                                    $timeInputs.replaceWith('<span class="day-status">Closed</span>');
                                }
                            }

                            saveCurrentWeekData();
                            updateJsonOutput();
                        });
                    });
                };

                // Function to apply recurring pattern from current week forward
                const applyRecurringFromCurrentWeek = () => {
                    if (assignedStaff.length === 0) return;
                    
                    const currentStaff = assignedStaff[currentStaffIndex];
                    const staffId = currentStaff.id;
                    
                    // Check if this staff has recurring settings
                    if (staffAvailabilityData[staffId] && staffAvailabilityData[staffId].recurring && staffAvailabilityData[staffId].recurring.enabled) {
                        const recurring = staffAvailabilityData[staffId].recurring;
                        console.log('Applying recurring pattern from current week forward for staff:', staffId, 'Recurring:', recurring);
                        
                        // Get the current week's data to use as the pattern
                        const currentWeekKey = baseStartDate.clone().add(currentWeekIndex * 7, "days").format("YYYY-MM-DD");
                        const currentWeekData = staffAvailabilityData[staffId][currentWeekKey];
                        
                        if (currentWeekData) {
                            // Get the number of weeks for recurring
                            let totalWeeks = 4; // default
                            if (recurring.weeks === 'custom' && recurring.customWeeks) {
                                totalWeeks = parseInt(recurring.customWeeks);
                            } else if (recurring.weeks) {
                                totalWeeks = parseInt(recurring.weeks);
                            }
                            
                            console.log('Using current week as pattern:', currentWeekKey, 'Applying to', totalWeeks, 'weeks starting from current week');
                            
                            // Apply the current week's pattern to future weeks only (starting from current week)
                            for (let i = currentWeekIndex; i < currentWeekIndex + totalWeeks; i++) {
                                const weekKey = baseStartDate.clone().add(i * 7, "days").format("YYYY-MM-DD");
                                
                                // Only apply if this week exists in the data
                                if (staffAvailabilityData[staffId][weekKey]) {
                                    // Deep copy the current week's pattern to this week
                                    staffAvailabilityData[staffId][weekKey] = JSON.parse(JSON.stringify(currentWeekData));
                                    console.log('Applied recurring pattern to week', weekKey);
                                }
                            }
                            
                            // Update the UI to reflect the changes
                            updateWeekUI();
                        }
                    }
                };

                // Function to apply recurring settings to current week (for navigation)
                const applyRecurringToCurrentWeek = () => {
                    if (assignedStaff.length === 0) return;
                    
                    const currentStaff = assignedStaff[currentStaffIndex];
                    const staffId = currentStaff.id;
                    const weekKey = baseStartDate.clone().add(currentWeekIndex * 7, "days").format("YYYY-MM-DD");
                    
                    // Check if this staff has recurring settings
                    if (staffAvailabilityData[staffId] && staffAvailabilityData[staffId].recurring && staffAvailabilityData[staffId].recurring.enabled) {
                        const recurring = staffAvailabilityData[staffId].recurring;
                        console.log('Applying recurring settings for staff:', staffId, 'Week:', weekKey, 'Recurring:', recurring);
                        
                        // Find the base week (first week where availability was set)
                        const baseWeekKey = Object.keys(staffAvailabilityData[staffId]).find(key => 
                            key !== 'recurring' && staffAvailabilityData[staffId][key] && 
                            Object.values(staffAvailabilityData[staffId][key]).some(day => day.enabled)
                        );
                        
                        if (baseWeekKey) {
                            const baseWeekData = staffAvailabilityData[staffId][baseWeekKey];
                            const weeksToApply = recurring.weeks === 'custom' ? parseInt(recurring.customWeeks) : parseInt(recurring.weeks);
                            
                            // Calculate how many weeks from base week
                            const baseWeekMoment = moment(baseWeekKey);
                            const currentWeekMoment = moment(weekKey);
                            const weeksDifference = currentWeekMoment.diff(baseWeekMoment, 'weeks');
                            
                            console.log('Base week:', baseWeekKey, 'Current week:', weekKey, 'Weeks difference:', weeksDifference, 'Weeks to apply:', weeksToApply);
                            
                            // Apply recurring if within the specified weeks
                            if (weeksDifference >= 0 && weeksDifference < weeksToApply) {
                                // Copy the base week data to current week
                                if (!weekData[weekKey]) {
                                    weekData[weekKey] = {};
                                }
                                
                                Object.keys(baseWeekData).forEach(day => {
                                    if (baseWeekData[day] && baseWeekData[day].enabled) {
                                        try {
                                            weekData[weekKey][day] = JSON.parse(JSON.stringify(baseWeekData[day]));
                                            console.log('Applied recurring for day:', day, 'Data:', weekData[weekKey][day]);
                                        } catch (e) {
                                            console.error('Error copying recurring data for day:', day, e);
                                        }
                                    }
                                });
                                
                                // Update the UI to reflect the changes
                                updateWeekUI();
                                console.log('Applied recurring settings to week:', weekKey);
                            }
                        }
                    }
                };

                const saveCurrentWeekData = () => {
                    const base = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                    const key = base.format("YYYY-MM-DD");
                    weekData[key] = {};

                    $(".day-row").each(function () {
                        const day = $(this).data("day");
                        const enabled = $(this).find(".day-checkbox").is(":checked");

                        let start = "10:00";
                        let end = "19:00";

                        if (enabled) {
                            // Get time from separate hour and minute dropdowns
                            const startHour = $(this).find(".start-hour").val() || "10";
                            const startMinute = $(this).find(".start-minute").val() || "00";
                            const endHour = $(this).find(".end-hour").val() || "19";
                            const endMinute = $(this).find(".end-minute").val() || "00";

                            start = startHour + ":" + startMinute;
                            end = endHour + ":" + endMinute;
                        }

                        weekData[key][day] = { enabled, start, end };
                    });

                    console.log('Saved week data:', weekData[key]);
                };

                // Function to duplicate the weeks (with reset functionality)
                const duplicateWeeks = (weeks) => {
                    const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                    const srcKey = current.format("YYYY-MM-DD");

                    // 🔄 RESET: Clear all future week duplications first
                    Object.keys(weekData).forEach(weekKey => {
                        const weekDate = moment(weekKey);
                        const currentWeekDate = moment(srcKey);

                        // Remove any week that's after the current week and was previously duplicated
                        if (weekDate.isAfter(currentWeekDate, 'week')) {
                            // Check if this week has the same pattern as current week (indicating it was duplicated)
                            const currentWeekData = weekData[srcKey];
                            const weekToCheck = weekData[weekKey];

                            // Only remove if it looks like a duplication (same enabled pattern)
                            if (currentWeekData && weekToCheck) {
                                const currentEnabledDays = Object.keys(currentWeekData).filter(day => currentWeekData[day].enabled);
                                const checkEnabledDays = Object.keys(weekToCheck).filter(day => weekToCheck[day].enabled);

                                // If same number of enabled days, likely a duplication - remove it
                                if (currentEnabledDays.length === checkEnabledDays.length && currentEnabledDays.length > 0) {
                                    delete weekData[weekKey];
                                }
                            }
                        }
                    });

                    // 📅 CREATE: Now create fresh duplications for the selected weeks
                    for (let i = 1; i < weeks; i++) {
                        const next = current.clone().add(i * 7, "days");
                        const newKey = next.format("YYYY-MM-DD");
                        // Create deep copy of current week's data
                        if (weekData[srcKey]) {
                        weekData[newKey] = JSON.parse(JSON.stringify(weekData[srcKey]));
                        }
                    }
                };

                // Validate time inputs
                const validateTimeInput = (input) => {
                    const $input = $(input);
                    const $dayRow = $input.closest('.day-row');
                    const dayName = $dayRow.data('day');

                    // Get hour and minute values
                    const startHour = $dayRow.find('.start-hour').val();
                    const startMinute = $dayRow.find('.start-minute').val();
                    const endHour = $dayRow.find('.end-hour').val();
                    const endMinute = $dayRow.find('.end-minute').val();

                    // Validate that all time components are selected
                    if (!startHour || !startMinute || !endHour || !endMinute ||
                        startHour === '' || startMinute === '' || endHour === '' || endMinute === '') {
                        Swal.fire({
                            icon: 'error',
                            title: 'Missing Time Selection',
                            text: `Please select complete start and end times for ${dayName}`,
                            confirmButtonText: 'OK'
                        });
                        $input.focus();
                        return false;
                    }

                    // Create time strings for comparison
                    const startTime = startHour + ":" + startMinute;
                    const endTime = endHour + ":" + endMinute;

                    // Validate that start time is before end time
                    if (startTime >= endTime) {
                        Swal.fire({
                            icon: 'error',
                            title: 'Invalid Time Range',
                            text: `End time must be after start time for ${dayName}`,
                            confirmButtonText: 'OK'
                        });
                        $input.focus();
                        return false;
                    }

                    return true;
                };

                // Function to generate hour options (0-23)
                const generateHourOptions = (selectedHour = '') => {
                    let options = '<option value="">Hr</option>';
                    for (let hour = 0; hour < 24; hour++) {
                        const hourValue = hour.toString().padStart(2, '0');
                        const selected = hourValue === selectedHour ? 'selected' : '';
                        options += `<option value="${hourValue}" ${selected}>${hourValue}</option>`;
                    }
                    return options;
                };

                // Function to generate minute options (0-59)
                const generateMinuteOptions = (selectedMinute = '') => {
                    let options = '<option value="">Min</option>';
                    for (let minute = 0; minute < 60; minute++) {
                        const minuteValue = minute.toString().padStart(2, '0');
                        const selected = minuteValue === selectedMinute ? 'selected' : '';
                        options += `<option value="${minuteValue}" ${selected}>${minuteValue}</option>`;
                    }
                    return options;
                };

                // Function to create time dropdown inputs with separate hour and minute dropdowns
                const createTimeInputs = (day, startTime = '10:00', endTime = '19:00') => {
                    const [startHour, startMinute] = startTime.split(':');
                    const [endHour, endMinute] = endTime.split(':');

                    return `
                        <div class="time-inputs">
                            <div class="time-group">
                                <select class="form-control form-control-sm start-hour" data-day="${day}">
                                    ${generateHourOptions(startHour)}
                                </select>
                                <span class="time-separator">:</span>
                                <select class="form-control form-control-sm start-minute" data-day="${day}">
                                    ${generateMinuteOptions(startMinute)}
                                </select>
                            </div>
                            <span style="margin: 0 8px;">to</span>
                            <div class="time-group">
                                <select class="form-control form-control-sm end-hour" data-day="${day}">
                                    ${generateHourOptions(endHour)}
                                </select>
                                <span class="time-separator">:</span>
                                <select class="form-control form-control-sm end-minute" data-day="${day}">
                                    ${generateMinuteOptions(endMinute)}
                                </select>
                            </div>
                        </div>
                    `;
                };

                // Function to force 24-hour format on time inputs (legacy support)
                const force24HourFormat = () => {
                    $('.time-inputs input[type="time"]').each(function() {
                        const $input = $(this);
                        const currentValue = $input.val();

                        // Force the input to use 24-hour format
                        $input.attr('step', '3600');
                        $input.attr('data-format', '24');

                        // Ensure the value is in 24-hour format
                        if (currentValue && currentValue.length === 5) {
                            $input.val(currentValue);
                        }
                    });
                };

                // Make functions globally available for form submission
                window.getSelectedAvailability = getSelectedAvailability;
                window.saveCurrentWeekData = saveCurrentWeekData;
                window.updateAvailabilityFieldStatus = updateAvailabilityFieldStatus;

                // Initialize staff section immediately to prevent flicker
                initializeStaffSection();

                // Initialize data from JSON and check if we have existing data
                console.log('BEFORE initializeDataFromJSON - assignedStaff:', assignedStaff);
                console.log('BEFORE initializeDataFromJSON - staffAvailabilityData:', staffAvailabilityData);
                const hasExistingData = initializeDataFromJSON();
                console.log('AFTER initializeDataFromJSON - assignedStaff:', assignedStaff);
                console.log('AFTER initializeDataFromJSON - staffAvailabilityData:', staffAvailabilityData);
                console.log('AFTER initializeDataFromJSON - hasExistingData:', hasExistingData);
                
                // For business role with staff, load the first staff's data
                if (assignedStaff.length > 0) {
                    console.log('Business role detected with staff:', assignedStaff);
                    console.log('Staff availability data:', staffAvailabilityData);
                    // Load data for the first staff member
                    loadStaffData();
                } else {
                    console.log('Individual role or no staff assigned');
                    // For individual role, update UI with regular weekData
                updateWeekUI();
                }
                
                updateJsonOutput();
                
                // Update availability field status on page load (for existing services)
                updateAvailabilityFieldStatus();

                // Track if modal should reset on close
                // Don't reset if we have existing data (edit mode) or if Done button was clicked
                let shouldResetOnClose = hasExistingData ? false : (isDone == true ? false : true);

                // Initialize modal when it's shown
                $('#availabilityModal').on('shown.bs.modal', function () {
                    console.log('Modal opened - initializing...');
                    
                    // Reset recurring flag when modal opens to allow pattern detection
                    userHasSetRecurring = false;
                    
                    // Don't reset if we have existing data (edit mode) or if Done button was clicked
                    shouldResetOnClose = hasExistingData ? false : (isDone == true ? false : true);
                    
                    // Update staff data from form
                    updateStaffFromForm();
                    
                    // Initialize staff section
                    initializeStaffSection();
                    
                    // Update UI
                    updateWeekUI();
                    updateJsonOutput();
                    
                    // Start periodic sync check with longer interval to avoid interfering with user actions
                    syncInterval = setInterval(function() {
                        // Only sync if we have staff assigned and user is not actively working
                        if (assignedStaff.length > 0 && !$('#recurringCheckbox').is(':focus') && !$('.day-checkbox').is(':focus')) {
                            updateStaffFromForm();
                        }
                    }, 10000);
                    
                    console.log('Modal initialization complete');
                });

                // Reset modal when it's hidden (only if not saved via Done button)
                $('#availabilityModal').on('hidden.bs.modal', function () {
                    // Stop periodic sync check
                    if (syncInterval) {
                        clearInterval(syncInterval);
                        syncInterval = null;
                    }
                    
                    if (shouldResetOnClose) {
                        resetModal();
                    }
                });

                // Validate time inputs when they change (works for hour and minute dropdowns)
                $(document).on("change", ".start-hour, .start-minute, .end-hour, .end-minute", function () {
                    if (validateTimeInput(this)) {
                        saveCurrentWeekData();
                        updateJsonOutput(); // Update JSON when time changes
                    }
                });

                $("#prevWeek").click(function () {
                    // Prevent going to past weeks
                    if (currentWeekIndex > 0) {
                        saveCurrentWeekData();
                        currentWeekIndex--;
                        updateWeekUI();
                        applyRecurringToCurrentWeek(); // Apply recurring logic to new week
                        updateJsonOutput(); // Update JSON when week changes
                    }
                });

                $("#nextWeek").click(function () {
                    saveCurrentWeekData();
                    currentWeekIndex++;
                    updateWeekUI();
                    applyRecurringToCurrentWeek(); // Apply recurring logic to new week
                    updateJsonOutput(); // Update JSON when week changes
                });

                // Function to reset modal to initial state
                const resetModal = () => {
                    // Reset all checkboxes
                    $('.day-checkbox').prop('checked', false);

                    // Reset all day statuses to "Closed"
                    $('.day-row').each(function () {
                        const $timeInputs = $(this).find('.time-inputs');
                        if ($timeInputs.length > 0) {
                            $timeInputs.replaceWith('<span class="day-status">Closed</span>');
                        }
                    });

                    // Only reset recurring options if no days are selected
                    // This prevents resetting when user is actively working
                    if (!hasSelectedDays()) {
                    $('#recurringCheckbox').prop('checked', false);
                    $('#recurringOptions').hide();
                    $('#customWeeksInput').hide();
                    $('input[name="recurringWeeks"]').prop('checked', false);
                    $('#customWeeksNumber').val('');
                    }

                    // Clear week data
                    Object.keys(weekData).forEach(key => {
                        delete weekData[key];
                    });

                    // Reset to current week
                    currentWeekIndex = 0;
                    updateWeekUI();
                    updateJsonOutput();
                };


                // Cancel button handler
                $("#cancelAvailability").click(function () {
                    // Don't reset if we have existing data (edit mode)
                    shouldResetOnClose = hasExistingData ? false : true;
                    if(shouldResetOnClose == true) {
                        resetModal();
                        // For new services, also clear staff availability data
                        staffAvailabilityData = {};
                        // Update availability field status after clearing data
                        updateAvailabilityFieldStatus();
                    } else {
                        // For existing services, update status normally
                        updateAvailabilityFieldStatus();
                    }
                    
                    $('#availabilityModal').modal('hide');
                });

                // Handle close button (X) and outside clicks - don't reset if in edit mode
                $('#availabilityModal .btn-close').click(function () {
                    shouldResetOnClose = hasExistingData ? false : true;
                    // Update availability field status
                    updateAvailabilityFieldStatus();
                });

                // Handle clicking outside modal or pressing Escape
                $('#availabilityModal').on('hide.bs.modal', function (e) {
                    // If modal is being hidden and shouldResetOnClose is still true,
                    // it means user didn't click Done button, so we should reset
                    if (shouldResetOnClose) {
                        // This will be handled by the hidden.bs.modal event
                    }
                });

                // Done button handler
                $("#saveAvailability").click(function () {
                    // Validate all selected days have proper times
                    let hasValidationError = false;
                    isDone = true;
                    $('.day-checkbox:checked').each(function () {
                        const $dayRow = $(this).closest('.day-row');
                        const $startTime = $dayRow.find('.start-time');
                        const $endTime = $dayRow.find('.end-time');

                        if ($startTime.length > 0 && $endTime.length > 0) {
                            if (!validateTimeInput($startTime[0])) {
                                hasValidationError = true;
                                return false; // Break the loop
                            }
                        }
                    });

                    // Validate recurring settings if recurring is enabled
                    if ($('#recurringCheckbox').is(':checked')) {
                        const selectedRecurringWeeks = $('input[name="recurringWeeks"]:checked').val();
                        
                        if (!selectedRecurringWeeks) {
                            showToast('Please select a recurring option (4 weeks, 8 weeks, or Custom)', 'warning');
                            hasValidationError = true;
                        } else if (selectedRecurringWeeks === 'custom') {
                            const customWeeks = parseInt($('#customWeeksNumber').val());
                            if (!customWeeks || customWeeks < 1 || customWeeks > 52) {
                                showToast('Please enter a valid number of weeks (1-52) for custom recurring', 'warning');
                                $('#customWeeksNumber').focus();
                                hasValidationError = true;
                            }
                        }
                    }

                    if (!hasValidationError) {
                        saveCurrentWeekData();
                        updateJsonOutput(); // Final update of JSON
                        const selectedData = getSelectedAvailability();
                        console.log("Availability saved:", selectedData);

                        // Update availability field status
                        updateAvailabilityFieldStatus();

                        // Prevent reset when closing via Done button
                        shouldResetOnClose = false;
                        $('#availabilityModal').modal('hide');
                    }
                });

                // Function to show toast notification
                const showToast = (message, type = 'success') => {
                    // Remove any existing toast
                    $('.toast-notification').remove();

                    let bgClass, iconClass;
                    switch (type) {
                        case 'success':
                            bgClass = 'bg-success';
                            iconClass = 'check-circle';
                            break;
                        case 'danger':
                            bgClass = 'bg-danger';
                            iconClass = 'exclamation-circle';
                            break;
                        case 'warning':
                            bgClass = 'bg-warning';
                            iconClass = 'exclamation-triangle';
                            break;
                        default:
                            bgClass = 'bg-success';
                            iconClass = 'check-circle';
                    }

                    const toastHtml = `
                    <div class="toast-notification position-fixed top-0 end-0 m-3" style="z-index: 9999;">
                        <div class="toast show ${bgClass} text-white" role="alert">
                            <div class="toast-body d-flex align-items-center">
                                <i class="fas fa-${iconClass} me-2"></i>
                                ${message}
                            </div>
                        </div>
                    </div>
                `;

                    $('body').append(toastHtml);

                    // Auto remove after 3 seconds
                    setTimeout(() => {
                        $('.toast-notification').fadeOut(300, function () {
                            $(this).remove();
                        });
                    }, 3000);
                };

                // Function to check if any day is selected
                const hasSelectedDays = () => {
                    return $('.day-checkbox:checked').length > 0;
                };

                // Recurring Checkbox Change
                $("#recurringCheckbox").change(function () {
                    const isChecked = $(this).is(':checked');
                    console.log('Recurring checkbox changed to:', isChecked);
                    console.log('Stack trace:', new Error().stack);
                    
                    // Mark that user has manually set recurring settings
                    userHasSetRecurring = true;
                    
                    if (isChecked) {
                        // Check if any day is selected first
                        if (!hasSelectedDays()) {
                            // Prevent checking and show warning toast
                            $(this).prop('checked', false);
                            showToast('Please select a day first', 'warning');
                            return;
                        }

                        // Show recurring options
                        $("#recurringOptions").show();
                        console.log('Recurring enabled - showing options');
                    } else {
                        // Hide recurring options
                        $("#recurringOptions").hide();
                        $("#customWeeksInput").hide();
                        // Clear all radio buttons
                        $('input[name="recurringWeeks"]').prop('checked', false);
                        $("#customWeeksNumber").val('');

                        // Clear future weeks when recurring is disabled
                        const current = baseStartDate.clone().add(currentWeekIndex * 7, "days");
                        const srcKey = current.format("YYYY-MM-DD");

                        Object.keys(weekData).forEach(weekKey => {
                            const weekDate = moment(weekKey);
                            const currentWeekDate = moment(srcKey);

                            if (weekDate.isAfter(currentWeekDate, 'week')) {
                                delete weekData[weekKey];
                            }
                        });
                        updateJsonOutput();

                        // Show toast notification for recurring removal
                        showToast('Recurring schedule removed', 'danger');
                        console.log('Recurring disabled - cleared future weeks');
                    }
                    
                    // Update recurring settings for current staff
                    updateRecurringSettings();
                });

                // Prevent radio button interaction when recurring is unchecked
                $('input[name="recurringWeeks"]').on('click', function(e) {
                    if (!$('#recurringCheckbox').is(':checked')) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('Recurring checkbox must be checked first');
                        showToast('Please enable recurring first', 'warning');
                        return false;
                    }
                });

                // Monitor day selection changes to handle recurring checkbox state
                $(document).on('change', '.day-checkbox', function() {
                    // Only uncheck recurring if it was previously checked and now no days are selected
                    // Add a small delay to prevent immediate unchecking during UI updates
                    setTimeout(() => {
                        if ($('#recurringCheckbox').is(':checked') && !hasSelectedDays()) {
                            $('#recurringCheckbox').prop('checked', false);
                            $('#recurringOptions').hide();
                            $('#customWeeksInput').hide();
                            $('input[name="recurringWeeks"]').prop('checked', false);
                            $('#customWeeksNumber').val('');
                            showToast('Recurring disabled - no days selected', 'info');
                        }
                    }, 100);
                });

                // Handle recurring weeks radio button changes
                $('input[name="recurringWeeks"]').change(function () {
                    // Only proceed if recurring checkbox is checked
                    if (!$('#recurringCheckbox').is(':checked')) {
                        $(this).prop('checked', false);
                        return;
                    }

                    // Mark that user has manually set recurring settings
                    userHasSetRecurring = true;

                    const selectedValue = $(this).val();

                    if (selectedValue === 'custom') {
                        $("#customWeeksInput").show();
                        $("#customWeeksNumber").focus();
                    } else {
                        $("#customWeeksInput").hide();
                        $("#customWeeksNumber").val('');

                        // Apply the selected number of weeks from current week forward
                        saveCurrentWeekData();
                        updateRecurringSettings(); // Save the recurring settings first
                        applyRecurringFromCurrentWeek(); // Apply pattern from current week forward
                        updateJsonOutput();

                        // Show success toast
                        showToast(`Your schedule is applied for ${selectedValue} weeks starting from this week`, 'success');
                        console.log(`Recurring set to ${selectedValue} weeks - applied from current week forward`);
                    }
                    
                    // Update recurring settings for current staff
                    updateRecurringSettings();
                });

                // Handle Apply button for custom weeks
                $("#applyCustomWeeks").click(function () {
                    // Only proceed if recurring checkbox is checked
                    if (!$('#recurringCheckbox').is(':checked')) {
                        showToast('Please enable recurring first', 'warning');
                        return;
                    }

                    // Mark that user has manually set recurring settings
                    userHasSetRecurring = true;

                    const customWeeks = parseInt($("#customWeeksNumber").val());
                    if (customWeeks && customWeeks > 0 && customWeeks <= 52) {
                        saveCurrentWeekData();
                        updateRecurringSettings(); // Save the recurring settings first
                        applyRecurringFromCurrentWeek(); // Apply pattern from current week forward
                        updateJsonOutput();

                        // Show success toast
                        showToast(`Your schedule is applied for ${customWeeks} weeks starting from this week`, 'success');
                        console.log(`Custom recurring set to ${customWeeks} weeks - applied from current week forward`);
                    } else {
                        showToast(`Please enter a valid number of weeks (1-52)`, 'danger');
                        $("#customWeeksNumber").focus();
                    }
                    
                    // Update recurring settings for current staff
                    updateRecurringSettings();
                });

                // Handle Enter key press in custom weeks input
                $("#customWeeksNumber").keypress(function (e) {
                    if (e.which === 13) { // Enter key
                        $("#applyCustomWeeks").click();
                    }
                });

                // Staff navigation event handlers
                $("#prevStaff").click(function () {
                    if (currentStaffIndex > 0) {
                        switchToStaff(currentStaffIndex - 1);
                    }
                });

                $("#nextStaff").click(function () {
                    if (currentStaffIndex < assignedStaff.length - 1) {
                        switchToStaff(currentStaffIndex + 1);
                    }
                });


                // Listen for staff selection changes in the form
                $(document).on('change', '#staff-member, #staff-member-secondary', function() {
                    console.log('Staff selection changed in form');
                    // Update staff data when user changes selection
                    updateStaffFromForm();
                });

                // Listen for Select2 changes (in case staff are removed via Select2)
                $(document).on('select2:unselect', '#staff-member, #staff-member-secondary', function() {
                    console.log('Staff member unselected via Select2');
                    updateStaffFromForm();
                });

                // Listen for any changes to the staff select elements (including clearing)
                $(document).on('change select2:clear', '#staff-member, #staff-member-secondary', function() {
                    console.log('Staff selection cleared or changed');
                    updateStaffFromForm();
                });

                // Periodic check to ensure modal stays in sync with form (every 10 seconds when modal is open)
                let syncInterval;

                // Save staff data when week changes
                $("#prevWeek, #nextWeek").click(function () {
                    if (assignedStaff.length > 0) {
                        saveCurrentStaffData();
                    }
                });
            })(); // Close IIFE
        }); // Close outer jQuery ready
    </script>
@endpush 