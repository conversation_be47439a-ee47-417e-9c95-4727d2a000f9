<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class CustomersExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize, WithTitle
{
    protected $customers;

    public function __construct($customers)
    {
        $this->customers = $customers;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return $this->customers;
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Name',
            'Email Address',
            'Status',
            'Joined Date',
            'Total Bookings',
            'Total Spending',
            'Last Booking'
        ];
    }

    /**
     * @param mixed $customer
     * @return array
     */
    public function map($customer): array
    {
        // Get actual data using the same methods as the table
        $totalBookings = $customer->totalBookings();
        $totalEarnings = $customer->totalEarnings();
        $lastBooking = $customer->lastBookingTime();

        return [
            $customer->name ?? 'Customer',
            $customer->email ?? 'N/A',
            $customer->status == 1 ? 'Active' : 'Inactive',
            $customer->created_at ? $customer->created_at->format('M d, Y') : 'N/A',
            $totalBookings,
            '$' . number_format($totalEarnings, 2),
            $lastBooking ? $lastBooking->format('M d, Y') : 'No Booking Yet'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true, 'size' => 12]],

            // Set background color for header (A1 to G1 for 7 columns)
            'A1:G1' => [
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Customers';
    }
}
