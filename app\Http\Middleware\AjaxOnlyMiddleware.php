<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AjaxOnlyMiddleware
{
    /**
     * Handle an incoming request.
     * Only allow AJAX requests, block direct URL access
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if request is AJAX
        if (!$request->ajax()) {
            // Return 404 for non-AJAX requests to hide the route
            abort(404);
        }

        return $next($request);
    }
}
