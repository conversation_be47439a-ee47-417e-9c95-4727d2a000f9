<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $user = Auth::guard($guard)->user();

                // Check admin status first
                if ($user->hasAnyRole(['admin', 'super admin'])) {
                    if ($user->status == 0) {
                        // Admin is inactive, logout and redirect to login with error
                        Auth::guard($guard)->logout();
                        $request->session()->invalidate();
                        $request->session()->regenerateToken();
                        return redirect()->route('login')->with([
                            'title' => 'Account Not Active',
                            'message' => 'Your admin account is not active. Please contact the system administrator.',
                            'type' => 'error',
                        ]);
                    }
                    // Admin is active, redirect to dashboard
                    return redirect('/dashboard');
                }

                // Check if registration is completed (but not for admins)
                if ($user->registration_completed == 0 && !$user->hasAnyRole(['admin', 'super admin'])) {
                    // Registration not completed, redirect to continue registration
                    if ($user->hasRole('customer')) {
                        return redirect()->route('register.user_type', 'customer');
                    } elseif ($user->hasAnyRole(['business', 'individual', 'professional'])) {
                        return redirect()->route('register.user_type', 'professional');
                    }
                }

                // Registration completed, role-based redirection for already authenticated users
                if ($user->hasRole('developer')) {
                    return redirect('/home');
                } elseif ($user->hasRole('customer')) {
                    return redirect('/');
                } elseif ($user->hasAnyRole(['business', 'individual', 'professional'])) {
                    return redirect('/dashboard');
                } else {
                    return redirect(RouteServiceProvider::HOME);
                }
            }
        }

        return $next($request);
    }
}
