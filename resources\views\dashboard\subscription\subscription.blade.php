@extends('dashboard.layout.master')
@push('css')
    <style>
        .payment-method-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid subscription">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-10 px-3">
                <div class="col-md-12 d-flex justify-content-between align-items-center flex-wrap ">
                    <div>
                        @if (auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']))
                            <h6 class="sora black">Subscription Management</h6>
                             <p class="fs-14 sora light-black m-0"> Manage and track all user subscriptions from here. </p>
                        @endif
                        @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business'))
                            <h6 class="sora black">Subscription</h6>
                            <p class="fs-14 sora light-black m-0">Subscribe monthly and enjoy unlimited beauty services with 
                        exclusive member discounts and priority booking. </p>
                        @endif
                       
                    </div>

                    @if(auth()->user()->activeSubscription)
                        <div class="row mt-4">
                            <div class="col-md-12 text-center">
                                <button id="update-payment-method-btn"
                                    class="add-btn text-white subscribed-label fs-16 semi_bold inter black">
                                    Update Payment Method
                                </button>
                            </div>
                        </div>
                    @endif
                    
                </div>
                <!-- Payment Method Update Button -->

                <div class="col-md-12 mb-10">
                    <div class="Subscription-tabs">
                        <ul class="nav mb-5" id="myTab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="Individual-tab" data-bs-toggle="tab"
                                    data-bs-target="#Individual" type="button" role="tab" aria-controls="Individual"
                                    aria-selected="true">
                                    <p class="fs-14 fw-500 mb-0">Individual</p>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="business-tab" data-bs-toggle="tab" data-bs-target="#business"
                                    type="button" role="tab" aria-controls="business" aria-selected="false">
                                    <p class="fs-14 fw-500 mb-0">Business</p>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="enterprise-tab" data-bs-toggle="tab"
                                    data-bs-target="#enterprise" type="button" role="tab" aria-controls="enterprise"
                                    aria-selected="false">
                                    <p class="fs-14 fw-500 mb-0">Enterprise</p>
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content " id="myTabContent">
                            <div class="tab-pane fade show active" id="Individual" role="tabpanel"
                                aria-labelledby="Individual-tab">
                                <div class="card_wrapper row row-gap-5">
                                    @foreach ($individualSubscriptions as $individualSubscription)
                                        <div class="col-xl-4 col-lg-6 col-md-6">
                                            <div class="card pricing-card ">
                                                <div class="pricing-header d-flex justify-content-between align-item-center">
                                                    <h5 class="fs-32 semi_bold sora card-text-blue">
                                                        <i class="fa-solid fa-star"></i>
                                                        {{ $individualSubscription->name ?? '' }}
                                                    </h5>
                                                </div>
                                                @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business'))
                                                    @if (auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $individualSubscription->id)
                                                        <span class="badge current-plan-badge deep-blue fs-14 bold">
                                                            Current Plan
                                                        </span>
                                                    @endif
                                                @endif
                                                <h2 class="semi_bold black sora">
                                                    ${{ $individualSubscription->price ?? '' }}<span
                                                        class="fs-18 light-black normal sora">/month</span></h2>
                                                <div class="card-body">
                                                    <ul class="features">
                                                        @foreach ($individualSubscription->details as $detail)
                                                            <li>{{ $detail->feature ?? '' }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                                @if (auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']))
                                                    <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                        class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                        Package</a>
                                                @endif
                                                @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional'))
                                                    @if (auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $individualSubscription->id)
                                                        <form action="{{ route('subscription.cancel') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="stripe_subscription_id"
                                                                value="{{ auth()->user()->activeSubscription->stripe_subscription_id ?? '' }}">
                                                            <button class="cancel-link fs-16 semi_bold inter w-100 cancel-btn"
                                                                type="submit">
                                                                Cancel Your Plan</button>
                                                        </form>
                                                    @else
                                                        <form action="{{ route('payment.subscription') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="subscription_id"
                                                                value="{{ $individualSubscription->id }}">
                                                            <button class="subscribed-label fs-16 semi_bold inter black"
                                                                type="submit">Subscribe</button>
                                                        </form>
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>


                            <div class="tab-pane fade show" id="business" role="tabpanel" aria-labelledby="business-tab">
                                <div class="card_wrapper row row-gap-5">
                                    @foreach ($businessSubscriptions as $businessSubscription)
                                        <div class="col-xl-4 col-lg-6 col-md-6">
                                            <div class="card pricing-card">
                                                <div class="pricing-header d-flex justify-content-between align-item-center">
                                                    <h5 class="fs-32 semi_bold sora card-text-blue">
                                                        <i class="fa-solid fa-star"></i>
                                                        {{ $businessSubscription->name ?? '' }}
                                                    </h5>
                                                </div>
                                                @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business'))
                                                    @if (auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $businessSubscription->id)
                                                        <span class="badge current-plan-badge deep-blue fs-14 bold">
                                                            Current Plan
                                                        </span>
                                                    @endif
                                                @endif
                                                <h2 class="semi_bold black sora">${{ $businessSubscription->price ?? '' }}
                                                    <span class="fs-18 light-black normal sora">/month</span>
                                                </h2>
                                                <div class="card-body">
                                                    <ul class="features">
                                                        @foreach ($businessSubscription->details as $detail)
                                                            <li>{{ $detail->feature ?? '' }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                                @if (auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']))
                                                    <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                        class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                        Package</a>
                                                @endif
                                                @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional'))
                                                    @if (auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $businessSubscription->id)
                                                        <form action="{{ route('subscription.cancel') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="stripe_subscription_id"
                                                                value="{{ auth()->user()->activeSubscription->stripe_subscription_id ?? '' }}">
                                                            <button class="cancel-link fs-16 semi_bold inter w-100 cancel-btn"
                                                                type="submit">
                                                                Cancel Your Plan</button>
                                                        </form>
                                                    @else
                                                        <form action="{{ route('payment.subscription') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="subscription_id"
                                                                value="{{ $businessSubscription->id }}">
                                                            <button class="subscribed-label fs-16 semi_bold inter black"
                                                                type="submit">Subscribe</button>
                                                        </form>
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>


                            <div class="tab-pane fade show " id="enterprise" role="tabpanel"
                                aria-labelledby="enterprise-tab">
                                <div class="card_wrapper row row-gap-5">
                                    @foreach ($enterpriseSubscriptions as $enterpriseSubscription)
                                        <div class="col-xl-4 col-lg-6 col-md-6">
                                            <div class="card pricing-card">
                                                <div class="pricing-header d-flex justify-content-between align-item-center">
                                                    <h5 class="fs-32 semi_bold sora card-text-blue">
                                                        <i class="fa-solid fa-star"></i>
                                                        {{ $enterpriseSubscription->name ?? '' }}
                                                    </h5>
                                                </div>
                                                @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business'))
                                                    @if (auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $enterpriseSubscription->id)
                                                        <span class="badge current-plan-badge deep-blue fs-14 bold">
                                                            Current Plan
                                                        </span>
                                                    @endif
                                                @endif
                                                <h2 class="semi_bold black sora">
                                                    ${{ $enterpriseSubscription->price ?? '' }}
                                                    <span class="fs-18 light-black normal sora">/month</span>
                                                </h2>
                                                <div class="card-body">
                                                    <ul class="features">
                                                        @foreach ($enterpriseSubscription->details as $detail)
                                                            <li>{{ $detail->feature ?? '' }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                                @if (auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']))
                                                    <a data-bs-toggle="modal" data-bs-target="#edit_package"
                                                        class="fs-16 semi_bold inter white action-btn text-center ">Edit
                                                        Package</a>
                                                @endif
                                                @if (auth()->check() && auth()->user()->hasAnyRole('individual', 'business', 'professional'))
                                                    @if (auth()->user()->activeSubscription && auth()->user()->activeSubscription->subscription_id == $enterpriseSubscription->id)
                                                        <form action="{{ route('subscription.cancel') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="stripe_subscription_id"
                                                                value="{{ auth()->user()->activeSubscription->stripe_subscription_id ?? '' }}">
                                                            <button class="cancel-link fs-16 semi_bold inter w-100 cancel-btn"
                                                                type="submit">
                                                                Cancel Your Plan</button>
                                                        </form>
                                                    @else
                                                        <form action="{{ route('payment.subscription') }}" method="POST">
                                                            @csrf
                                                            <input type="hidden" name="subscription_id"
                                                                value="{{ $enterpriseSubscription->id }}">
                                                            <button class="subscribed-label fs-16 semi_bold inter black"
                                                                type="submit">Subscribe</button>
                                                        </form>
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @include('dashboard.subscription.modal.edit-package-modal')
@endsection
    @push('js')
        <script>
            $(document).ready(function () {
                // Get stored tab
                var activeTab = localStorage.getItem('activeTab');
                if (activeTab) {
                    var tabElement = $('#myTab button[data-bs-target="' + activeTab + '"]');
                    if (tabElement.length) {
                        $('#myTab button').removeClass('active');
                        $('.tab-pane').removeClass('show active');

                        tabElement.addClass('active').attr('aria-selected', 'true');
                        $(activeTab).addClass('show active');
                    }
                }
                $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function (event) {
                    var target = $(event.target).attr('data-bs-target');
                    localStorage.setItem('activeTab', target);
                });

                // Handle subscription cancellation with confirmation
                $(document).on('submit', 'form[action="{{ route('subscription.cancel') }}"]', function (e) {
                    e.preventDefault(); // Prevent default form submission

                    var form = $(this);

                    Swal.fire({
                        title: 'Cancel Subscription',
                        text: 'Are you sure you want to cancel your subscription?',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#d33',
                        cancelButtonColor: '#3085d6',
                        confirmButtonText: 'Yes, cancel it!',
                        cancelButtonText: 'No, keep it'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            // User confirmed, submit the form
                            form[0].submit();
                        }
                    });
                });

                // Payment method functionality ready

                // Handle update payment method button
                $('#update-payment-method-btn').click(function () {
                    updatePaymentMethod();
                });

                // Handle subscription form submissions with enhanced confirmation
                $(document).on('submit', 'form[action="{{ route('payment.subscription') }}"]', function (e) {
                    e.preventDefault(); // Prevent default form submission

                    var form = $(this);
                    var newSubscriptionId = parseInt(form.find('input[name="subscription_id"]').val());

                    // Check if user has active subscription
                    @if(auth()->user()->activeSubscription)
                        var hasActiveSubscription = true;
                        var currentSubscriptionId = {{ auth()->user()->activeSubscription->subscription_id }};
                        var currentSubscriptionPrice = {{ auth()->user()->activeSubscription->subscription_price }};

                        // If user has active subscription and trying to subscribe to different plan
                        if (currentSubscriptionId != newSubscriptionId) {
                            // Get subscription data from the page
                            var subscriptionData = getSubscriptionData(newSubscriptionId);

                            if (subscriptionData) {
                                var isUpgrade = subscriptionData.price > currentSubscriptionPrice;
                                var isDowngrade = subscriptionData.price < currentSubscriptionPrice;

                                if (isUpgrade) {
                                    showUpgradeConfirmation(subscriptionData, {
                                        id: currentSubscriptionId,
                                        name: '{{ auth()->user()->activeSubscription->subscription_name }}',
                                        price: currentSubscriptionPrice
                                    });
                                } else if (isDowngrade) {
                                    showDowngradeConfirmation(subscriptionData, {
                                        id: currentSubscriptionId,
                                        name: '{{ auth()->user()->activeSubscription->subscription_name }}',
                                        price: currentSubscriptionPrice
                                    });
                                }
                            } else {
                                // Fallback to generic message
                                showGenericSubscriptionChange();
                            }
                            return false; // Prevent form submission
                        }
                    @else
                                        // New subscription
                                        var subscriptionData = getSubscriptionData(newSubscriptionId);
                        if (subscriptionData) {
                            showNewSubscriptionConfirmation(subscriptionData);
                            return false; // Prevent form submission
                        }
                    @endif

                    // No active subscription or same subscription, proceed normally
                    form[0].submit();
                });
            });
        </script>

        <script>
            $(document).ready(function () {
                function updateModalForActiveTab() {
                    var activeTab = $('#myTab .nav-link.active');
                    if (activeTab.length > 0) {
                        var tabText = activeTab.find('p').text().trim();
                        var tabValue = tabText.toLowerCase();

                        $('#package-heading').html('<i class="fa-solid fa-star"></i> ' + tabText);
                        $('#package-type').val(tabValue);
                    }
                }
                // Update modal when it's shown
                $('#edit_package').on('show.bs.modal', function () {
                    updateModalForActiveTab();
                });
                $('#myTab button[data-bs-toggle="tab"]').on('shown.bs.tab', function () {
                    updateModalForActiveTab();
                });
                updateModalForActiveTab();
            });
        </script>

        <!-- Payment Method and Subscription Management Functions -->
        <script>

            function updatePaymentMethod() {
                Swal.fire({
                    title: 'Update Payment Method',
                    text: 'You will be redirected to update your payment method. This will not charge your card.',
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'Continue',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Create a form to submit the update request
                        const form = $('<form>', {
                            'method': 'POST',
                            'action': '{{ route("payment.method.update") }}'
                        });

                        form.append($('<input>', {
                            'type': 'hidden',
                            'name': '_token',
                            'value': '{{ csrf_token() }}'
                        }));

                        $('body').append(form);
                        form.submit();
                    }
                });
            }

            function getSubscriptionData(subscriptionId) {
                // Get subscription data from the page elements
                var subscriptionCard = $(`form[action="{{ route('payment.subscription') }}"] input[value="${subscriptionId}"]`).closest('.pricing-card');

                if (subscriptionCard.length) {
                    var name = subscriptionCard.find('h5').text().trim();
                    var priceText = subscriptionCard.find('h2').text().trim();
                    var price = parseFloat(priceText.replace('$', '').replace('/month', ''));

                    return {
                        id: subscriptionId,
                        name: name,
                        price: price
                    };
                }
                return null;
            }

            function showUpgradeConfirmation(targetSubscription, currentSubscription) {
                const priceDifference = targetSubscription.price - currentSubscription.price;

                Swal.fire({
                    title: 'Upgrade Subscription',
                    html: `
                                <div class="text-left">
                                    <p><strong>Upgrading from:</strong> ${currentSubscription.name} ($${currentSubscription.price}/month)</p>
                                    <p><strong>Upgrading to:</strong> ${targetSubscription.name} ($${targetSubscription.price}/month)</p>
                                    <p><strong>You will be charged:</strong> $${priceDifference.toFixed(2)} (prorated difference)</p>
                                    <p><strong>Upgrade takes effect:</strong> Immediately</p>
                                </div>
                            `,
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'Upgrade Now',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: '#3085d6'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $(`form[action="{{ route('payment.subscription') }}"] input[value="${targetSubscription.id}"]`).closest('form')[0].submit();
                    }
                });
            }

            function showDowngradeConfirmation(targetSubscription, currentSubscription) {
                Swal.fire({
                    title: 'Downgrade Subscription',
                    html: `
                                <div class="text-left">
                                    <p><strong>Downgrading from:</strong> ${currentSubscription.name} ($${currentSubscription.price}/month)</p>
                                    <p><strong>Downgrading to:</strong> ${targetSubscription.name} ($${targetSubscription.price}/month)</p>
                                    <p><strong>You will be charged:</strong> $${currentSubscription.price} (full amount for current billing period)</p>
                                    <p><strong>Downgrade takes effect:</strong> Next billing cycle</p>
                                    <p class="text-warning"><strong>Note:</strong> You will keep your current plan benefits until your next billing date.</p>
                                </div>
                            `,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Downgrade',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: '#f39c12'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $(`form[action="{{ route('payment.subscription') }}"] input[value="${targetSubscription.id}"]`).closest('form')[0].submit();
                    }
                });
            }

            function showNewSubscriptionConfirmation(targetSubscription) {
                Swal.fire({
                    title: 'Subscribe to Plan',
                    html: `
                                <div class="text-left">
                                    <p><strong>Plan:</strong> ${targetSubscription.name}</p>
                                    <p><strong>Price:</strong> $${targetSubscription.price}/month</p>
                                    <p><strong>You will be charged:</strong> $${targetSubscription.price} (full amount)</p>
                                </div>
                            `,
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'Subscribe',
                    cancelButtonText: 'Cancel',
                    confirmButtonColor: '#3085d6'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $(`form[action="{{ route('payment.subscription') }}"] input[value="${targetSubscription.id}"]`).closest('form')[0].submit();
                    }
                });
            }

            function showGenericSubscriptionChange() {
                Swal.fire({
                    title: 'Subscription Change',
                    text: 'Your previous subscription will be cancelled and you will be switched to the new plan. Do you want to proceed?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, proceed',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Find the form that was being submitted
                        $('form[action="{{ route('payment.subscription') }}"]').off('submit').submit();
                    }
                });
            }
        </script>
    @endpush