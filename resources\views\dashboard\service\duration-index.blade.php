@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="fs-18 fw-bold">Service Durations</h2>
                        <button type="button" class="btn btn-primary" onclick="openCreateDurationModal()">
                            <i class="fas fa-plus me-2"></i>Add Duration
                        </button>
                    </div>

                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-classes"
                            role="tabpanel" aria-labelledby="pills-classes-tab" tabindex="0">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="table-container">
                                        <table id="responsiveTable" class=" display wallet-history-table w-100 ">
                                            <thead>
                                                <tr>
                                                    <th>#</th>
                                                    <th>Duration</th>
                                                    <th>Created At</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="durationsTableBody">
                                                @forelse ($serviceDurations as $index => $duration)
                                                    <tr>
                                                        <td data-label="#">{{ $index + 1 }}</td>
                                                        <td data-label="Duration">{{ $duration->duration }} min</td>
                                                        <td data-label="Created At">{{ $duration->created_at->format('M d, Y') }}</td>
                                                        <td data-label="Actions">
                                                            <div class="dropdown">
                                                                <button class="drop-btn" type="button"
                                                                    id="dropdownMenuButton{{ $duration->id }}" data-bs-toggle="dropdown"
                                                                    aria-expanded="false">
                                                                    <i class="bi bi-three-dots-vertical"></i>
                                                                </button>
                                                                <ul class="dropdown-menu"
                                                                    aria-labelledby="dropdownMenuButton{{ $duration->id }}">
                                                                    <li>
                                                                        <button type="button"
                                                                            class="dropdown-item complete fs-14 regular"
                                                                            onclick="openEditDurationModal({{ $duration->id }}, {{ $duration->duration }})">
                                                                            <i class="bi bi-pencil-square complete-icon"></i>
                                                                            Edit
                                                                        </button>
                                                                    </li>
                                                                    <li>
                                                                        <form
                                                                            action="{{ route('services.durations.destroy', $duration->id) }}"
                                                                            method="POST" class="delete-form">
                                                                            @csrf
                                                                            @method('DELETE')
                                                                            <button
                                                                                class="dropdown-item cancel fs-14 regular"
                                                                                type="button"
                                                                                onclick="showDeleteConfirmation(this)">
                                                                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                                Delete
                                                                            </button>
                                                                        </form>
                                                                    </li>
                                                                </ul>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="4" class="text-center py-5">
                                                            <div class="d-flex flex-column align-items-center">
                                                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                                                <h5 class="text-muted">No Service Durations Found</h5>
                                                                <p class="text-muted">Click "Add Duration" to create your first service duration.</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Include the duration modal -->
    @include('dashboard.service.include.duration-modal')
@endsection
@push('js')
@endpush
