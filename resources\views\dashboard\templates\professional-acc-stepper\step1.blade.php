<div class="form-card frst-step">
    <div class="container">
        <div class="row">
            <div class="col-md-12 mb-10">
                <h2>Create a professional account</h2>
                <p>You're almost there! Create your new account for {{ $user->email }} by completing these
                    details.</p>
            </div>

            <div class="col-md-12 mb-10 pro-account-image-holder">
                <div class="Image-input_holder mb-10">
                    <div class="image-input image-input-empty" data-kt-image-input="true">
                        <div class="image-input" data-kt-image-input="true">
                            <div class="image-input-wrapper "
                                style="background-image: url('{{ asset('website') }}/{{ $user->profile?->pic ?? '' }}'); background-size: cover; background-position: center;">
                            </div>
                            <label class="dark-green-btn fs-14 regular pt-9" data-kt-image-input-action="change">
                                <span class="pe-3 fs-16 fw-600 mb-10 blue-text"> Upload Profile Picture<span
                                        style="color: red;">*</span></span>
                                <input type="file" name="avatar" class="no_validate" accept=".png, .jpg, .jpeg"
                                    id="profileImage" />
                                <input type="hidden" name="avatar_remove" />
                                <input type="hidden" name="has_existing_image" id="has_existing_image" value="{{ !empty($user->profile?->pic) ? '1' : '0' }}" />
                                <p class="fs-24 medium pt-4 gray-text"> At least 125x125 px recommended. JPG or PNG is
                                    allowed</p>
                            </label>

                            <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click"
                                title="Cancel avatar"> <i class="fas fa-times fa-5"></i> </a>

                            <a href="#!" class=" ms-5" data-kt-image-input-action="remove"><i
                                    class="fas fa-trash-alt"></i> </a>
                        </div>
                    </div>
                    <p class="image-error-msg mt-5" style="color: red; display: none;">Image required</p>
                </div>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="name" class="fieldlabels">Full Name <span style="color: red;">*</span></label>
                <input type="text" name="name" value="{{ old('name', $user->name) }}"
                    placeholder="Enter your first name" id="fullname" />
                <label id="fullname-error" class="error" for="fullname"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="companyname" class="fieldlabels">Company Name <span style="color: red;">*</span></label>
                <input type="text" name="company_name"
                    value="{{ old('company_name', $user->profile?->company_name ?? '') }}" id="companyname"
                    placeholder="Enter your company name" />
                <label id="companyname-error" class="error" for="companyname"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="email" class="fieldlabels">Email <span style="color: red;">*</span></label>
                <input type="email" name="email" value="{{ $user->email }}" id="email" readonly autocomplete="off" />
            </div>

            <div class="col-md-12 field-spacing phone-call">
                <div>
                    <label for="phone" class="fieldlabels">Phone <span style="color: red;">*</span></label>
                    <input id="phone" type="tel" placeholder="Phone Number" value="{{ $user->profile?->phone ?? '' }}"
                        name="phone" />
                    <input type="hidden" name="country_code" id="country_code" value="">
                    <small class="text-muted">Country code will be automatically included</small>
                </div>
                <label id="phone-error" class="error" for="phone"
                    style="color: red; font-weight: bold; display: none; margin-top: 5px;"></label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="website" class="fieldlabels">Website <span style="color: red;">*</span></label>
                <input type="url" name="website"
                    value="{{ old('website', $user->profile?->website ?? '') }}"
                    placeholder="Enter your website url" id="website" />
                <label id="website-error" class="error" for="website"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="facebook" class="fieldlabels">Facebook <span style="color: red;">*</span></label>
                <input type="url" name="facebook"
                    value="{{ old('facebook', $user->socials?->where('social_platform_id', 1)->first()?->link ?? '') }}"
                    placeholder="Enter your facebook url" id="facebook" />
                <label id="facebook-error" class="error" for="facebook"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="instagram" class="fieldlabels">Instagram <span style="color: red;">*</span></label>
                <input type="url" name="instagram"
                    value="{{ old('instagram', $user->socials?->where('social_platform_id', 2)->first()?->link ?? '') }}"
                    placeholder="Enter your Instagram url" id="instagram" />

                <label id="instagram-error" class="error" for="instagram"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="tiktok" class="fieldlabels">TikTok <span style="color: red;">*</span></label>
                <input type="url" name="tiktok"
                    value="{{ old('tiktok', $user->socials?->where('social_platform_id', 3)->first()?->link ?? '') }}"
                    placeholder="Enter your TikTok url" id="tiktok" />

                <label id="tiktok-error" class="error" for="tiktok"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>
            <div class="col-md-12 field-spacing">
                <div class="row">
                    <div class="col-6 field-spacing">
                        <label for="location" class="fieldlabels w-100">Location <span
                                style="color: red;">*</span></label>
                        <input id="pac-input" type="text" name="location"
                            value="{{ old('location', $user->profile?->location ?? '') }}"
                            placeholder="Please enter your location" />
                        <input type="hidden" name="lat"
                            value="{{ old('lat', $user->profile?->lat ?? '') }}" id="latitude">
                        <input type="hidden" name="lng"
                            value="{{ old('lng', $user->profile?->lng ?? '') }}" id="longitude">
                    </div>
                    <div class="col-6 field-spacing">
                        <label for="location_service" class="fieldlabels ">Provide service within (km) <span
                                style="color: red;">*</span></label>
                        <input type="number" name="location_service"
                            value="{{ old('location_service', $user->profile?->location_service ?? '') }}"
                            placeholder="Enter service radius in km" id="location_service" />
                    </div>
                    <div class="col-md-12 ">
                        <div class="custom_loc">
                            <div id="map" style="height: 300px"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="country" class="fieldlabels">Country<span style="color: red;">*</span></label>
                <select class="form-select form-select-field" data-control="select2"
                    data-dropdown-css-class="w-619px" data-close-on-select="false"
                    data-placeholder="Select an option" id="country" name="country">
                    <option disabled selected>Select Country</option>
                    @foreach ($countries as $country)
                        <option value="{{ $country->country_name }}"
                            {{ old('country', $user->profile?->country) == $country->country_name ? 'selected' : '' }}>
                            {{ $country->country_name }}
                        </option>
                    @endforeach
                </select>
                {{-- <input type="text" name="country"
                    value="{{ old('country', auth()->user()->profile?->country ?? '') }}" placeholder="Enter country"
                    id="country" /> --}}

                <label id="country-error" class="error" for="country"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="company_id" class="fieldlabels">Company ID <span style="color: red;">*</span></label>
                <input type="text" name="company_id"
                    value="{{ old('company_id', $user->profile?->company_id ?? '') }}"
                    placeholder="Enter company ID" id="company_id" />

                <label id="company_id-error" class="error" for="company_id"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"> </label>
            </div>

            <div class="col-md-12 field-spacing">
                <label for="vat_number" class="fieldlabels">Company VAT Number <span
                        style="color: red;">*</span></label>
                <input type="number" name="vat_number"
                    value="{{ old('vat_number', $user->profile?->vat_number ?? '') }}"
                    placeholder="Enter company VAT number" id="vat_number" min="0"
                    oninput="if(this.value < 0) this.value = this.value.slice(0, -1);" />

                <label id="vat_number-error" class="error" for="vat_number"
                    style="color: red; font-weight: bold; display: block; margin-bottom: 5px;"></label>
            </div>

            <div class="col-md-12">
                <div class="d-flex justify-content-between my-3 gap-8">
                    @if ($user->hasGoogleCalendarConnected())
                        <a href="{{ route('google.remove') }}" class="google_btn  w-md-50 w-sm-100 text-center">
                            <span class="pe-3"> <img src="{{ asset('website') }}/assets/images/Google_Logo.svg">
                            </span> Remove Google
                            Calendar </a>
                    @else
                        <a href="{{ route('google.calendar.connect') }}" id="googleCalendarBtn"
                            class="google_btn  w-md-50 w-sm-100 text-center"> <span class="pe-3"> <img
                                    src="{{ asset('website') }}/assets/images/Google_Logo.svg"> </span> Connect Google
                            Calendar </a>
                    @endif
                    <a href="{{ route('outlook.redirect') }}" id="outlookCalendarBtn"
                        class="outlook_btn  w-md-50 w-sm-100 text-center">
                        <span class="pe-3"> <img src="{{ asset('website') }}/assets/images/outlook.svg">
                        </span>
                        Connect Outlook
                        Calendar</a>
                </div>
            </div>
        </div>
    </div>
</div>
