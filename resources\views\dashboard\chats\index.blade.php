@extends(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master')

@push('css')
<link rel="stylesheet" href="{{ asset('website/assets/css/chat.css') }}">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/emoji-picker-element@^1/index.css">
@endpush

@section('content')
<div id="kt_app_content" class="app-content flex-column-fluid {{ auth()->user()->hasRole('customer') ? 'customer_dashboard bg-color' : '' }}">
    <div id="kt_app_content_container" class="container-fluid {{ auth()->user()->hasRole('customer') ? '' : '' }}">
        <div class="row g-0">
            <div class="col-12">
                <div class="chat-container d-flex">
                    <!-- Chat Sidebar -->
                    <div class="chat-sidebar">
                        <div class="chat-sidebar-header">
                            <div class="chat-dropdown">
                                <button class="chat-dropdown-toggle" type="button">
                                    Chats
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="chat-tabs">
                                <button class="chat-tab active" data-tab="active">
                                    <i class="fas fa-comments"></i>
                                    Active
                                </button>
                                <button class="chat-tab" data-tab="archived">
                                    <i class="fas fa-archive"></i>
                                    Archived
                                </button>
                            </div>
                            <div class="chat-search">
                                <i class="fas fa-search search-icon"></i>
                                <input type="text" id="chat-search-input" placeholder="Search conversations..." autocomplete="off">
                            </div>
                        </div>
                        <div id="conversations-list">
                            <div class="text-center p-4">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Main Area -->
                    <div class="chat-main">
                        <div id="chat-area">
                            <div class="empty-state">
                                <i class="fas fa-comments"></i>
                                @if(auth()->user()->hasRole(['admin', 'super admin']))
                                <h5>Select a conversation to view messages</h5>
                                <p class="text-muted">Choose from existing conversations to monitor user communications</p>
                                @else
                                <h5>Select a conversation to start messaging</h5>
                                <p class="text-muted">Choose from your existing conversations or start a new one</p>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Simple File Browser Modal -->
<div class="modal fade" id="fileUploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <!-- Header -->
            <div class="modal-header">
                <div class="d-flex align-items-center w-100">
                    <button type="button" class="btn btn-sm btn-outline-secondary me-3" id="backBtn" style="display: none;">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <h6 class="modal-title mb-0 flex-grow-1" id="modalTitle">Select Files</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
            </div>

            <!-- File Browser Content -->
            <div class="modal-body p-0" style="height: 450px; overflow-y: auto;">
                <div id="fileBrowserContent">
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open text-muted mb-3" style="font-size: 3rem;"></i>
                        <p class="text-muted">Click "Browse Files" to select files</p>
                        <button type="button" class="btn btn-primary" id="browseFilesBtn">
                            <i class="fas fa-folder-open me-2"></i>Browse Files
                        </button>
                    </div>
                </div>
            </div>

            <!-- Hidden file input (kept outside dynamic content) -->
            <input type="file" class="d-none" id="realFileInput" multiple accept="image/*,video/*,.pdf,.doc,.docx,.txt">

            <!-- Selected Files Preview -->
            <div id="selectedFilesPreview" class="border-top px-3 py-2" style="display: none;">
                <div class="d-flex align-items-center">
                    <small class="text-muted me-2">Selected files:</small>
                    <div class="selected-files-list d-flex flex-wrap gap-1"></div>
                </div>
            </div>

            <!-- Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="sendSelectedBtn" disabled>
                    Send Files (<span id="selectedCount">0</span>)
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('js')
<script src="{{ asset('website/assets/js/chat.js') }}?v={{ time() }}"></script>

<script>
    // Pusher is now initialized globally in master layout
    // No need to initialize it again here

    // Set current user ID for JavaScript access
    window.currentUserId = '{{ auth()->id() }}';
    window.currentUserRole = '{{ auth()->user()->getRoleNames()->first() }}';
    window.isAdmin = {{ auth()->user()->hasRole(['admin', 'super admin']) ? 'true' : 'false' }};

    // Initialize chat when page loads
    $(document).ready(function() {
        // Set current user ID for message alignment
        ChatApp.currentUserId = @json(auth()->id());
        ChatApp.currentUserRole = window.currentUserRole;
        ChatApp.isAdmin = window.isAdmin;

        // Get conversation ID from backend only (clean URL approach)
        const conversationId = @json($conversationId ?? null); // This is database ID

        // Check if ChatApp is defined
        if (typeof ChatApp !== 'undefined') {
            ChatApp.init(conversationId);
        } else {
            console.error('ChatApp is not defined. Please check if chat.js is loaded properly.');
        }

    });
</script>
@endpush
