<?php

namespace App\Http\Controllers;

use App\Models\CmsPage;
use App\Models\Home;
use App\Models\HomeDetail;
use App\Models\PrivacyAndTerm;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CmsController extends Controller
{
    function __construct(){
        $pages = CmsPage::all();
        view()->share('pages', $pages);
    }

    public function home()
    {
        $page = Home::with('details')->first();
        return view('dashboard.admin.cms.home', compact('page'));
    }

    public function privacy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('dashboard.admin.cms.privacy', compact('policies'));
    }

    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('dashboard.admin.cms.terms', compact('terms'));
    }

    public function create()
    {
        return view('dashboard.admin.cms.create');
    }

    public function editHome(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            // Text fields
            'kicker' => 'nullable|string|max:255',
            'heading_one' => 'nullable|string|max:255',
            'heading_two' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'section_two_title' => 'nullable|string|max:255',
            'section_two_heading' => 'nullable|string|max:255',
            'section_two_description' => 'nullable|string',
            'section_three_title' => 'nullable|string|max:255',
            'section_three_heading' => 'nullable|string|max:255',
            'section_three_button_one_text' => 'nullable|string|max:255',
            'section_three_button_one_link' => 'nullable|string|max:255',
            // Details array
            'details.*.title' => 'nullable|string|max:100',
            'details.*.description' => 'nullable|string',
            // Image fields
            'banner_image' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'image' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'section_two_bg_image' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'section_two_image' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'section_three_bg_image' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'details.*.image' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
        ], [
            'banner_image.mimes' => $messagesConfig['image']['mimes'],
            'banner_image.max' => $messagesConfig['image']['max'],
            'image.mimes' => $messagesConfig['image']['mimes'],
            'image.max' => $messagesConfig['image']['max'],
            'section_two_bg_image.mimes' => $messagesConfig['image']['mimes'],
            'section_two_bg_image.max' => $messagesConfig['image']['max'],
            'section_two_image.mimes' => $messagesConfig['image']['mimes'],
            'section_two_image.max' => $messagesConfig['image']['max'],
            'section_three_bg_image.mimes' => $messagesConfig['image']['mimes'],
            'section_three_bg_image.max' => $messagesConfig['image']['max'],
            'details.*.image.mimes' => $messagesConfig['image']['mimes'],
            'details.*.image.max' => $messagesConfig['image']['max'],
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $home = Home::first();
            if (!$home) {
                $home = new Home();
            }
            $homeData = $validator->validated();
            if ($request->hasFile('banner_image')) {
                if ($home->banner_image) {
                    $this->deleteImage($home->banner_image);
                }
                $homeData['banner_image'] = $this->storeImage('home-images', $request->file('banner_image'));
            }
            if ($request->hasFile('section_two_bg_image')) {
                if ($home->section_two_bg_image) {
                    $this->deleteImage($home->section_two_bg_image);
                }
                $homeData['section_two_bg_image'] = $this->storeImage('home-images', $request->file('section_two_bg_image'));
            }
            if ($request->hasFile('section_two_image')) {
                if ($home->section_two_image) {
                    $this->deleteImage($home->section_two_image);
                }
                $homeData['section_two_image'] = $this->storeImage('home-images', $request->file('section_two_image'));
            }
            if ($request->hasFile('image')) {
                if ($home->image) {
                    $this->deleteImage($home->image);
                }
                $homeData['image'] = $this->storeImage('home-images', $request->file('image'));
            }
            if ($request->hasFile('section_three_bg_image')) {
                if ($home->section_three_bg_image) {
                    $this->deleteImage($home->section_three_bg_image);
                }
                $homeData['section_three_bg_image'] = $this->storeImage('home-images', $request->file('section_three_bg_image'));
            }
            unset($homeData['details']);
            $home->fill($homeData);
            $home->save();
            if ($request->has('details')) {
                $home->details()->delete();
                foreach ($request->details as $index => $detailData) {
                    if (!empty($detailData['title']) || !empty($detailData['description'])) {
                        $detail = new HomeDetail();
                        $detail->home_id = $home->id;
                        $detail->title = $detailData['title'] ?? null;
                        $detail->description = $detailData['description'] ?? null;
                        if ($request->hasFile("details.{$index}.image")) {
                            $detail->image = $this->storeImage('home-details', $request->file("details.{$index}.image"));
                        } elseif ($request->input("details.{$index}.old_image")) {
                            $detail->image = $request->input("details.{$index}.old_image");
                        }
                        $detail->save();
                    }
                }
            }
            DB::commit();
            return redirect()->back()->with([
                "type" => "success",
                "title" => "Updated",
                "message" => 'Home page updated successfully!'
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with([
                "type" => "error",
                "message" => $th->getMessage(),
                "title" => "Error"
            ]);
        }
    }

    public function editPrivacyTerms(Request $request)
    {
        $type = $request->input('type');
        try {
            DB::beginTransaction();
            $pageType = $type;
            if ($type === 'privacy') {
                PrivacyAndTerm::where('type', 'privacy')->delete();
                foreach ($request->privacy as $sectionData) {
                    if (!empty($sectionData['title']) || !empty($sectionData['description'])) {
                        PrivacyAndTerm::create([
                            'type' => 'privacy',
                            'title' => $sectionData['title'] ?? null,
                            'description' => $sectionData['description'] ?? null,
                        ]);
                    }
                }
            } elseif ($type === 'term') {
                PrivacyAndTerm::where('type', 'term')->delete();
                foreach ($request->term as $sectionData) {
                    if (!empty($sectionData['title']) || !empty($sectionData['description'])) {
                        PrivacyAndTerm::create([
                            'type' => 'term',
                            'title' => $sectionData['title'] ?? null,
                            'description' => $sectionData['description'] ?? null,
                        ]);
                    }
                }
            }
            DB::commit();
            $successMessage = $type === 'privacy' ? 'Privacy Policy updated successfully!' : 'Terms & Conditions updated successfully!';
            return redirect()->back()->with([
                "type" => "success",
                "title" => "Updated",
                "message" => $successMessage
            ]);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with([
                "type" => "error",
                "message" => $th->getMessage(),
                "title" => "Error"
            ]);
        }
    }

    public function store(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'title' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'heading' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'description' => 'required|string',
            'code_snippets' => 'nullable|string',
        ], [
            'title.required' => 'Please enter page title',
            'title.regex' => $messagesConfig['input']['regex'],
            'title.max' => $messagesConfig['name']['max'],
            'heading.required' => 'Please enter page heading',
            'heading.regex' => $messagesConfig['input']['regex'],
            'heading.max' => $messagesConfig['name']['max'],
            'description.required' => 'Please enter page description',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $pageData = $validator->validated();
            $pageData['slug'] = Str::slug($request->input('title'));
            CmsPage::create($pageData);
            DB::commit();
            return redirect()->route('cms.home')->with(["type" => "success", "title" => "Created", "message" => 'Page created successfully!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    public function update(Request $request, $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'title' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'heading' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'description' => 'required|string',
            'code_snippets' => 'nullable|string',
        ], [
            'title.required' => 'Please enter page title',
            'title.regex' => $messagesConfig['input']['regex'],
            'title.max' => $messagesConfig['name']['max'],
            'heading.required' => 'Please enter page heading',
            'heading.regex' => $messagesConfig['input']['regex'],
            'heading.max' => $messagesConfig['name']['max'],
            'description.required' => 'Please enter page description',
        ]);

        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $page = CmsPage::find($id);
            $pageData = $validator->validated();
            if ($page->title !== $request->input('title')) {
                $pageData['slug'] = Str::slug($request->input('title'));
            }
            $page->update($pageData);
            DB::commit();
            return redirect()->route('cms.home')->with(["type" => "success", "title" => "Updated", "message" => 'Page updated successfully!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }   

    public function show($slug)
    {
        $page = CmsPage::where('slug', $slug)->firstOrFail();
        return view('dashboard.admin.cms.show', compact('page'));
    }

    public function destroy($id)
    {
        try {
            $page = CmsPage::findOrFail($id);
            $page->delete();
            
            return redirect()->route('cms.home')->with([
                'type' => 'success',
                'title' => 'Deleted',
                'message' => 'Page deleted successfully!'
            ]);
        } catch (\Throwable $th) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $th->getMessage(),
                'title' => 'Error'
            ]);
        }
    }
}
