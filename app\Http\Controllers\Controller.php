<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\View;
use Storage;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;
    public  function storeImage($folderName, $file)
    {
        try {
            return Storage::disk('website')->put($folderName, $file);
        } catch (\Exception $e) {
            return '';
        } //end trycatch.
    } //end storeImage function.
    public  function storeImageToStorageFolder($folderName, $file)
    {
        try {
            return Storage::disk('storage')->put($folderName, $file);
        } catch (\Exception $e) {
            return '';
        } //end trycatch.
    } //end storeImageToStorageFolder function.
    public function deleteImage($file)
    {
        try {
            return Storage::disk('website')->delete($file);
        } catch (\Exception $e) {
            return '';
        } //end trycatch.
    } //end storeImage function.

    public function user_notification($user_id, $title, $message, $sender_user_id = null, $filter_keyword = null)
    {
        $user = User::find($user_id);
        if ($user) {
            $notification = Notification::create([
                'user_id' => $user_id,
                'sender_user_id' => $sender_user_id,
                'title' => $title,
                'message' => $message,
                'filter_keyword' => $filter_keyword,
            ]);

            // Broadcast real-time notification
            broadcast(new \App\Events\NotificationSent($notification, $user_id))->toOthers();
        }
    }

    /**
     * Send notification to all admins and super admins
     * 
     * @param string $title Notification title
     * @param string $message Notification message
     * @param int|null $sender_user_id ID of user who triggered the notification
     * @param string|null $filter_keyword Filter keyword for notification categorization
     * @return void
     */
    public function notifyAdmins($title, $message, $sender_user_id = null, $filter_keyword = null)
    {
        // Get all users with admin and super admin roles
        $admins = User::whereHas('roles', function($query) {
            $query->whereIn('name', ['admin', 'super admin']);
        })->get();

        // Send notification to each admin/super admin
        foreach ($admins as $admin) {
            $this->user_notification(
                $admin->id,
                $title,
                $message,
                $sender_user_id,
                $filter_keyword
            );
        }
    }
}
