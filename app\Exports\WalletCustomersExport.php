<?php

namespace App\Exports;

use App\Models\Booking;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class WalletCustomersExport implements FromCollection, WithHeadings, WithMapping
{
    protected $transactions;

    public function __construct($transactions)
    {
        $this->transactions = $transactions;
    }

    public function collection()
    {
        return $this->transactions;
    }

    public function headings(): array
    {
        return [
            'Booking ID',
            'Customer Name',
            'Service Name',
            'Professional',
            'Amount',
            'Status',
            'Date & Time'
        ];
    }

    public function map($transaction): array
    {
        return [
            $transaction->booking_number ?? 'N/A',
            $transaction->customer->name ?? 'N/A',
            $transaction->service->name ?? 'N/A',
            $transaction->provider->name ?? 'N/A',
            '$' . number_format($transaction->total_amount ?? 0, 2),
            $transaction->status_text,
            $transaction->created_at ? \Carbon\Carbon::parse($transaction->created_at)->format('d M, Y g:i A') : 'N/A'
        ];
    }
}
