@media screen and (max-width: 1025px) {

/* Table Responsive */
    #responsiveTable tr { display: block; margin-bottom: 15px; border-radius: 8px; overflow: hidden; box-shadow: 0px 1.13px 2.25px 0px #0000000d; }
    #responsiveTable td { display: block; padding: 12px 15px; text-align: left; border-radius: 0 !important; border-right: 1px solid #f0f0f0; border-left: 1px solid #f0f0f0; border-top: none; border-bottom: none; }
    #responsiveTable td::before { content: attr(data-label); font-weight: bold; margin-right: 15px; text-align: left; color: #555; display: inline-block; width: 100px; }
    #responsiveTable tr td:first-child { border-top-left-radius: 8px !important; border-top-right-radius: 8px !important; border-bottom-left-radius: 0 !important; }
    #responsiveTable tr td:last-child { border-bottom-left-radius: 8px !important; border-bottom-right-radius: 8px !important; border-top-right-radius: 0 !important; }
    .dataTables_info, .dataTables_paginate { text-align: center !important; float: none !important; }
}


@media(max-width:1920px){
  
}

@media (max-width: 1680px) {
    .customer-calender #calendar .width-1-slot {width: 149px;}
}

@media (max-width: 1520px) {
    .customer-calender #calendar .width-1-slot {   width: 144px;}
}

@media (max-width: 1440px) {
     .width-1-slot{width: 142px;}
     .reservation>h4 {margin: 20px 0 0 10px;}
     .reservation>ul {margin: 0px 0 20px 10px;}
     .customer-calender #calendar tr>td:first-child {padding: 5px 20px 5px 0;}
     .customer-calender #calendar .width-1-slot { width: 145px; }
}
@media(max-width:1366px){
    .cart-section .padding-block{padding-block: 9em;}
    .service-subcategory { gap: 11px;}
    #responsiveTable td {
    padding: 15px;
   
}

.business-home-sec .fs-24 {
    font-size: 18px;
}

.business-home-sec .fs-14 {
    font-size: 13px;
}
}
@media(max-width:1280px){
}
@media(max-width:1025px){
    td.w-400px {
    width: 100%;
}
}
@media(max-width:991px){
.fixed-web-header {
    width: 100vw;
}

/* Sidebar header adjustments for mobile */
body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-fixed=true][data-kt-app-sidebar-push-header=true] .app-header.admin-sidebar-changes,
body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-sticky=on][data-kt-app-sidebar-push-header=true] .app-header.admin-sidebar-changes {
margin-left: 1px !important;}

body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-fixed=true][data-kt-app-sidebar-push-header=true] .app-header.admin-sidebar-changes, 
body:not([data-kt-app-header-fixed=true])[data-kt-app-sidebar-sticky=on][data-kt-app-sidebar-push-header=true] .app-header.admin-sidebar-changes
{ margin-left: 1px !important;}
body .admin-padding #kt_app_main {margin-inline: 2em; margin-top: 5em;}

[data-kt-app-sidebar-fixed=true] .app-wrapper.admin-padding{margin-left: unset;}

div#kt_app_sidebar_mobile_toggle {
    background: #FFF;
    color: #FFF;
    z-index: 999;
    margin-left: 6em;

}

body:has(#kt_app_sidebar_mobile_toggle) ul.header-icon {
    margin-left: 4em;
}

    div#kt_app_sidebar_mobile_toggle.active {
        margin-left: 14.3em;
    background-color: lightgray;
color: #FFF; }


div#kt_app_sidebar_mobile_toggle.active  menu.menu-column{margin-top: 3em;}

.padding-block {
    padding-block: 11em;
}

}

@media(max-width:800px){
}
@media(max-width:767px){
        .padding-block {
        padding-block: 14em;
    }
}
@media(max-width:600px){

    .chart-container-booking {
    position: relative;
    width: 159px;
    height: 312px;
}
    .padding-block {
        padding-block: 19em;
    }
}
@media(max-width:480px){
        .search_box .search_input, .date_picker {

    min-width: 166px;
   
}

       .search_box .search_input, .date_picker {
        min-width: 357px;
    }
    .date_picker .date-picker-container .down-arrow {
        position: absolute;
        left: 292px;
    }

.date-picker-container .w-200px {
    width: 350px ;
}
}
@media(max-width:414px){
        .search_box .search_input, .date_picker {
        min-width: 146px;
    }
   .search_box .search_input, .date_picker {
        min-width: 360px;
    }

}
@media(max-width:375px){
        .search_box .search_input, .date_picker {
        min-width: 315px;
    }
        .date_picker .date-picker-container .down-arrow {
        position: absolute;
        left: 261px;
    }
}
@media(max-width:320px){
}
