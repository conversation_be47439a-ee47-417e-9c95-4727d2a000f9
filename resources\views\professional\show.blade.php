@extends('dashboard.layout.master')
@push('css')
    <style>
        .certificate-card {
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .certificate-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .certificate-expandable:hover {
            background-color: #f8f9fa;
        }

        .cursor-pointer {
            cursor: pointer !important;
        }

        .certificate-image {
            transition: transform 0.2s ease;
        }

        .certificate-image:hover {
            transform: scale(1.1);
        }

        .certificate-toggle-icon {
            transition: transform 0.3s ease;
        }

        .certificate-toggle-icon.rotated {
            transform: rotate(180deg);
        }

        .certificate-exception-details {
            animation: slideDown 0.3s ease;
        }

        .certificate-rejection-details {
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }

            to {
                opacity: 1;
                max-height: 200px;
            }
        }


        .accept-certificate-btn,
        .reject-certificate-btn {
            transition: all 0.2s ease;
            font-size: 12px;
            padding: 4px 8px;
        }

        .reject-certificate-btn {
            border: 1px solid gray
        }
    </style>
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard profile-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">

                <div class="col-md-2 mb-10">
                    <a href="{{ route('professionals') }}" class="add-btn mb-8 w-50"> <i class="fas fa-long-arrow-alt-left pe-3"></i> Back </a>
                </div>

                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="sora black">User Details</h4>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="card white-box friends-cards ">
                        <div class="card-header align-items-center justify-content-center flex-column gap-3 my-6">

                            <img src="{{ asset('website') . '/' . $user->profile->pic ?? '/website/assets/images/image_input_holder.png' }}"
                                class="customer_profile" alt="card-image"
                                onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'" />
                            <p class="fs-22 sora black semi_bold">{{ $user->name ?? '' }}</p>
                        </div>
                        <div class="card-body">
                            <p class="fs-12 normal sora light-black"><span
                                    class="me-3">@include('svg.building')</span>
                                {{ $user->email ?? '' }}</p>
                            @if ($user->profile->country)
                                <p class="fs-12 normal sora light-black"><span
                                        class="me-3">@include('svg.pin')</span>
                                    {{ $user->profile->location ?? '' }}</p>
                            @endif
                            @if ($user->approval == 1)
                                <div class="fs-12 normal sora light-black">
                                    <x-star-rating-display :rating="auth()->user()->average_rating" :total-reviews="auth()->user()->total_reviews" size="sm" />
                                </div>

                                <div class="d-flex gap-4 mt-5">
                                    @forelse ($user->socials as $social)
                                        <a href="{{ $social->link }}" target="_blank" class="logo-box">
                                            <img src="{{ asset('website') . '/' . $social->socialPlatform->image }}"
                                                alt="social-logo" height="30px" width="30px"
                                                onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'">
                                        </a>
                                    @empty
                                        <p>No Socials Found</p>
                                    @endforelse
                                </div>
                            @endif

                            <a class="blue-button mt-6 text-center"
                                href="{{ route('professional_profile', $user->profile->slug) }}">
                                View Public Profile </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Gallery Images</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        @foreach ($user->galleries as $gallery)
                                            @if ($gallery->image)
                                                <div class="col-md-3 profile-gallery">
                                                    <img src="{{ asset('website') . '/' . $gallery->image }}"
                                                        class="img-fluid"
                                                        alt="card-image" onerror="this.style.display='none'" />
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        <div class="col-md-6 ">
                                            <label for="full-name" class="form-label form-input-labels">Full
                                                name</label>
                                            <input type="text" class="form-control form-inputs-field"
                                                placeholder="Enter full-name" id="full-name" name="full-name"
                                                value="{{ $user->name ?? '' }}" readonly>
                                        </div>
                                        <div class="col-md-6 ">
                                            <label for="email" class="form-label form-input-labels">Email
                                                Address</label>
                                            <input type="email" class="form-control form-inputs-field"
                                                placeholder="Enter email address" id="email" name="email"
                                                value="{{ $user->email ?? '' }}" disabled>
                                        </div>
                                        <div class="col-md-6 ">
                                            <label for="phone-number" class="form-label form-input-labels">Phone
                                                Number</label>
                                            <input type="tel" class="form-control form-inputs-field"
                                                placeholder="Enter phone number " id="phone-number" name="phone-number"
                                                value="{{ $user->profile->phone ?? '' }}" readonly>
                                        </div>
                                        <div class="col-md-6 ">
                                            <label for="location" class="form-label form-input-labels">Location</label>
                                            <input type="text" class="form-control form-inputs-field"
                                                placeholder="Enter email address" id="location" name="location"
                                                value="{{ $user->profile->location ?? '' }}" readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Company details</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        <div class="col-md-12 ">
                                            <label for="company-name" class="form-label form-input-labels">Company
                                                name</label>
                                            <input type="text" class="form-control form-inputs-field"
                                                placeholder="Enter company name" id="company-name" name="company-name"
                                                value="{{ $user->profile->company_name ?? '' }}" readonly>
                                        </div>
                                        <div class="col-md-6 ">
                                            <label for="company-id" class="form-label form-input-labels">Company
                                                ID</label>
                                            <input type="email" class="form-control form-inputs-field"
                                                placeholder="Enter company id" id="company-id" name="company-id"
                                                value="{{ $user->profile->company_id ?? '' }}" readonly>
                                        </div>
                                        <div class="col-md-6 ">
                                            <label for="company-vat-number" class="form-label form-input-labels">Company
                                                VAT
                                                number</label>
                                            <input type="tel" class="form-control form-inputs-field"
                                                placeholder="Enter company vat number " id="company-vat-number"
                                                name="company-vat-number" value="{{ $user->profile->vat_number ?? '' }}"
                                                readonly>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Categories</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        <div class="col-md-12">
                                            <p class="fs-14 black regular">My Categories</p>
                                            <div class="d-flex gap-5 flex-wrap">
                                                @forelse ($userCategories as $category)
                                                    <div class="my-3">
                                                        <p
                                                            class="fs-14 sora light-black normal text-center service-details mb-1 w-100">
                                                            <strong>{{ $category->name }}</strong>
                                                        </p>
                                                        @if (isset($userSubcategoriesByCategory[$category->id]))
                                                            <div class="mt-5">
                                                                @foreach ($userSubcategoriesByCategory[$category->id] as $subcategory)
                                                                    <span
                                                                        class="fs-13 sora light-black normal w-100 service-details ">
                                                                        {{ $subcategory->name }}
                                                                    </span>
                                                                @endforeach
                                                            </div>
                                                        @endif
                                                    </div>
                                                @empty
                                                    <p class="fs-14 sora light-black normal service-details">No
                                                        Categories Found</p>
                                                @endforelse
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Product Certifications</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        <div class="col-md-12">
                                            <div class="d-flex gap-4 flex-wrap">
                                                @forelse ($user->product_cerficates as $productCertification)
                                                    <p
                                                        class="fs-14 sora light-black normal service-details align-items-center">
                                                        <span>
                                                            <img src="{{ asset('website') . '/' . $productCertification->image }}"
                                                                class="h-25px w-25px object-fit-contain rounded-pill top-rated-image"
                                                                alt="card-image"
                                                                onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'" />
                                                        </span>
                                                        <span> {{ $productCertification->name ?? '' }}</span>
                                                    </p>
                                                @empty
                                                    <p class="fs-14 sora light-black normal service-details">No
                                                        Product Certifications Found</p>
                                                @endforelse
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Certifications & Licenses</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        @forelse ($user->certificates as $certificate)
                                            <div class="col-md-6">
                                                <div class="card card-box p-0 certificate-card"
                                                    data-certificate-id="{{ $certificate->id }}">
                                                    <!-- Main Certificate Content -->
                                                    <div class="certificate-main-content p-3 d-flex gap-5 {{ $certificate->exception ? 'cursor-pointer certificate-expandable' : '' }}"
                                                        data-certificate-id="{{ $certificate->id }}"
                                                        @if ($certificate->exception) data-has-exception="true" @endif
                                                        @if ($certificate->approval == 2 && $certificate->rejection_reason) data-has-rejection="true" @endif>
                                                        <div class="certificate-image-container">
                                                            @if ($certificate->image)
                                                                <img src="{{ asset('website') . '/' . $certificate->image }}"
                                                                    class="h-50px w-50px object-fit-contain top-rated-image cursor-pointer certificate-image"
                                                                    alt="certificate-image"
                                                                    onclick="event.stopPropagation(); showCertificateModal('{{ asset('website') . '/' . $certificate->image }}', '{{ $certificate->title }}')"
                                                                    onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'" />
                                                            @else
                                                                <div
                                                                    class="h-50px w-50px d-flex align-items-center justify-content-center rounded">
                                                                    <i class="fas fa-certificate text-muted"></i>
                                                                </div>
                                                            @endif
                                                        </div>
                                                        <div class="certificate-info flex-grow-1">
                                                            <div class="d-flex align-items-center gap-2">
                                                                <p class="black sora fs-16 semi_bold m-0">
                                                                    {{ $certificate->title ?? '' }}</p>
                                                                @if ($certificate->exception || ($certificate->approval == 2 && $certificate->rejection_reason))
                                                                    <i class="fas fa-chevron-down certificate-toggle-icon"
                                                                        id="toggle-icon-{{ $certificate->id }}"></i>
                                                                @endif
                                                            </div>
                                                            <p class="black fs-14 normal m-0 mt-1">
                                                                <span class="link-gray">Issued by:
                                                                </span>{{ $certificate->issued_by ?? '' }}
                                                            </p>
                                                            <p class="black fs-14 normal m-0">
                                                                <span class="link-gray">Issue Date:
                                                                </span>{{ $certificate->issued_date ?? '' }}
                                                            </p>
                                                            <p class="black fs-14 normal m-0">
                                                                <span class="link-gray">End Date:
                                                                </span>{{ $certificate->end_date ?? '' }}
                                                            </p>
                                                            <!-- Certificate Status and Exception -->
                                                            <div class="mt-2">
                                                                @if ($certificate->exception)
                                                                    <span class="badge me-1"
                                                                        style="background-color: #ffc107; color: #FFF;">Exception</span>
                                                                @endif
                                                                
                                                                @if ($certificate->approval == 1)
                                                                    <span class="badge bg-success text-white">Approved</span>
                                                                @elseif ($certificate->approval == 2)
                                                                    <span class="badge bg-danger text-white">Rejected</span>
                                                                @else
                                                                    <span
                                                                        class="badge blue-badge text-white">Pending</span>
                                                                @endif
                                                            </div>

                                                            <!-- Admin Action Buttons -->
                                                            @if (auth()->user()->hasAnyRole(['admin', 'super admin']) && $certificate->approval == 0)
                                                                <div class="mt-9 d-flex gap-2 flex-end">
                                                                    <button type="button"
                                                                        class="add-btn accept-certificate-btn"
                                                                        data-certificate-id="{{ $certificate->id }}"
                                                                        data-certificate-title="{{ $certificate->title }}">
                                                                        <i class="fas fa-check me-1"></i>Accept
                                                                    </button>
                                                                    <button type="button"
                                                                        class="cancel-btn reject-certificate-btn"
                                                                        data-certificate-id="{{ $certificate->id }}"
                                                                        data-certificate-title="{{ $certificate->title }}"
                                                                        data-bs-toggle="modal"
                                                                        data-bs-target="#rejectCertificateModal">
                                                                        <i class="fas fa-times me-1"></i>Reject
                                                                    </button>
                                                                </div>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <!-- Exception Reason (Hidden by default) -->
                                                    @if ($certificate->exception)
                                                        <div class="certificate-exception-details"
                                                            id="exception-details-{{ $certificate->id }}"
                                                            style="display: none;">
                                                            <div class="border-top p-3"
                                                                style="background-color: #fff3cd;">
                                                                <p class="fs-14 text-dark mb-0">
                                                                    <strong>Reason:</strong>
                                                                    {{ $certificate->exception_reason ?? 'No reason provided' }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    @endif

                                                    <!-- Rejection Reason (Expandable) -->
                                                    @if ($certificate->approval == 2 && $certificate->rejection_reason)
                                                        <div class="certificate-rejection-details"
                                                            id="rejection-details-{{ $certificate->id }}">
                                                            <div class="border-top p-3"
                                                                style="background-color: #f8d7da; border-color: #f5c6cb;">
                                                                <p class="fs-14 text-dark mb-0">
                                                                    <strong><i
                                                                            class="fas fa-times-circle text-danger me-1"></i>Rejection
                                                                        Reason:</strong>
                                                                    {{ $certificate->rejection_reason }}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    @endif
                                                </div>
                                            </div>
                                        @empty
                                            <p class="fs-14 sora light-black normal service-details">No Certifications
                                                Found</p>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Availability</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        <div class="col-md-12">
                                            <p class="fs-14 black regular">Weekly Availability </p>
                                            <div class="d-flex gap-2 flex-wrap">
                                                @forelse ($user->openingHours as $availability)
                                                    <p class="fs-14 sora light-black normal service-details">
                                                        {{ $availability->day }}
                                                        ({{ date('H:i', strtotime($availability->open)) }} -
                                                        {{ date('H:i', strtotime($availability->close)) }})
                                                    </p>
                                                @empty
                                                    <p class="fs-14 sora light-black normal service-details">No
                                                        Availability Found</p>
                                                @endforelse
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <p class="fs-14 black regular">Holidays Availability</p>
                                            <div class="d-flex gap-4 flex-wrap">
                                                @forelse ($user->allHolidays as $holiday)
                                                    <p class="fs-14 sora light-black normal service-details">
                                                        {{ $holiday->name }}
                                                        ({{ $holiday->date }})
                                                    </p>
                                                    <p class="fs-14 sora light-black normal service-details">
                                                        @if (!$holiday->is_full_day && $holiday->start_time && $holiday->end_time)
                                                            {{ date('H:i', strtotime($holiday->start_time)) }} -
                                                            {{ date('H:i', strtotime($holiday->end_time)) }}
                                                        @endif
                                                    </p>
                                                @empty
                                                    <p class="fs-14 sora light-black normal service-details">No
                                                        Holidays Availability Found</p>
                                                @endforelse
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="card card-box p-0">
                                <div class="card-header align-items-center">
                                    <p class="sora black fs-16 semi_bold m-0">Intro Card</p>
                                </div>
                                <div class="card-body">
                                    <div class="row row-gap-5">
                                        @forelse ($user->introCards as $introCard)
                                            <div class="col-md-6">
                                                <div
                                                    class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                    <div
                                                        class="card-header border-0 p-0 justify-content-center align-items-center">
                                                        <img src="{{ asset('website') . '/' . $introCard->image }}"
                                                            class="h-35px w-35px  object-fit-contain top-rated-image"
                                                            alt="card-image"
                                                            onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'" />
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <p class="fs-16 sora w-700 m-0 dark-blue">
                                                            {{ $introCard->heading ?? '' }}</p>
                                                        <p class="fs-14 sora normal  m-0 light-gray">
                                                            {{ $introCard->description ?? '' }}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        @empty
                                            <p class="fs-14 sora light-black normal service-details">No
                                                Intro Cards Found</p>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <!-- Certificate Image Modal -->
    <div class="modal fade" id="certificateImageModal" tabindex="-1" aria-labelledby="certificateImageModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="certificateImageModalLabel">Certificate Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="certificateModalImage" src="" alt="Certificate" class="img-fluid rounded"
                        style="max-height: 70vh;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Reject Certificate Modal -->
    <div class="modal fade" id="rejectCertificateModal" tabindex="-1" aria-labelledby="rejectCertificateModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="rejectCertificateModalLabel">Reject Certificate</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="rejectCertificateForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="rejectionReason" class="form-label">Reason for Rejection <span
                                    class="text-danger">*</span></label>
                            <textarea class="form-control" id="rejectionReason" name="rejection_reason" rows="4"
                                placeholder="Please provide a reason for rejecting this certificate..." required></textarea>
                            <div class="invalid-feedback">
                                Please provide a reason for rejection.
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-times me-1"></i>Reject Certificate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state for toggle icons
            document.querySelectorAll('.certificate-expandable').forEach(element => {
                const certificateId = element.dataset.certificateId;
                const toggleIcon = document.getElementById(`toggle-icon-${certificateId}`);
                const exceptionDetails = document.getElementById(`exception-details-${certificateId}`);
                const rejectionDetails = document.getElementById(`rejection-details-${certificateId}`);
                
                if (toggleIcon) {
                    const hasVisibleDetails = (exceptionDetails && exceptionDetails.style.display === 'block') || 
                                           (rejectionDetails && rejectionDetails.style.display !== 'none');
                    if (hasVisibleDetails) {
                        toggleIcon.classList.add('rotated');
                    }
                }
            });

            // Function to toggle certificate details (exception and rejection)
            function toggleCertificateDetails(certificateId) {
                const exceptionDetails = document.getElementById(`exception-details-${certificateId}`);
                const rejectionDetails = document.getElementById(`rejection-details-${certificateId}`);
                const toggleIcon = document.getElementById(`toggle-icon-${certificateId}`);

                // Toggle exception details if exists
                if (exceptionDetails) {
                    if (exceptionDetails.style.display === 'none' || exceptionDetails.style.display === '') {
                        exceptionDetails.style.display = 'block';
                    } else {
                        exceptionDetails.style.display = 'none';
                    }
                }

                // Toggle rejection details if exists
                if (rejectionDetails) {
                    if (rejectionDetails.style.display === 'none') {
                        rejectionDetails.style.display = 'block';
                    } else {
                        rejectionDetails.style.display = 'none';
                    }
                }

                // Update toggle icon if any details are shown
                if (toggleIcon) {
                    const hasVisibleDetails = (exceptionDetails && exceptionDetails.style.display === 'block') || 
                                           (rejectionDetails && rejectionDetails.style.display !== 'none');
                    if (hasVisibleDetails) {
                        toggleIcon.classList.add('rotated');
                    } else {
                        toggleIcon.classList.remove('rotated');
                    }
                }
            }

            // Function to show certificate image in modal
            function showCertificateModal(imageSrc, certificateTitle) {
                const modal = new bootstrap.Modal(document.getElementById('certificateImageModal'));
                const modalImage = document.getElementById('certificateModalImage');
                const modalTitle = document.getElementById('certificateImageModalLabel');

                modalImage.src = imageSrc;
                modalTitle.textContent = certificateTitle || 'Certificate Image';

                modal.show();
            }

            // Event delegation for certificate expansion
            document.addEventListener('click', function(event) {
                const certificateContent = event.target.closest('.certificate-expandable');
                if (certificateContent && (certificateContent.dataset.hasException === 'true' || certificateContent.dataset.hasRejection === 'true')) {
                    const certificateId = certificateContent.dataset.certificateId;
                    toggleCertificateDetails(certificateId);
                }
            });

            // Event delegation for certificate images
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('certificate-image')) {
                    event.stopPropagation();
                    const imageSrc = event.target.src;
                    const certificateTitle = event.target.closest('.certificate-card').querySelector(
                        '.certificate-info p').textContent;
                    showCertificateModal(imageSrc, certificateTitle);
                }
            });

            // Add keyboard support for accessibility
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Enter' || event.key === ' ') {
                    const target = event.target.closest('.certificate-expandable');
                    if (target && (target.dataset.hasException === 'true' || target.dataset.hasRejection === 'true')) {
                        event.preventDefault();
                        const certificateId = target.dataset.certificateId;
                        toggleCertificateDetails(certificateId);
                    }
                }
            });

            // Certificate approval/rejection functionality
            document.addEventListener('click', function(event) {
                if (event.target.closest('.accept-certificate-btn')) {
                    const button = event.target.closest('.accept-certificate-btn');
                    const certificateId = button.dataset.certificateId;
                    const certificateTitle = button.dataset.certificateTitle;

                    Swal.fire({
                        title: 'Accept Certificate?',
                        text: `Are you sure you want to accept "${certificateTitle}"?`,
                        icon: 'question',
                        showCancelButton: true,
                        confirmButtonColor: '#28a745',
                        cancelButtonColor: '#6c757d',
                        confirmButtonText: 'Yes, Accept',
                        cancelButtonText: 'Cancel'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            acceptCertificate(certificateId, button);
                        }
                    });
                }

                if (event.target.closest('.reject-certificate-btn')) {
                    const button = event.target.closest('.reject-certificate-btn');
                    const certificateId = button.dataset.certificateId;
                    const certificateTitle = button.dataset.certificateTitle;

                    // Store certificate data for modal form submission
                    document.getElementById('rejectCertificateForm').dataset.certificateId = certificateId;
                    document.getElementById('rejectCertificateModalLabel').textContent =
                        `Reject Certificate: ${certificateTitle}`;

                    // Clear previous form data
                    document.getElementById('rejectionReason').value = '';
                    document.getElementById('rejectionReason').classList.remove('is-invalid');
                }
            });

            // Function to accept certificate
            function acceptCertificate(certificateId, button) {
                // Disable button and show loading
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';

                fetch(`/certificates/${certificateId}/accept`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: data.title,
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                location.reload(); // Reload to show updated status
                            });
                        } else {
                            throw new Error(data.message);
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'Error',
                            text: error.message || 'Failed to accept certificate',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        // Re-enable button
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-check me-1"></i>Accept';
                    });
            }

            // Function to reject certificate
            function rejectCertificate(certificateId, button) {
                // Disable button and show loading
                button.disabled = true;
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';

                fetch(`/certificates/${certificateId}/reject`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute(
                                'content')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: data.title,
                                text: data.message,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            }).then(() => {
                                location.reload(); // Reload to show updated status
                            });
                        } else {
                            throw new Error(data.message);
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'Error',
                            text: error.message || 'Failed to reject certificate',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        // Re-enable button
                        button.disabled = false;
                        button.innerHTML = '<i class="fas fa-times me-1"></i>Reject';
                    });
            }
        });

        // Handle rejection form submission
        document.getElementById('rejectCertificateForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const certificateId = this.dataset.certificateId;
            const rejectionReason = document.getElementById('rejectionReason').value.trim();

            // Validate rejection reason
            if (!rejectionReason) {
                document.getElementById('rejectionReason').classList.add('is-invalid');
                return;
            }

            // Disable submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Rejecting...';

            // Send rejection request
            const formData = new FormData();
            formData.append('rejection_reason', rejectionReason);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            fetch(`/certificates/${certificateId}/reject`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Close modal
                        const modal = bootstrap.Modal.getInstance(document.getElementById(
                            'rejectCertificateModal'));
                        modal.hide();

                        // Show success message
                        Swal.fire({
                            title: 'Success',
                            text: data.message,
                            icon: 'success',
                            confirmButtonText: 'OK'
                        }).then(() => {
                            // Reload page to show updated status
                            location.reload();
                        });
                    } else {
                        throw new Error(data.message);
                    }
                })
                .catch(error => {
                    Swal.fire({
                        title: 'Error',
                        text: error.message || 'Failed to reject certificate',
                        icon: 'error',
                        confirmButtonText: 'OK'
                    });
                })
                .finally(() => {
                    // Re-enable submit button
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                });
        });
    </script>
@endpush
