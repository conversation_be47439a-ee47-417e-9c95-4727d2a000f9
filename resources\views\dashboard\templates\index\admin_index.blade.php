<div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-home add-service">
    <div id="kt_app_content_container" class="app-container container-fluid">
        <section class="business-home-sec padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12">
                        <h6 class="semi_bold">Welcome, {{ auth()->user()->name ?? 'Admin' }}</h6>
                        <p class="fs-14 normal" id="periodIndicator">Showing data for: All Time</p>
                    </div>
                    <div class=" col-xl-6 col-lg-6 col-md-6 col-sm-12">
                        <div class="d-flex justify-content-md-end justify-content-start align-items-center gap-5 pb-md-0 pb-sm-10 pb-10">
                            <!-- export btn -->
                            <div class="search_box d-block">
                                <button id="exportBtn" class="search_input fs-14 normal link-gray">
                                    Export All Data <i class="ms-1 bi bi-file-arrow-down"></i>
                                </button>
                            </div>
                            <!-- category -->
                            <div class="search_box select-box">
                                <select id="periodFilter" class="search_input">
                                    <option value="all">All Time</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row row-gap-5 mb-10 card-wrapper">
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="{{ route('booking') }}">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        @include('svg.calender')
                                    </div>
                                </div>
                                <div class="card-body w-50">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Bookings
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $totalBookings ?? 0 }}">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="{{ route('customers') }}">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        @include('svg.customer')
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Customers
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $totalCustomers }}">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="{{ route('professionals') }}">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        @include('svg.dollar')
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Professionals
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $totalProfessionals }}">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="{{ route('wallet') }}">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        @include('svg.professional')
                                    </div>
                                </div>
                                <div class="card-body w-50 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Revenue
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $totalRevenue ?? 0 }}" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="row row-gap-10">
                    <div class="col-xxl-7 col-xl-12">
                        <div class="  card-box">
                            <p class="black sora semi_bold">Revenue</p>
                            <canvas id="popularServicesChart"></canvas>
                        </div>
                    </div>
                    <div class="col-xxl-5 col-xl-12">
                        <div class="  card-box">
                            <p class="black sora semi_bold">Top Category Purchases</p>
                            <div class="d-flex align-items-center justify-content-center">
                                <div class="chart-container-booking">
                                    <canvas id="bookingChart" class="bookingchart"></canvas>
                                </div>
                                <div id="chartLegend"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row row-gap-5 pt-12">
                    <div class="col-md-12 d-flex justify-content-between align-items-center">
                        <h6 class="sora black">Professionals & Customers</h6>
                    </div>
                    <div class="col-lg-12">
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active business-services" id="professionals-tab"
                                    data-bs-toggle="pill" data-bs-target="#pills-professionals" type="button"
                                    role="tab" aria-controls="pills-professionals" aria-selected="true">
                                    Professionals
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link business-services" id="Customers-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-customers" type="button" role="tab"
                                    aria-controls="pills-customers" aria-selected="false">
                                    Customers
                                </button>
                            </li>
                        </ul>

                        <div class="tab-content" id="pills-tabContent">
                            <div class="tab-pane fade show active" id="pills-professionals" role="tabpanel"
                                aria-labelledby="professionals-tab" tabindex="0">
                                <div class="table-container">
                                    <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                        <div class="search_box d-block my-3 ms-auto">
                                            <a class=" add-btn" href="{{ route('professionals') }}">
                                                View All
                                            </a>
                                        </div>
                                    </div>
                                    <table id="responsiveTable" class="display" style="width: 100%">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Email Address</th>
                                                <th>Category</th>
                                                <th>service category</th>
                                                <th>Action</th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($professionals as $professional)
                                                <tr>
                                                    <td>
                                                        <div
                                                            class="card flex-row shadow-none p-0 gap-3 align-items-center">
                                                            <div class="card-header p-0 border-0 align-items-start">
                                                                <img src="{{ asset('website').'/'.$professional->profile->pic ?? '' }}"

                                                                    alt="card-image" onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'"/>
                                                            </div>
                                                            <div class="card-body p-0 ">
                                                                <p class="fs-16 regular black m-0 pb-5">
                                                                    {{ $professional->name }}</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td data-label="EMAIL ADDRESS">{{ $professional->email }}</td>
                                                    <td data-label="SERVICE CATEGORY">Hair Stylist</td>

                                                    <td data-label="STATUS"
                                                        class="professional-status status  {{ $professional->status == 1 ? 'paid-status' : 'unpaid-status' }}">
                                                        {{ $professional->status == 1 ? 'Active' : 'Inactive' }} 
                                                    </td>

                                                    <td data-label="JOINED DATE">
                                                        {{ $professional->created_at->format('M d, Y') }}</td>
                                                    <td data-label="TOTAL BOOKINGS">
                                                        {{ $professional?->bookings?->count() ?? 0 }}</td>

                                                    <td data-label="">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button"
                                                                id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu"
                                                                aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <a class="dropdown-item complete fs-14 regular "
                                                                        href="{{ route('professional.change_status', $professional->ids) }}">
                                                                        <i
                                                                            class="bi bi-check-circle complete-icon"></i>
                                                                        Change Status
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item complete fs-14 regular "
                                                                        href="{{ route('professional.show', $professional->ids) }}">
                                                                        <i
                                                                            class="bi bi-check-circle complete-icon"></i>
                                                                        View Profile
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center">No Professionals Found</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pills-customers" role="tabpanel"
                                aria-labelledby="Customers-tab" tabindex="1">
                                <div class="table-container">
                                    <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                        <div class="search_box d-block ms-auto">
                                            <a class="add-btn" href="{{ route('customers') }}">
                                                View All
                                            </a>
                                        </div>

                                    </div>
                                    <table id="responsiveTable" class="display" style="width: 100%">
                                        <thead>
                                            <tr>
                                                <th></th>
                                                <th>Email Address</th>
                                                <th>Category</th>
                                                <th>Bookings</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @forelse ($customers as $customer)
                                                <tr>
                                                    <td>
                                                        <div
                                                            class="card  flex-row shadow-none p-0 gap-3 align-items-center">
                                                            <div class="card-header p-0 border-0 align-items-start">
                                                                <img src="{{ asset('website').'/'.($customer?->profile?->pic ?? '') }}"

                                                                    alt="card-image" onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'" />
                                                            </div>
                                                            <div class="card-body p-0 ">
                                                                <p class="fs-16 regular black m-0 pb-5">
                                                                    {{ $customer->name }}</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td data-label="EMAIL ADDRESS">{{ $customer->email }}</td>
                                                    <td data-label="STATUS"
                                                        class="professional-status status {{ $customer->status == 1 ? 'paid-status' : 'unpaid-status' }}">
                                                        {{ $customer->status == 1 ? 'Active' : 'Inactive' }}
                                                    </td>

                                                    <td data-label="JOINED DATE">
                                                        {{ $customer->created_at->format('M d, Y') }}</td>
                                                    <td data-label="">
                                                        <div class="dropdown">
                                                            <button class="drop-btn" type="button"
                                                                id="dropdownMenuButton" data-bs-toggle="dropdown"
                                                                aria-expanded="false">
                                                                <i class="bi bi-three-dots-vertical"></i>
                                                            </button>
                                                            <ul class="dropdown-menu"
                                                                aria-labelledby="dropdownMenuButton">
                                                                <li>
                                                                    <a class="dropdown-item complete fs-14 regular "
                                                                        href="{{ route('customer.change_status', ['id' => $customer?->ids]) }}">
                                                                        <i
                                                                            class="bi bi-check-circle complete-icon"></i>
                                                                        Change Status
                                                                    </a>
                                                                </li>
                                                                <li>
                                                                    <a class="dropdown-item complete fs-14 regular "
                                                                        href="{{ route('customer.show', $customer->ids) }}">
                                                                        <i
                                                                            class="bi bi-check-circle complete-icon"></i>
                                                                        View Profile
                                                                    </a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center">No Customers Found</td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>



@push('js')
    <!-- donut-chart -->
    <script>
        var ctx4 = document.getElementById('bookingChart');
        var blue = '#62B2FD';
        var green = '#9BDFC4';
        var pink = '#F99BAB';
        var lightGray = '#F3F4F6';


        @php
            $defaultTopCategories = [
                'labels' => [],
                'data' => []
            ];
        @endphp
        // Get dynamic category data from server
        const topCategories = @json($topCategories ?? $defaultTopCategories);

        // Check if we have data
        const hasData = topCategories.labels && topCategories.labels.length > 0 && topCategories.data && topCategories.data.length > 0;

        const config4 = {
            type: 'doughnut',
            data: {
                labels: hasData ? topCategories.labels : [''],
                datasets: [{
                    label: 'Booking Analytics',
                    data: hasData ? topCategories.data : [100],
                    backgroundColor: hasData ? [blue, pink, green] : ['#F3F4F6'],
                    borderColor: '#fff',
                    borderWidth: 3,
                    hoverOffset: 0,
                }]
            },
            options: {
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: hasData,
                        callbacks: {
                            title: (context) => context[0].label,
                            label: (context) => `${context.raw} Bookings`
                        }
                    }
                },

                cutout: '75%',
                responsive: true,
                maintainAspectRatio: false
            }
        };

        var myChart4 = new Chart(ctx4, config4);

        // Custom Legend (skip light grey entry)
        function createCustomLegend(chart) {
            const legendContainer = document.getElementById('chartLegend');
            const data = chart.data;
            let legendHTML = '';

            if (!hasData) {
                legendHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px; padding: 20px;">
                        <span style="font-size: 14px; color: #9CA3AF; font-style: italic;">
                            No category data available
                        </span>
                    </div>
                `;
            } else {
                data.labels.forEach((label, index) => {
                    if (label !== '') { // Skip light gray segments
                        const color = data.datasets[0].backgroundColor[index];

                        legendHTML += `
                            <div style="display: flex; align-items: center; margin-bottom: 10px;">
                                <span style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; display: inline-block; margin-right: 10px;"></span>
                                <span style="font-size: 12px; color: #6B7280; margin-right: 6px;">${data.datasets[0].data[index]}%</span>
                                <span style="font-size: 12px; font-weight: 500; color: #111827;">
                                    ${label}
                                </span>
                            </div>
                        `;
                    }
                });
            }

            legendContainer.innerHTML = legendHTML;
        }

        createCustomLegend(myChart4);
    </script>

    <!-- Admin Dashboard Dynamic Filtering and Export -->
    <script>
        $(document).ready(function() {
            // Handle period filter change
            $('#periodFilter').on('change', function() {
                const period = $(this).val();
                updatePeriodIndicator(period);
                updateDashboardData(period);
            });

            // Handle export button click
            $('#exportBtn').on('click', function() {
                const period = $('#periodFilter').val();
                exportDashboardData(period);
            });

            // Function to update dashboard data
            function updateDashboardData(period) {
                $.ajax({
                    url: '{{ route("admin.dashboard.filter") }}',
                    type: 'GET',
                    data: { period: period },
                    beforeSend: function() {
                        // Show loading state
                        $('.card-wrapper').addClass('opacity-50');
                        $('#periodFilter').prop('disabled', true);
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update the dashboard cards with new data
                            updateDashboardCards(response.data);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error filtering dashboard data:', error);
                        alert('Error filtering data. Please try again.');
                    },
                    complete: function() {
                        // Remove loading state
                        $('.card-wrapper').removeClass('opacity-50');
                        $('#periodFilter').prop('disabled', false);
                    }
                });
            }

            // Function to update dashboard cards
            function updateDashboardCards(data) {
                // Update Total Bookings
                const bookingsCard = $('.card-wrapper').find('p:contains("Total Bookings")').next('p');
                bookingsCard.attr('data-kt-countup-value', data.totalBookings);
                bookingsCard.text(data.totalBookings);

                // Update Total Customers
                const customersCard = $('.card-wrapper').find('p:contains("Total Customers")').next('p');
                customersCard.attr('data-kt-countup-value', data.totalCustomers);
                customersCard.text(data.totalCustomers);

                // Update Total Professionals
                const professionalsCard = $('.card-wrapper').find('p:contains("Total Professionals")').next('p');
                professionalsCard.attr('data-kt-countup-value', data.totalProfessionals);
                professionalsCard.text(data.totalProfessionals);

                // Update Total Revenue
                const revenueCard = $('.card-wrapper').find('p:contains("Total Revenue")').next('p');
                revenueCard.attr('data-kt-countup-value', data.totalRevenue);
                revenueCard.text('$' + parseFloat(data.totalRevenue).toLocaleString());

                // Update charts with dynamic data
                updateCharts(data);

                // Reinitialize countup animations if available
                setTimeout(function() {
                    if (typeof KTUtil !== 'undefined' && KTUtil.components && KTUtil.components.CountUp) {
                        KTUtil.components.CountUp.init();
                    }
                }, 100);
            }

            // Function to update charts with dynamic data
            function updateCharts(data) {
                // Update booking categories chart
                if (data.topCategories && typeof myChart4 !== 'undefined') {
                    const hasData = data.topCategories.labels && data.topCategories.labels.length > 0 && data.topCategories.data && data.topCategories.data.length > 0;

                    myChart4.data.labels = hasData ? data.topCategories.labels : [''];
                    myChart4.data.datasets[0].data = hasData ? data.topCategories.data : [100];
                    myChart4.data.datasets[0].backgroundColor = hasData ? [blue, pink, green] : ['#F3F4F6'];

                    // Update tooltip setting
                    myChart4.options.plugins.tooltip.enabled = hasData;

                    myChart4.update();
                    // Update custom legend
                    createCustomLegend(myChart4);
                }

                // Update revenue line chart
                if (data.revenueData && data.revenueData.daily_revenue && typeof popularServicesChart !== 'undefined') {
                    popularServicesChart.data.labels = data.revenueData.daily_revenue.labels;
                    popularServicesChart.data.datasets[0].data = data.revenueData.daily_revenue.professional_bookings;
                    popularServicesChart.data.datasets[1].data = data.revenueData.daily_revenue.customer_bookings;
                    popularServicesChart.update();
                }
            }

            // Function to export dashboard data
            function exportDashboardData(period) {
                // Show loading state on export button
                const exportBtn = $('#exportBtn');
                const originalText = exportBtn.html();
                exportBtn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Exporting...');
                exportBtn.prop('disabled', true);

                // Create export URL
                const exportUrl = '{{ route("admin.dashboard.export") }}' + '?period=' + period;

                // Trigger download
                window.location.href = exportUrl;

                // Reset button after a delay
                setTimeout(function() {
                    exportBtn.html(originalText);
                    exportBtn.prop('disabled', false);
                }, 2000);
            }

            // Function to update period indicator
            function updatePeriodIndicator(period) {
                let periodText = '';
                switch(period) {
                    case 'weekly':
                        periodText = 'This Week';
                        break;
                    case 'monthly':
                        periodText = 'This Month';
                        break;
                    default:
                        periodText = 'All Time';
                        break;
                }
                $('#periodIndicator').text('Showing data for: ' + periodText);
            }
        });
    </script>

    <!-- revenue-chart -->
    <script>
        const ctx = document.getElementById('popularServicesChart').getContext('2d');

        @php
            $defaultRevenueData = [
                'daily_revenue' => [
                    'labels' => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    'customer_bookings' => [0, 0, 0, 0, 0, 0, 0],
                    'professional_bookings' => [0, 0, 0, 0, 0, 0, 0]
                ]
            ];
        @endphp
        // Get dynamic data from server
        const revenueData = @json($revenueData ?? $defaultRevenueData);


        const data = {
            labels: revenueData.daily_revenue.labels,
            datasets: [{
                    label: 'By Professionals (Subscriptions)',
                    data: revenueData.daily_revenue.professional_bookings,
                    borderColor: '#3B82F6',
                    backgroundColor: function(context) {
                        const chart = context.chart;
                        const {
                            ctx,
                            chartArea
                        } = chart;
                        if (!chartArea) return null;

                        const gradient = ctx.createLinearGradient(0, chartArea.bottom, 0, chartArea.top);
                        gradient.addColorStop(0, 'rgba(59, 130, 246, 0.00)');
                        gradient.addColorStop(1, 'rgba(59, 130, 246, 0.15)');
                        return gradient;
                    },
                    fill: true,
                    tension: 0.5,
                    pointBackgroundColor: '#3B82F6',
                    pointBorderColor: 'transparent',
                    pointRadius: 1,
                    pointHoverRadius: 7,
                },
                {
                    label: 'By Customers (Bookings)',
                    data: revenueData.daily_revenue.customer_bookings,
                    borderColor: '#0BC688',
                    backgroundColor: 'rgba(11, 198, 136, 0.1)',
                    fill: false,
                    tension: 0.5,
                    pointBackgroundColor: '#0BC688',
                    pointBorderColor: 'transparent',
                    pointRadius: 0,
                    pointHoverRadius: 7,
                }
            ]
        };

        const config = {
            type: 'line',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: true,
                        position: 'bottom',
                        align: 'start',
                        labels: {
                            usePointStyle: true,
                            pointStyle: 'dot',
                            padding: 20,
                            boxWidth: 5,
                            boxHeight: 5,
                        }
                    },
                    tooltip: {
                        enabled: false, // Disable the default tooltip
                        external: function(context) {
                            // Tooltip Element
                            let tooltipEl = document.getElementById('chartjs-tooltip');

                            // Create element on first render
                            if (!tooltipEl) {
                                tooltipEl = document.createElement('div');
                                tooltipEl.id = 'chartjs-tooltip';
                                tooltipEl.style.background = 'white';
                                tooltipEl.style.borderRadius = '8px';
                                tooltipEl.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
                                tooltipEl.style.color = '#111827';
                                tooltipEl.style.opacity = 1;
                                tooltipEl.style.pointerEvents = 'none';
                                tooltipEl.style.position = 'absolute';
                                tooltipEl.style.transform = 'translate(-50%, -100%)';
                                tooltipEl.style.transition = 'all .1s ease';
                                tooltipEl.style.padding = '8px 12px';
                                tooltipEl.style.fontFamily = 'sans-serif';
                                tooltipEl.style.fontSize = '14px';
                                document.body.appendChild(tooltipEl);
                            }

                            // Hide if no tooltip
                            const tooltipModel = context.tooltip;
                            if (tooltipModel.opacity === 0) {
                                tooltipEl.style.opacity = 0;
                                return;
                            }

                            const dataPoint = tooltipModel.dataPoints[0];
                            const label = dataPoint.dataset.label;
                            const value = dataPoint.raw;
                            const color = dataPoint.element.options.backgroundColor;

                            // Set inner HTML
                            tooltipEl.innerHTML = `
                                <div style="display: flex; align-items: center; gap: 8px;">
                                    <span style="width: 10px; height: 10px; border-radius: 50%; background:${color}; display:inline-block;"></span>
                                    <div>
                                        <div style="font-weight: 600; font-size: 16px;">$${value.toLocaleString()}</div>
                                        <div style="font-size: 13px; color: #6B7280;">${label}</div>
                                    </div>
                                </div>
                            `;

                            // Positioning
                            const {
                                offsetLeft: positionX,
                                offsetTop: positionY
                            } = context.chart.canvas;
                            tooltipEl.style.left = positionX + tooltipModel.caretX + 'px';
                            tooltipEl.style.top = positionY + tooltipModel.caretY + 'px';
                            tooltipEl.style.opacity = 1;
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 12,
                                weight: '400',
                                opacity: 0.6,
                            },
                            color: '#363636'
                        }
                    },
                    y: {
                        grid: {
                            display: true
                        },
                        beginAtZero: true,
                        min: 50,
                        max: 350,
                        ticks: {
                            font: {
                                size: 12,
                                weight: 400,
                            },
                            color: '#363636',
                            stepSize: 50,
                            callback: function(value) {
                                return '$' + value;
                            }
                        }
                    }
                }
            }
        };

        const popularServicesChart = new Chart(ctx, config);
    </script>
@endpush
