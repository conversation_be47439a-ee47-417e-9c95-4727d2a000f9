<?php

namespace App\Exports;

use App\Models\Booking;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class WalletProfessionalsExport implements FromCollection, WithHeadings, WithMapping
{
    protected $transactions;

    public function __construct($transactions)
    {
        $this->transactions = $transactions;
    }

    public function collection()
    {
        return $this->transactions;
    }

    public function headings(): array
    {
        return [
            'Professional Name',
            'Subscription Name',
            'Subscription Type',
            'Price',
            'Status',
            'Start Date',
            'End Date'
        ];
    }

    public function map($transaction): array
    {
        return [
            $transaction->user->name ?? 'N/A',
            $transaction->subscription->name ?? 'N/A',
            $transaction->subscription_type ?? 'N/A',
            '$' . number_format($transaction->subscription_price ?? 0, 2),
            $transaction->status == 1 ? 'Active' : 'Inactive',
            $transaction->start_date ? \Carbon\Carbon::parse($transaction->start_date)->format('d M, Y') : 'N/A',
            $transaction->end_date ? \Carbon\Carbon::parse($transaction->end_date)->format('d M, Y') : 'N/A'
        ];
    }
}
