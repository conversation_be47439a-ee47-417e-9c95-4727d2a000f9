@extends(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-home notification-sec">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row justify-content-center">
                <div class="col-md-10 ">
                    <div class="card card-box notification-bg p-0">
                        <div
                            class="card-header notification-header d-flex justify-content-between align-items-center p-4 flex-wrap gap-2">
                            <p class="fs-18 semi_bold sora mb-0">Notifications</p>
                            <div class="d-flex align-items-center gap-3">
                                <!-- Mark all as read -->
                                <a href="#" class="add-btn fs-16 semi_bold" id="mark-all-read">Mark all as read</a>
                                <div class="notification-filter p-0">
                                    <!-- Filter dropdown -->
                                    <select class="form-select filter-select filter-dropdown" id="filter-category"
                                        data-control="select2" data-placeholder="Select category" data-hide-search="true"
                                        data-dropdown-css-class="w-200px">
                                        <option value="filters" selected disabled hidden>Filters</option>
                                        <option value="all">All</option>
                                        <option value="booking">Bookings</option>
                                        <option value="reschedule">Reschedule</option>
                                        <option value="canceled">Canceled</option>
                                        <option value="reviews">Reviews</option>
                                        <option value="subscription">Subscriptions</option>
                                        <option value="friend_request">Friend Requests</option>
                                        <option value="registration">Registrations</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="scrollbar ">
                                <div class="row row-gap-2" id="notifications-container">
                                    @forelse ($notifications as $notification)
                                        <div class="col-lg-12 notification-item">
                                            <div
                                                class="card noti-content flex-row gap-5 align-items-center justify-content-center shadow-none"
                                                data-notification-id="{{ $notification->id }}"
                                                data-filter-keyword="{{ $notification->filter_keyword ?? 'general' }}"
                                                style="cursor: pointer;">
                                                <div class="card-header profile-img border-0 align-items-center">
                                                    <img src="{{ asset('website').'/'.$notification?->senderUser?->profile?->pic}}"
                                                     alt="profile" onerror="this.src='{{ asset('website/assets/images/image_input_holder.png') }}'">
                                                </div>
                                                <div class="card-body px-4 py-0">
                                                    <div class="noti-heading">
                                                        
                                                        <div class="d-flex justify-content-between mail-massage">
                                                             <p class="fs-13 semi_bold light-black m-0 ">{{ $notification->title ?? '' }}</p>
                                                        <p class="fs-14 normal dark-cool-gray line-clamp-1 mb-0">{{ $notification->created_at->diffForHumans() }}</p>
                                                    </div>
                                                        <p class="fs-13 normal light-black opacity-8 m-0"> {{ $notification->message ?? '' }}</p>
                                                    </div>
                                                   
                                                </div>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="col-lg-12" id="no-notifications-default">
                                            <div class="d-flex flex-column align-items-center justify-content-center py-5 my-5">
                                                <i class="fas fa-bell-slash fa-4x text-muted mb-4"></i>
                                                <h4 class="text-muted mb-2">No Notifications Found</h4>
                                                <p class="text-muted fs-14 text-center">You don't have any notifications at the moment.<br>New notifications will appear here when you receive them.</p>
                                            </div>
                                        </div>
                                    @endforelse

                                    <!-- No notifications found message for filtered results -->
                                    <div class="col-lg-12" id="no-notifications-filtered" style="display: none;">
                                        <div class="d-flex flex-column align-items-center justify-content-center py-5 my-5">
                                            <i class="fas fa-bell-slash fa-4x text-muted mb-4"></i>
                                            <h4 class="text-muted mb-2">No Notifications Found</h4>
                                            <p class="text-muted fs-14 text-center">No notifications found for the selected filter.<br>Try selecting a different filter or check back later.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
<script>
$(document).ready(function() {
    // Mark all notifications as read
    $('#mark-all-read').click(function(e) {
        e.preventDefault();

        $.ajax({
            url: '/notifications/mark-all-read',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: function(response) {
                if (response.success) {
                    // Hide notification counter
                    $('#notification-counter').hide();

                    // Show success message
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    }

                    // Optionally reload the page to show updated notifications
                    location.reload();
                }
            },
            error: function() {
                if (typeof toastr !== 'undefined') {
                    toastr.error('Failed to mark notifications as read');
                }
            }
        });
    });

    // Filter notifications by category
    $('#filter-category').change(function() {
        const selectedFilter = $(this).val();

        // Hide the filtered no notifications message initially
        $('#no-notifications-filtered').hide();

        if (selectedFilter === 'all') {
            $('.notification-item').show();
        } else {
            $('.notification-item').hide();
            $('.noti-content[data-filter-keyword="' + selectedFilter + '"]').closest('.notification-item').show();

            // Check if any notifications are visible after filtering
            const visibleNotifications = $('.notification-item:visible').length;
            if (visibleNotifications === 0) {
                $('#no-notifications-filtered').show();
            }
        }
    });

    // Mark individual notification as read when clicked
    $('.noti-content').click(function() {
        const notificationId = $(this).data('notification-id');
        if (notificationId) {
            $.ajax({
                url: '/notifications/' + notificationId + '/mark-read',
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    if (response.success) {
                        // Decrement counter
                        if (typeof decrementNotificationCounter === 'function') {
                            decrementNotificationCounter();
                        }
                    }
                }
            });
        }
    });
});
</script>
@endpush
