<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Lara<PERSON>\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Role;
use App\Events\UserOnline;
use App\Mail\UserPasswordMail;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;

class SocialAuthController extends Controller
{
    public function redirectToGoogle($user_type = null)
    {
        // Check if the user type is valid
        if (!in_array($user_type, ['customer', 'professional'])) {
            return redirect()->route('home')->with('error', 'Invalid user type');
        }

        // Now check if the email already exists in the database
        $email = session('social_email'); // Assume this was set on the frontend with email input

        // Check if user exists
        $existingUser = User::where('email', $email)->first();

        if ($existingUser) {
            // Check if the user role matches the selected user type
            $userRole = $existingUser->getRoleNames()->first();

            if ($user_type == "professional" && !in_array($userRole, ["individual", "business", "professional"])) {
                return redirect()->route('home')->with('error', "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            } elseif ($user_type == "customer" && $userRole != "customer") {
                return redirect()->route('home')->with('error', "This email is registered as a {$userRole}. Please select the correct account type or use a different email.");
            }
        }
        session(['user_type' => $user_type]);
        return Socialite::driver('google')->redirect();
    }

    // public function handleGoogleCallback()
    // {
    //     try {
    //         $googleUser = Socialite::driver('google')->user();
    //         $user_type = session('user_type', 'customer');
    //         $email = $googleUser->getEmail();
    //         $name = $googleUser->getName();

    //         // Check if user already exists
    //         $existingUser = User::where('email', $email)->first();

    //         if ($existingUser) {
    //             // User exists - login and redirect based on registration status
    //             Auth::login($existingUser);

    //             // Update online status
    //             $existingUser->update([
    //                 'is_online' => true,
    //                 'online_at' => now()
    //             ]);

    //             // Broadcast user online status
    //             broadcast(new UserOnline($existingUser->id));

    //             // Check registration completion status
    //             if ($existingUser->registration_completed == 0 && !$existingUser->hasAnyRole(['admin', 'super admin'])) {
    //                 // Incomplete registration - redirect to stepper based on original user_type selection
    //                 return redirect()->route('register.user_type', $user_type);
    //             } else {
    //                 // Complete registration - redirect to dashboard
    //                 if ($existingUser->hasRole('customer')) {
    //                     return redirect()->route('customer.dashboard');
    //                 } elseif ($existingUser->hasAnyRole(['business', 'individual', 'professional'])) {
    //                     return redirect()->route('professional.dashboard');
    //                 } else {
    //                     return redirect('/home');
    //                 }
    //             }
    //         } else {
    //             // New user - create account immediately
    //             $password = Str::random(8);
    //             $user = new User();
    //             $user->ids = Str::uuid();
    //             $user->name = $name;
    //             $user->email = $email;
    //             $user->password = Hash::make($password);
    //             $user->email_verified_at = now();
    //             $user->registration_completed = 0; // Incomplete registration
    //             $user->save();

    //             // Assign role based on user type
    //             $role = Role::where("name", $user_type)->first();
    //             $user->assignRole($role);

    //             // Login the user
    //             Auth::login($user);

    //             // Send password email
    //             Mail::to($user->email)->send(new \App\Mail\UserPasswordMail($password));

    //             // Redirect to stepper based on user type
    //             return redirect()->route('register.user_type', $user_type);
    //         }

    //     } catch (\Exception $e) {
    //         return redirect('/register')->withErrors(['error' => 'Google login failed: ' . $e->getMessage()]);
    //     }
    // }
    public function handleGoogleCallback()
    {
        try {
            // Get user information from Google
            $googleUser = Socialite::driver('google')->user();
            $email = $googleUser->getEmail();
            $name = $googleUser->getName();
            $user_type = session('user_type', 'customer'); // Default to 'customer'
            // Check if user exists
            $existingUser = User::where('email', $email)->first();
            if ($existingUser) {
                // Check if the existing user's role matches the selected user type
                $userRole = $existingUser->getRoleNames()->first();
                if ($user_type == "professional" && !$existingUser->hasRole("professional")) {
                    $message = "This email is registered as a {$userRole}. Please select the correct account type or use a different email.";
                    // Ensure the message is a string
                    if (is_array($message)) {
                        $message = implode(' ', $message); // Join array elements if it's an array
                    }
                    // Apply ucwords() and pass formatted message
                    $message = ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', $message));
                    return redirect()->route('home')->with('social_validation', [
                        'type' => 'warning',
                        'title' => 'Oops...',
                        'message' => $message
                    ]);
                } elseif ($user_type == "customer" && !$existingUser->hasRole("customer")) {
                    $message = "This email is registered as a {$userRole}. Please select the correct account type or use a different email.";
                    // Ensure the message is a string
                    if (is_array($message)) {
                        $message = implode(' ', $message); // Join array elements if it's an array
                    }
                    // Apply ucwords() and pass formatted message
                    $message = ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', $message));
                    return redirect()->route('home')->with('social_validation', [
                        'type' => 'warning',
                        'title' => 'Oops...',
                        'message' => $message
                    ]);
                }
                // If the user role is valid, log them in and redirect to the appropriate dashboard
                Auth::login($existingUser);
                // Update user status (optional)
                $existingUser->update([
                    'is_online' => true,
                    'online_at' => now()
                ]);
                // Broadcast user online status
                broadcast(new UserOnline($existingUser->id));
                if ($existingUser->registration_completed == 0 && !$existingUser->hasAnyRole(['admin', 'super admin'])) {
                    // Incomplete registration - redirect to stepper
                    return redirect()->route('register.user_type', $user_type);
                }
                // Redirect based on user role
                if ($existingUser->hasRole('customer')) {
                    return redirect()->route('customer.dashboard');
                } elseif ($existingUser->hasAnyRole(['business', 'individual', 'professional'])) {
                    return redirect()->route('professional.dashboard');
                } else {
                    return redirect('/home');
                }
            }
            // New user registration flow
            $password = Str::random(8);
            $user = new User();
            $user->ids = Str::uuid();
            $user->name = $name;
            $user->email = $email;
            $user->password = Hash::make($password);
            $user->email_verified_at = now();
            $user->registration_completed = 0; // Incomplete registration
            $user->save();
            // Assign the role based on the user type
            $role = Role::where("name", $user_type)->first();
            $user->assignRole($role);
            // Log in the new user
            Auth::login($user);
            // Send password email
            Mail::to($user->email)->send(new \App\Mail\UserPasswordMail($password));
            // Redirect to registration stepper
            return redirect()->route('register.user_type', $user_type);
        } catch (\Exception $e) {
            return redirect('/register')->withErrors(['error' => 'Google login failed: ' . $e->getMessage()]);
        }
    }
    public function redirectToApple($user_type = null)
    {
        session(['user_type' => $user_type]);
        return Socialite::driver('apple')->redirect();
    }

    public function handleAppleCallback()
    {
        try {
            // Get user information from Apple
            $appleUser = Socialite::driver('apple')->user();
            $email = $appleUser->getEmail();
            $user_type = session('user_type', 'customer'); // Default to 'customer'

            // Check if user exists
            $existingUser = User::where('email', $email)->first();

            if ($existingUser) {
                // Check if the existing user's role matches the selected user type
                $userRole = $existingUser->getRoleNames()->first();

                if ($user_type == "professional" && !$existingUser->hasRole("professional")) {
                    $message = "This email is registered as a {$userRole}. Please select the correct account type or use a different email.";
                    // Ensure the message is a string
                    if (is_array($message)) {
                        $message = implode(' ', $message); // Join array elements if it's an array
                    }
                    // Apply ucwords() and pass formatted message
                    $message = ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', $message));
                    return redirect()->route('home')->with('social_validation', [
                        'type' => 'warning',
                        'title' => 'Oops...',
                        'message' => $message
                    ]);
                } elseif ($user_type == "customer" && !$existingUser->hasRole("customer")) {
                    $message = "This email is registered as a {$userRole}. Please select the correct account type or use a different email.";
                    // Ensure the message is a string
                    if (is_array($message)) {
                        $message = implode(' ', $message); // Join array elements if it's an array
                    }
                    // Apply ucwords() and pass formatted message
                    $message = ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', $message));
                    return redirect()->route('home')->with('social_validation', [
                        'type' => 'warning',
                        'title' => 'Oops...',
                        'message' => $message
                    ]);
                }

                // If the user role is valid, log them in and redirect to the appropriate dashboard
                Auth::login($existingUser);
                // Update user status (optional)
                $existingUser->update([
                    'is_online' => true,
                    'online_at' => now()
                ]);
                // Broadcast user online status
                broadcast(new UserOnline($existingUser->id));

                if ($existingUser->registration_completed == 0 && !$existingUser->hasAnyRole(['admin', 'super admin'])) {
                    // Incomplete registration - redirect to stepper
                    return redirect()->route('register.user_type', $user_type);
                }

                // Redirect based on user role
                if ($existingUser->hasRole('customer')) {
                    return redirect()->route('customer.dashboard');
                } elseif ($existingUser->hasAnyRole(['business', 'individual', 'professional'])) {
                    return redirect()->route('professional.dashboard');
                } else {
                    return redirect('/home');
                }
            }

            // New user registration flow
            $password = Str::random(8);
            $user = new User();
            $user->ids = Str::uuid();
            $user->name = $appleUser->getName();
            $user->email = $email;
            $user->password = Hash::make($password);
            $user->email_verified_at = now();
            $user->registration_completed = 0; // Incomplete registration
            $user->save();

            // Assign the role based on the user type
            $role = Role::where("name", $user_type)->first();
            $user->assignRole($role);

            // Log in the new user
            Auth::login($user);

            // Send password email
            Mail::to($user->email)->send(new \App\Mail\UserPasswordMail($password));

            // Redirect to registration stepper
            return redirect()->route('register.user_type', $user_type);
        } catch (\Exception $e) {
            return redirect('/register')->withErrors(['error' => 'Apple login failed: ' . $e->getMessage()]);
        }
    }

    // public function handleAppleCallback()
    // {
    //     try {
    //         $appleUser = Socialite::driver('apple')->user();
    //         $user_type = session('user_type', 'customer');
    //         $email = $appleUser->getEmail();
    //         session(['social_email' => $email, 'social_user_type' => $user_type]);
    //         return redirect()->route('register')
    //             ->with('skip_to_step', '2')
    //             ->with('social_email', $email)
    //             ->with('social_user_type', $user_type);
    //     } catch (\Exception $e) {
    //         return redirect('/register')->withErrors(['error' => 'Apple login failed: ' . $e->getMessage()]);
    //     }
    // }
}
