<div id="kt_app_sidebar nnnnn" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar"
    data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px"
    data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
    <div class="app-sidebar-logo px-6" id="kt_app_sidebar_logo">

        <a class="d-flex align-items-center gap-3" href="{{ url('/') }}">

            <img src="{{ asset('website') }}/assets/images/footer_primary.svg"
                class="h-300px w-225px object-fit-contain rounded-pill top-rated-image" alt="card-image">

            <!-- <img src="{{ asset('website') . '/' . setting()->logo }}" alt="Logo" class="img-fluid"
                style="height: 50px;"> <span class="deep-blue fs-32 bold">Keldaa</span> -->


            <!-- <img alt="Logo" src="{{ asset('') }}{{ App\Models\Setting::first()->favicon ?? '' }}"
                class="h-50px app-sidebar-logo-minimize" /> -->
        </a>

        <!-- begin::Minimized sidebar setup:
     if (isset($_COOKIE["sidebar_minimize_state"]) && $_COOKIE["sidebar_minimize_state"] === "on") {
     1. "src/js/layout/sidebar.js" adds "sidebar_minimize_state" cookie value to save the sidebar minimize state.
     2. Set data-kt-app-sidebar-minimize="on" attribute for body tag.
     3. Set data-kt-toggle-state="active" attribute to the toggle element with "kt_app_sidebar_toggle" id.
     4. Add "active" class to to sidebar toggle element with "kt_app_sidebar_toggle" id.
     }
        <div id="kt_app_sidebar_toggle"
            class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary h-30px w-30px position-absolute top-50 start-100 translate-middle rotate"
            data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body"
            data-kt-toggle-name="app-sidebar-minimize">
            <i class="ki-duotone ki-black-left-line fs-3 rotate-180">
                <span class="path1"></span>
                <span class="path2"></span>
            </i>
        </div>  -->
    </div>
    <div class="app-sidebar-menu overflow-hidden flex-column-fluid admin-sidebar ">
        <div id="kt_app_sidebar_menu_wrapper" class="app-sidebar-wrapper ">
            <div id="kt_app_sidebar_menu_scroll" class="scroll-y my-5 mx-3" data-kt-scroll="true"
                data-kt-scroll-activate="true" data-kt-scroll-height="auto"
                data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px"
                data-kt-scroll-save-state="true">
                <div class="menu menu-column menu-rounded menu-sub-indention fw-semibold fs-6" id="#kt_app_sidebar_menu"
                    data-kt-menu="true" data-kt-menu-expand="false">
                    @if (Auth::user()->hasRole('developer'))
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Developer</span>
                            </div>
                        </div>
                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                            <span class="menu-link">
                                <span class="menu-icon">
                                    <i class="ki-duotone ki-abstract-28 fs-2">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                                <span class="menu-title">User Management</span>
                                <span class="menu-arrow"></span>
                            </span>
                            <div class="menu-sub menu-sub-accordion">
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    @can('crud-list')
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">CRUD</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    @endcan
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('crud_generator') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">CRUD Generator</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion mb-1">
                                    @can('user-list')
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Users</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    @endcan
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('users') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Users List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
                                    @can('role-list')
                                        <span class="menu-link">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Roles</span>
                                            <span class="menu-arrow"></span>
                                        </span>
                                    @endcan
                                    <div class="menu-sub menu-sub-accordion">
                                        <div class="menu-item">
                                            <a class="menu-link" href="{{ url('roles') }}">
                                                <span class="menu-bullet">
                                                    <span class="bullet bullet-dot"></span>
                                                </span>
                                                <span class="menu-title">Roles List</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="menu-item">
                                    <a class="menu-link" href="javascript:void(0);">
                                        <span class="menu-bullet">
                                            <span class="bullet bullet-dot"></span>
                                        </span>
                                        <span class="menu-title">Permissions</span>
                                    </a>
                                </div>
                                @can('settings-list')
                                    <div class="menu-item">
                                        <a class="menu-link" href="{{ url('settings') }}">
                                            <span class="menu-bullet">
                                                <span class="bullet bullet-dot"></span>
                                            </span>
                                            <span class="menu-title">Settings</span>
                                        </a>
                                    </div>
                                @endcan
                            </div>
                        </div>
                        <hr>
                        <div class="menu-item pt-5">
                            <div class="menu-content">
                                <span class="menu-heading fw-bold text-uppercase fs-7">Menu</span>
                            </div>
                        </div>
                        @foreach ($crud as $item)
                            @can($item->url . '-list')
                                {{-- @can(\Illuminate\Support\Str::slug($item->name) . '-list') --}}
                                <div class="menu-item">
                                    <a class="menu-link {{ request()->is($item->url) ? 'active' : '' }}"
                                        href="{{ url($item->url ?? 'home') }}">
                                        <span class="menu-icon">
                                            <i class="ki-duotone ki-abstract-28 fs-2">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </span>
                                        <span
                                            class="menu-title">{{ preg_replace('/(?<=[a-z])[A-Z]|[A-Z](?=[a-z])/', ' $0', $item->name) }}</span>
                                    </a>
                                </div>
                            @endcan
                        @endforeach
                    @endif
                    @if (Auth::user()->hasAnyRole(['admin', 'super admin']))
                        <div class="menu-item">
                            <a class="menu-link {{ request()->is('dashboard') ? 'active' : '' }}"
                                href="{{ route('dashboard') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-qrcode"></i></span>
                                <span class="menu-title">Dashboard</span>
                            </a>
                        </div>

                        @can('user-list')
                            <div class="menu-item">
                                <a class="menu-link {{ Route::is('admins') ? 'active' : '' }}" href="{{ route('admins') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-qrcode"></i></span>
                                    <span class="menu-title">Create Admins</span>
                                </a>
                            </div>
                        @endcan


                        <div class="menu-item">
                            <a class="menu-link {{ Route::is('admin_analytics') ? 'active' : '' }}"
                                href="{{ route('admin_analytics') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-qrcode"></i></span>
                                <span class="menu-title">Analytics</span>
                            </a>
                        </div>


                        @can('categories-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*categories*') ? 'active' : '' }}"
                                    href="{{ route('categories.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-regular fa-file"></i></span>
                                    <span class="menu-title">Categories</span>
                                </a>
                            </div>
                        @endcan

                        <div class="menu-item">
                            <a class="menu-link {{ request()->is('*professionals*') ? 'active' : '' }}"
                                href="{{ route('professionals') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-users"></i></span>
                                <span class="menu-title">Professionals</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link {{ request()->is('*customers*') ? 'active' : '' }}"
                                href="{{ route('customers') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-money-bill-1"></i></span>
                                <span class="menu-title">Customers</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link {{ request()->is('services') || (request()->is('services/*') && !request()->is('services/durations*')) ? 'active' : '' }}"
                                href="{{ route('services.index') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-money-bill-1"></i></span>
                                <span class="menu-title">Services</span>
                            </a>
                        </div>

                        {{-- <div class="menu-item">
                            <a class="menu-link {{ request()->is('services/durations*') ? 'active' : '' }}"
                                href="{{ route('services.durations.index') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-money-bill-1"></i></span>
                                <span class="menu-title">Service Duration</span>
                            </a>
                        </div> --}}

                        <div class="menu-item">
                            <a class="menu-link {{ request()->is('*booking*') ? 'active' : '' }}"
                                href="{{ route('booking') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-laptop"></i></span>
                                <span class="menu-title">Bookings</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link {{ request()->is('*refund_request*') ? 'active' : '' }}"
                                href="{{ route('refund_request') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-file-invoice"></i></span>
                                <span class="menu-title">Refund Requests</span>
                            </a>
                        </div>

                        <div class="menu-item">
                            <a class="menu-link {{ request()->routeIs('wallet') ? 'active' : '' }}"
                                href="{{ route('wallet') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                <span class="menu-title">Transactions</span>
                            </a>
                        </div>

                        @can('vatmanagements-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*vatmanagements*') ? 'active' : '' }}"
                                    href="{{ route('vatmanagements.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">VAT Management</span>
                                </a>
                            </div>
                        @endcan

                        @can('discountcoupons-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*discount-coupons*') ? 'active' : '' }}"
                                    href="{{ route('discount-coupons.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Discount & Coupon</span>
                                </a>
                            </div>
                        @endcan

                        @can('subscriptions-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*subscriptions*') ? 'active' : '' }}"
                                    href="{{ route('subscriptions.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Subscription Management</span>
                                </a>
                            </div>
                        @endcan

                        @can('holidays-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*holidays*') ? 'active' : '' }}"
                                    href="{{ route('holidays.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Holidays</span>
                                </a>
                            </div>
                        @endcan

                        @can('certifications-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*certifications*') ? 'active' : '' }}"
                                    href="{{ route('certifications.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Certifications</span>
                                </a>
                            </div>
                        @endcan

                        @can('cms-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*cms*') ? 'active' : '' }}"
                                    href="{{ route('cms.home') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">CMS</span>
                                </a>
                            </div>
                        @endcan

                        @can('social-platforms-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*social-platforms*') ? 'active' : '' }}"
                                    href="{{ route('social-platforms.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Social Platforms</span>
                                </a>
                            </div>
                        @endcan

                        {{-- <div class="menu-item">
                            <a class="menu-link {{ request()->is('*navigation-links*') ? 'active' : '' }}"
                                href="{{ route('navigation-links.index') }}">
                                <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                <span class="menu-title">Navigation Links</span>
                            </a>
                        </div> --}}

                        @can('settings-list')
                            <div class="menu-item">
                                <a class="menu-link {{ request()->is('*settings*') ? 'active' : '' }}"
                                    href="{{ route('settings.index') }}">
                                    <span class="cust-menu-icon"><i class="fa-solid fa-file-signature"></i></span>
                                    <span class="menu-title">Settings</span>
                                </a>
                            </div>
                        @endcan
                    @endif
                </div>
            </div>
        </div>
    </div>
    @if (Auth::user()->hasRole('developer'))
        <div class="app-sidebar-footer flex-column-auto pt-2 pb-6 px-6" id="kt_app_sidebar_footer">
            <a href="{{ url('html/demo1/dist') }}"
                class="btn btn-flex flex-center btn-custom btn-primary overflow-hidden text-nowrap px-0 h-40px w-100"
                data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-dismiss-="click"
                title="200+ in-house components and 3rd-party plugins">
                <span class="btn-label">Docs & Components</span>
                <i class="ki-duotone ki-document btn-icon fs-2 m-0">
                    <span class="path1"></span>
                    <span class="path2"></span>
                </i>
            </a>
        </div>
    @endif
</div>