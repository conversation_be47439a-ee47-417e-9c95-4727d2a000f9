<?php

return [
    // Input allowing letters (including extended Latin), numbers, spaces, and common punctuation
    'input_regex' => '/^[\p{L}\p{N}\s\p{P}]+$/u',
    'input_text_length' => 50,
    
    // Field-specific lengths to match database schema
    'name_length' => 100,        // varchar(100) fields
    'alt_tag_length' => 100,     // varchar(100) fields  
    'image_description_length' => 255,  // varchar(255) fields
    'slug_length' => 255,        // varchar(255) fields

    // Password validation
    'password_min' => 8,
    'password_max' => 16,
    'password_regex' => '/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,16}$/',

    // Email validation
    'email_regex' => '/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/',

    // Phone validation - more flexible for international numbers
    'phone_length' => 20,
    'phone_regex' => '/^[\+]?[0-9\s\-\(\)]+$/',

    // Image validation
    'image_size' => 5120, // in KB (5MB)
    'image_types' => ['jpeg', 'png', 'jpg', 'gif', 'svg', 'webp'],
    'image_mimes' => 'jpeg,png,jpg,gif,svg,webp',

    // URL validation - simplified, Laravel's url rule is better
    'url_regex' => '/^https?:\/\/[^\s\/$.?#].[^\s]*$/i',

    // Description allowing more characters including punctuation
    'description_regex' => '/^[\p{L}\p{N}\p{P}\p{S}\p{Zs}\n\r]+$/u',

    //messages
    'messages' => [
        'input' => [
            'regex' => 'Contains invalid characters. Only letters, numbers, spaces, and punctuation are allowed.',
            'max' => 'Text must not exceed 50 characters.',
        ],
        'name' => [
            'max' => 'Name must not exceed 100 characters.',
        ],
        'alt_tag' => [
            'max' => 'Alt tag must not exceed 100 characters.',
        ],
        'image_description' => [
            'max' => 'Image description must not exceed 255 characters.',
        ],
        'slug' => [
            'max' => 'Slug must not exceed 255 characters.',
        ],
        'password' => [
            'required' => 'Password is required.',
            'min' => 'Password must be at least 8 characters.',
            'max' => 'Password must not exceed 16 characters.',
            'regex' => 'Password must include at least 1 uppercase, 1 lowercase, 1 number, and 1 special character.',
        ],
        'email' => [
            'required' => 'Email address is required.',
            'regex' => 'Enter a valid email address.',
        ],
        'phone' => [
            'regex' => 'Enter a valid international phone number.',
            'max' => 'Phone number must not exceed 20 characters.',
        ],
        'image' => [
            'mimes' => 'Only JPEG, PNG, JPG, GIF, SVG, or WEBP images are allowed.',
            'max' => 'Image size must not exceed 5MB.',
        ],
        'url' => [
            'regex' => 'Please enter a valid URL.',
        ],
        'description' => [
            'regex' => 'Description contains invalid characters.',
        ],
    ],
];
