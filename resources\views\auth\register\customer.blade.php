@extends('layouts.app')
@push('css')
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@1.5.2/dist/select2-bootstrap4.min.css"
        rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/css/intlTelInput.css" />
    <style>
        .error-input {
            border-color: #dc3545 !important;
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }

        .image-input.error-input {
            border-color: #dc3545 !important;
        }

        .image-input.error-input .image-input-wrapper {
            border-color: #dc3545 !important;
        }

        /* Map and Location Styles */
        #pac-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 10px;
        }

        #pac-input:focus {
            outline: none;
            border-color: #020C87;
            box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
        }

        #map {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }

        #map-placeholder {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }

        /* Phone Input Styles */
        .iti {
            width: 100%;
        }

        .iti__country-list {
            z-index: 9999;
        }

        .iti__selected-flag {
            padding: 12px 16px;
        }

        .iti input[type="tel"] {
            padding: 12px 16px;
            padding-left: 60px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            width: 100%;
        }

        .iti input[type="tel"]:focus {
            outline: none;
            border-color: #020C87;
            box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
        }

        .iti input[type="tel"].error-input {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }
    </style>
@endpush
@section('content')
    <div class="container professional-acc-form customer-registration">
        <div class="row justify-content-center">
            <div class="col-md-12 stepper-navigation">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <!-- Home Logo -->
                    <a href="{{ url('/') }}" class="home-logo">
                        <img src="{{ asset('website') . '/' . setting()->logo }}" alt="Home" class="img-fluid"
                            style="height: 60px;">
                    </a>

                    <!-- Continue Button with Dropdown -->

                    <div class="d-flex flex-column">
                        <a class="blue-border-btn mb-3" href="#" id="logoutBtn">Logout</a>
                        <button type="button" class="btn action-button next" id="continueBtn">Continue</button>
                    </div>
                </div>
            </div>

            <div class="col-md-8 mb-2 pt-20 mt-20">
                <div class=" px-0 pt-4 pb-0 mt-3 mb-3">
                    <form id="acc-form" action="{{ route('register.customer') }}" method="post"
                        enctype="multipart/form-data">
                        @csrf
                        <div class="form-card frst-step">
                            <div class="container">
                                <div class="row">
                                    <div class="col-md-12 mb-10">
                                        <h2>Create a Customer Account</h2>
                                        <p>You're almost there! Create your new account for {{ auth()->user()->email }} by
                                            completing these details.</p>
                                    </div>

                                    <div class="col-md-12 mb-10">
                                        <div class="Image-input_holder pro-account-image-holder mb-10">
                                            <div class="image-input image-input-empty" data-kt-image-input="true">
                                                <div class="image-input " data-kt-image-input="true">
                                                    <div class="image-input-wrapper"></div>
                                                    <label class="dark-green-btn fs-14 regular pt-9"
                                                        data-kt-image-input-action="change">
                                                        <span class="pe-3 fs-16 fw-600 mb-10 blue-text"> Upload Profile
                                                            Image<span style="color: red;">*</span></span>
                                                        <input type="file" name="avatar" id="avatar"
                                                            accept=".png, .jpg, .jpeg" id="profileImage" />
                                                        <input type="hidden" name="profile_image" />
                                                        <p class="fs-24 medium pt-4 gray-text"> At least 125x125 px
                                                            recommended. JPG or PNG is allowed</p>
                                                    </label>

                                                    <a href="#!" class="light-green-btn fs-14 regular ms-5"
                                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                        data-bs-dismiss="click" title="Cancel avatar"> <i
                                                            class="fas fa-times fa-5"></i> </a>

                                                    <a href="#!" class=" ms-5" data-kt-image-input-action="remove"><i
                                                            class="fas fa-trash-alt"></i> </a>
                                                </div>
                                            </div>
                                            <p class="image-error-msg mt-5" style="color: red; display: none;">Image
                                                required</p>
                                            @error('avatar')
                                                <span class="invalid-feedback" role="alert">
                                                    <strong>{{ $message }}</strong>
                                                </span>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-12 mb-6">
                                        <label for="name" class="fieldlabels">Full Name <span
                                                style="color: red;">*</span></label>
                                        <input type="text" name="name" id="fullname" value="{{ old('name', auth()->user()->name) }}"
                                            placeholder="Enter your Name" id="fullname" />
                                        @error('name')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12 field-spacing">
                                        <label for="email" class="fieldlabels">Email Address </label>
                                        <input type="email" value="{{ auth()->user()->email }}" id="email" readonly />
                                        @error('email')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="col-md-12 field-spacing">
                                        <label for="phone" class="fieldlabels">Phone Number <span
                                                style="color: red;">*</span> </label>
                                        <input id="phone" type="tel" placeholder="Phone Number" name="phone"
                                            value="{{ old('phone') }}" />
                                        <input type="hidden" name="country_code" id="country_code" value="{{ old('country_code') }}" />
                                        @error('phone')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                        @error('country_code')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>

                                    <div class="mb-3 service_preferances">
                                        <label for="categories" class="form-label form-input-labels">Service
                                            Preferences<span class="red">*</span></label>
                                        <select class="form-select form-select-field" id="categories" name="categories[]"
                                            data-control="select2" multiple >   
                                            <option></option>
                                            @foreach ($categories as $category)
                                                <option value="{{ $category->id }}">
                                                    {{ $category->name }}
                                                </option>
                                            @endforeach
                                        </select>

                                        @error('categories')
                                            <div class="text-danger mt-1">
                                                <small><strong>{{ $message }}</strong></small>
                                            </div>
                                        @enderror

                                        <label id="categories-error" class="error" for="categories"> </label>
                                    </div>

                                    <div class="col-md-12 customer-loc-map mb-6 mt-4">
                                        <label for="location" class="fieldlabels w-100">Location <span
                                                style="color: red;">*</span></label>
                                        <input id="pac-input" type="text" name="location"
                                            value="{{ old('location', auth()->user()->profile?->location ?? '') }}"
                                            placeholder="Please enter your location" />
                                        <input type="hidden" name="lat"
                                            value="{{ old('lat', auth()->user()->profile?->lat ?? '') }}" id="latitude">
                                        <input type="hidden" name="lng"
                                            value="{{ old('lng', auth()->user()->profile?->lng ?? '') }}" id="longitude">

                                        <!-- Map Container -->
                                        <div id="map"
                                            style="height: 400px; width: 100%; border-radius: 8px; margin-top: 10px;">
                                        </div>
                                        @error('location')
                                            <span class="invalid-feedback" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <!-- Load jQuery only once -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.validate/1.7/additional-methods.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>

    <script>
        $('#toggle-password').on('click', function () {
            var passwordField = $('#password');
            var passwordFieldType = passwordField.attr('type');
            if (passwordFieldType === 'password') {
                passwordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                passwordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
        $('#toggle-password-confirm').on('click', function () {
            var confirmPasswordField = $('#confirm_password');
            var confirmPasswordFieldType = confirmPasswordField.attr('type');
            if (confirmPasswordFieldType === 'password') {
                confirmPasswordField.attr('type', 'text');
                $(this).find('.fa-eye-slash').removeClass('d-none');
                $(this).find('.fa-eye').addClass('d-none');
            } else {
                confirmPasswordField.attr('type', 'password');
                $(this).find('.fa-eye').removeClass('d-none');
                $(this).find('.fa-eye-slash').addClass('d-none');
            }
        });
    </script>

    <!-- Phone number -->
    <script>
        const input = document.querySelector("#phone");
 
        const iti = window.intlTelInput(input, {
            initialCountry: "gb",
            separateDialCode: true,
            preferredCountries: ["gb", "us", "in", "pk"],
            utilsScript:
                "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        });
 
        // jQuery Validate hook (optional)
        if (window.jQuery && $.validator) {
            $.validator.addMethod(
                "phoneValidation",
                function (value, element) {
                    if (this.optional(element)) return true;
                    return iti.isValidNumber();
                },
                "Please enter a valid phone number"
            );
        }
 
        // Keep last valid (not-too-long) value to revert on overflow/paste.
        let lastOk = "";
 
        function digitsOnly(s) {
            return (s || "").replace(/\D/g, "");
        }
 
        // Compute a conservative max national length using an E.164 example.
        function setMaxLengthFromExample() {
            const u = window.intlTelInputUtils;
            if (!u) return; // utils not ready yet
            const { iso2, dialCode } = iti.getSelectedCountryData();
 
            // Example in E.164, e.g. "+923001234567"
            const exampleE164 = u.getExampleNumber(
                iso2,
                false,
                u.numberFormat.E164
            );
            const e164Digits = digitsOnly(exampleE164); // e.g. "923001234567"
            const nationalMax =
                e164Digits && dialCode ? e164Digits.length - String(dialCode).length : 15;
 
            // Backstop: set input's maxLength to this computed cap
            input.maxLength = Math.max(1, nationalMax);
        }
 
        function clampIfTooLong() {
            const raw = input.value;
            const digits = digitsOnly(raw);
 
            // Always keep only digits in the visible input
            input.value = digits;
 
            const u = window.intlTelInputUtils;
            if (!u) {
                // utils.js not ready; remember current sanitized value
                lastOk = input.value;
                return;
            }
 
            const { dialCode } = iti.getSelectedCountryData();
 
            // Build an E.164-like candidate and ask libphonenumber if it's TOO_LONG
            const e164Candidate = (dialCode ? "+" + dialCode : "") + digits;
            const reason = u.isPossibleNumberWithReason(e164Candidate);
            const TOO_LONG = u.validationError.TOO_LONG;
 
            if (reason === TOO_LONG) {
                // Revert to the last value that wasn't too long
                input.value = lastOk;
                return;
            }
 
            // Accept and remember this value
            lastOk = input.value;
        }
 
        // Enforce on every change (typing, paste, drag-drop, autofill)
        input.addEventListener("input", clampIfTooLong);
 
        // When the country changes, reset and recompute caps
        input.addEventListener("countrychange", () => {
            input.value = "";
            lastOk = "";
            setMaxLengthFromExample();
 
            const countryData = iti.getSelectedCountryData();
            const hidden = document.getElementById("country_code");
            if (hidden) hidden.value = countryData.dialCode;
        });
 
        // Initial cap (after utils loads it will be refined on the next input/countrychange)
        setMaxLengthFromExample();
 
        // In case utils loads a bit later, try to set max once more after a tick
        window.addEventListener("load", () => {
            setTimeout(setMaxLengthFromExample, 200);
        });
    </script>

    <script>
        const fileInput = document.querySelector('input[type="file"]');
        const wrapper = document.querySelector('.image-input-wrapper');
        const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
        const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
        const imageInput = document.querySelector('.image-input');

        fileInput.addEventListener('change', function (e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();

                reader.onload = function (event) {
                    wrapper.style.backgroundImage = `url('${event.target.result}')`;
                    imageInput.classList.remove('image-input-empty');
                };

                reader.readAsDataURL(file);
            }
        });

        // Remove action
        removeBtn.addEventListener('click', function () {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
            // Set hidden input for backend if needed
            document.querySelector('input[name="avatar_remove"]').value = '1';
        });

        // Optional: Cancel action (if you need to reset back to default image, add that logic)
        cancelBtn.addEventListener('click', function () {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
        });
    </script>

    <script>
        $(document).ready(function () {
            // Initialize Select2 for services with Bootstrap 4 theme
            $('#categories').select2({
                theme: 'bootstrap4',
                placeholder: "Select categories",
                allowClear: false,
                width: '100%'
            });

            // Trigger validation when Select2 changes
            $('#categories').on('change', function () {
                // Clear existing error for this field
                $(this).next('.error').remove();
                $(this).valid();
            });

            // Clear errors when form fields change
            $('#acc-form input, #acc-form select, #acc-form textarea').on('input change', function () {
                // Remove error styling and messages for this specific field
                $(this).removeClass('error-input');
                $(this).next('.error').remove();
                $(this).siblings('.invalid-feedback').remove();
            });

            $.validator.addMethod('maxFileSize', function (value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true; // no file selected, let 'required' rule handle this
                }
                const fileSizeKB = element.files[0].size / 1024; // size in KB
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            // Clear any existing validation instance
            if ($("#acc-form").data('validator')) {
                $("#acc-form").removeData('validator');
            }
            $("#acc-form").validate({
                ignore: ':hidden:not(select)', // Don't ignore hidden selects (for Select2)
                errorPlacement: function (error, element) {
                    // Remove any existing error for this element first
                    const existingError = element.next('.error');
                    if (existingError.length) {
                        existingError.remove();
                    }

                    // Special handling for select2 elements
                    if (element.hasClass('select2-hidden-accessible')) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    name: {
                        required: true,
                        maxlength: 30,
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone: {
                        required: true,
                        phoneValidation: true
                    },
                    "categories[]": {
                        required: true,
                        minlength: 1
                    },
                    avatar: {
                        required: true,
                        maxFileSize: 5120
                    },
                    location: {
                        required: true
                    },
                    lat: {
                        required: true,
                        numeric: true
                    },
                    lng: {
                        required: true,
                        numeric: true
                    }
                },
                messages: {
                    name: {
                        required: "Please enter your Name",
                        maxlength: "Your Name must not exceed 30 characters",
                    },
                    email: {
                        required: "Please enter your email address",
                        email: "Please enter a valid email address"
                    },
                    phone: {
                        required: "Please enter your phone number",
                        phoneValidation: "Please enter a valid phone number"
                    },
                    "categories[]": {
                        required: "Please select at least one category",
                        minlength: "Please select at least one category"
                    },
                    avatar: {
                        required: "Please upload your profile image",
                        maxFileSize: "Image size must not exceed 5 MB"
                    },
                    location: {
                        required: "Please select your location"
                    },
                    lat: {
                        required: "Please select your location",
                        numeric: "Please select your location"
                    },
                    lng: {
                        required: "Please select your location",
                        numeric: "Please select your location"
                    }
                },
                submitHandler: function (form) {
                    // Get the country code and set it in hidden field
                    const countryData = iti.getSelectedCountryData();
                    $('#country_code').val(countryData.dialCode);

                    // Optional: Log for debugging (remove in production)
                    console.log('Submitting phone:', $('#phone').val(), 'Country code:', countryData.dialCode);

                    form.submit();
                }
            });


            // Debounce mechanism for continue button
            let continueTimeout;
            $(document).on('click', '#continueBtn', function (e) {
                e.preventDefault();

                // Clear any existing timeout
                clearTimeout(continueTimeout);

                // Debounce the submit to prevent multiple rapid clicks
                continueTimeout = setTimeout(function () {
                    // Clear any existing custom error messages before validation
                    clearAllCustomErrors();

                    // Get the country code and set it in hidden field
                    const countryData = iti.getSelectedCountryData();
                    $('#country_code').val(countryData.dialCode);

                    // Optional: Log for debugging (remove in production)
                    console.log('Submitting phone:', $('#phone').val(), 'Country code:', countryData.dialCode);

                    $("#acc-form").submit();
                }, 300);
            });

            // Function to clear all custom error messages
            function clearAllCustomErrors() {
                // Clear phone errors
                if (typeof clearPhoneErrors === 'function') {
                    clearPhoneErrors();
                }

                // Clear any other custom error messages
                $('.invalid-feedback:not(.d-none)').remove();
                $('.error-input').removeClass('error-input');

                // Clear image error messages
                $('.image-error-msg').hide();
            }

            // Handle logout with confirmation
            $(document).on('click', '#logoutBtn', function (e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Registration Not Complete',
                    text: 'Your registration is not complete yet. You can continue from here anytime. Are you sure you want to logout?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, Logout',
                    cancelButtonText: 'Continue Registration'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = "{{ url('logout') }}";
                    }
                });
            });
        });
    </script>

    <!-- Google Maps Script -->
    <script
        src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.api_key') }}&libraries=places&v=weekly"
        async defer></script>
    <script>
        // Initialize Google Maps for Customer Registration
        function initCustomerMap() {
            console.log('Initializing Customer Registration Map...');

            const mapElement = document.getElementById("map");
            const inputElement = document.getElementById("pac-input");
            const latElement = document.getElementById("latitude");
            const lngElement = document.getElementById("longitude");

            if (!mapElement || !inputElement || !latElement || !lngElement) {
                console.warn('Required map elements not found');
                return;
            }

            // Check if Google Maps API is loaded
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                console.warn('Google Maps API not loaded yet, retrying...');
                setTimeout(initCustomerMap, 500);
                return;
            }

            console.log('Google Maps API loaded successfully');

            // Get existing coordinates
            const existingLat = '{{ old('lat', auth()->user()->profile?->lat ?? '') }}';
            const existingLng = '{{ old('lng', auth()->user()->profile?->lng ?? '') }}';
            const hasExistingCoords = existingLat && existingLng && existingLat !== '' && existingLng !== '';

            console.log('Map Debug:', {
                hasExistingCoords: hasExistingCoords,
                lat: existingLat,
                lng: existingLng
            });

            const defaultLatLng = {
                lat: hasExistingCoords ? parseFloat(existingLat) : 40.7128,
                lng: hasExistingCoords ? parseFloat(existingLng) : -74.0060
            };

            const searchBox = new google.maps.places.SearchBox(inputElement);
            const geocoder = new google.maps.Geocoder();
            let map, marker;

            // Show placeholder if no coordinates
            if (!hasExistingCoords) {
                console.log('No coordinates - showing placeholder');
                inputElement.placeholder = "Please enter your location";
                mapElement.style.display = 'none';

                // Create placeholder
                const placeholder = document.createElement('div');
                placeholder.id = 'map-placeholder';
                placeholder.style.cssText =
                    'padding: 60px 40px; text-align: center; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; color: #9ca3af; font-size: 16px; height: 400px; display: flex; flex-direction: column; justify-content: center; align-items: center;';
                placeholder.innerHTML =
                    '<div style="color: #020C87; font-size: 32px; margin-bottom: 15px;">📍</div><div style="color: #9ca3af; font-size: 16px;">Map will appear here after you enter your location</div>';
                mapElement.parentNode.insertBefore(placeholder, mapElement);
                console.log('Placeholder created');
            } else {
                console.log('Has coordinates - showing map');
                initializeMap();
            }

            function initializeMap() {
                // Remove placeholder if exists
                const placeholder = document.getElementById('map-placeholder');
                if (placeholder) {
                    placeholder.remove();
                }

                // Show map
                mapElement.style.display = 'block';

                map = new google.maps.Map(mapElement, {
                    center: defaultLatLng,
                    zoom: 14,
                    mapTypeControl: false,
                    streetViewControl: false,
                    rotateControl: true,
                });

                marker = new google.maps.Marker({
                    map,
                    position: defaultLatLng,
                    draggable: true,
                });

                // Update fields if we have existing coordinates
                if (hasExistingCoords) {
                    updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                    updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
                }

                // Add marker drag listener
                marker.addListener("dragend", function (event) {
                    const lat = event.latLng.lat();
                    const lng = event.latLng.lng();
                    updateLocationFields(lat, lng);
                    updateAddressFromLatLng(lat, lng);
                });
            }

            // On place search
            searchBox.addListener("places_changed", () => {
                const places = searchBox.getPlaces();
                if (!places.length || !places[0].geometry?.location) return;

                // Initialize map if not already initialized
                if (!map) {
                    initializeMap();
                }

                const location = places[0].geometry.location;
                marker.setPosition(location);
                map.setCenter(location);
                map.setZoom(18);

                const lat = location.lat();
                const lng = location.lng();
                updateLocationFields(lat, lng);
                inputElement.value = places[0].formatted_address || "";
            });

            function updateLocationFields(lat, lng) {
                document.getElementById("latitude").value = lat;
                document.getElementById("longitude").value = lng;
            }

            function updateAddressFromLatLng(lat, lng) {
                geocoder.geocode({
                    location: {
                        lat,
                        lng
                    }
                }, (results, status) => {
                    if (status === "OK" && results[0]) {
                        inputElement.value = results[0].formatted_address;
                    }
                });
            }

            console.log('Customer Map initialization completed');
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', function () {
            // Small delay to ensure all elements are ready
            setTimeout(initCustomerMap, 100);
        });

        // Also try to initialize when Google Maps loads
        window.initMap = initCustomerMap;
    </script>
@endpush
{{-- @include('layouts.includes.google-map') --}}
