<?php $__env->startPush('js'); ?>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard profile-setting">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="sora black">Profile</h4>
                        <p class="fs-14 light-black sora"> View and update your profile details here.</p>
                    </div>

                    <?php if(auth()->check() && auth()->user()->hasRole('customer')): ?>
                        <div>
                            <button onclick="confirmDeactivation()"
                                class="drop-btn delete-btn btn btn-outline-danger  py-2 px-3 text-center"><i
                                    class="bi bi-trash p-0 red me-3"></i>Delete Profile
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="card white-box friends-cards ">
                        <div class="card-header align-items-center justify-content-center flex-column gap-3 py-20 my-4">

                            <img src="<?php echo e(asset('website') . '/' . ($user->profile->pic ?? '/assets/media/avatars/blank.png')); ?>"
                                class="customer_profile img-fluid" alt="card-image"
                                onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'" />
                            <p class="fs-22 sora black semi_bold"><?php echo e($user->name ?? ''); ?></p>
                        </div>
                        <div class="card-body">
                            <p class="fs-12 normal sora light-black"><span
                                    class="me-3"><?php echo $__env->make('svg.building', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                <?php echo e($user->email ?? ''); ?></p>
                            <?php if($user->profile->country): ?>
                                <p class="fs-12 normal sora light-black"><span
                                        class="me-3"><?php echo $__env->make('svg.pin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?></span>
                                    <?php echo e($user->profile?->location ?? ''); ?></p>
                            <?php endif; ?>
                            <div class="fs-12 normal sora light-black">
                                <?php if (isset($component)) { $__componentOriginal71c6471fa76ce19017edc287b6f4508c = $component; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.star-rating-display','data' => ['rating' => $user->averageRating,'totalReviews' => $user->total_reviews,'size' => 'sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('star-rating-display'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['rating' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->averageRating),'total-reviews' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($user->total_reviews),'size' => 'sm']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal71c6471fa76ce19017edc287b6f4508c)): ?>
<?php $component = $__componentOriginal71c6471fa76ce19017edc287b6f4508c; ?>
<?php unset($__componentOriginal71c6471fa76ce19017edc287b6f4508c); ?>
<?php endif; ?>
                            </div>

                            <?php if(auth()->check() &&
                                    !auth()->user()->hasAnyRole(['admin', 'super admin'])): ?>
                                <div class="d-flex gap-4 mt-5">
                                    <?php $__empty_1 = true; $__currentLoopData = $user->socials; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $social): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                        <a href="<?php echo e($social->link); ?>" target="_blank" class="logo-box">
                                            <img src="<?php echo e(asset('website') . '/' . $social->socialPlatform->image); ?>"
                                                alt="social-logo" height="30px" width="30px">
                                        </a>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                        <p>No Socials Found</p>
                                    <?php endif; ?>
                                </div>

                                <button type="button" class="blue-button mt-8" data-bs-toggle="modal"
                                    data-bs-target="#edit-social-modal">Edit
                                </button>

                                <a class="blue-button mt-3 text-center"
                                    href="<?php echo e(route('professional_profile', auth()->user()->profile->slug)); ?>">
                                    View Public Profile </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <div class="col-md-8">
                    <div class="row row-gap-5">
                        <?php if(Auth::user()->hasAnyRole(['individual', 'business', 'professional'])): ?>
                            <!-- businesss and individual -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Profile Images</p>
                                        <button type="button" class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#edit-media-modal"
                                            data-media-type="galleries">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5 profile-gallery">
                                            <?php $__empty_1 = true; $__currentLoopData = $user->galleries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-3 ">
                                                    <img src="<?php echo e(asset('website') . '/' . $gallery->image); ?>"
                                                        class="top-rated-image" alt="card-image" />
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p>No Images Found</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                        
                                    </div>
                                    <div class="card-body">
                                        <form id="personal-info-form" action="<?php echo e(route('personal-info.update')); ?>"
                                            method="POST" enctype="multipart/form-data">
                                            <?php echo csrf_field(); ?>
                                            <div class="row row-gap-5">
                                                <div class="col-md-12">
                                                    <label for="specification-image"
                                                        class="form-label form-input-labels">Image</label>
                                                    <div class="position-relative form-add-services">

                                                        <div class="image-input image-input-empty image-input-circle"
                                                            data-kt-image-input="true"
                                                            style="background-image: url(<?php echo e(asset('website') . '/' . auth()->user()->profile?->pic ?? 'images/image_input_holder.png'); ?>)">
                                                            <div class="image-input-wrapper w-125px h-125px">
                                                            </div>
                                                            <label
                                                                class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                                data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click" title="Change avatar">
                                                                <i class="ki-duotone ki-pencil fs-6"><span
                                                                        class="path1"></span><span
                                                                        class="path2"></span></i>

                                                                <input type="file" name="image"
                                                                    accept=".png, .jpg, .jpeg" />
                                                                <input type="hidden" name="avatar_remove" />
                                                            </label>
                                                            <span
                                                                class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                                data-bs-dismiss="click" title="Cancel avatar">
                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                            </span>
                                                            <span
                                                                class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="remove"
                                                                data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                title="Remove avatar">
                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 ">
                                                    <label for="full-name" class="form-label form-input-labels">Full
                                                        name</label>
                                                    <input type="text" class="form-control form-inputs-field"
                                                        placeholder="Enter full-name" id="full-name" name="name"
                                                        value="<?php echo e($user->name ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6 ">
                                                    <label for="email" class="form-label form-input-labels">Email
                                                        Address</label>
                                                    <input type="email" class="form-control form-inputs-field"
                                                        placeholder="Enter email address" id="email" name="email"
                                                        value="<?php echo e($user->email ?? ''); ?>" disabled>
                                                </div>
                                                <div class="col-md-6 ">
                                                    <label for="phone-number" class="form-label form-input-labels">Phone
                                                        Number</label>
                                                    <input type="tel" class="form-control form-inputs-field"
                                                        placeholder="Enter phone number " id="phone-number"
                                                        name="phone" value="<?php echo e($user->profile->phone ?? ''); ?>">
                                                </div>
                                                <div class="col-md-12 mb-2">
                                                    <label for="location"
                                                        class="form-label form-input-labels">Location</label>
                                                    <input id="pac-input" type="text"
                                                        class="form-control form-inputs-field"
                                                        placeholder="Enter your location" id="customer-location"
                                                        name="location"
                                                        value="<?php echo e(old('location', auth()->user()->profile?->location ?? '')); ?>">
                                                    <input type="hidden" name="lat"
                                                        value="<?php echo e(auth()->user()->profile?->lat ?? ''); ?>" id="latitude">
                                                    <input type="hidden" name="lng"
                                                        value="<?php echo e(auth()->user()->profile?->lng ?? ''); ?>" id="longitude">
                                                    <div class="custom_loc mt-2">
                                                        <div id="map" style="height: 300px"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12 text-end mt-6">
                                                <button type="submit" class="add-btn">Update</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Company details</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#edit-company-details-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12 ">
                                                <label for="company-name" class="form-label form-input-labels">Company
                                                    name</label>
                                                <input type="text" class="form-control form-inputs-field"
                                                    placeholder="Enter company name" id="company-name"
                                                    name="company-name" value="<?php echo e($user->profile->company_name ?? ''); ?>"
                                                    disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-id" class="form-label form-input-labels">Company
                                                    ID</label>
                                                <input type="email" class="form-control form-inputs-field"
                                                    placeholder="Enter company id" id="company-id" name="company-id"
                                                    value="<?php echo e($user->profile->company_id ?? ''); ?>" disabled>
                                            </div>
                                            <div class="col-md-6 ">
                                                <label for="company-vat-number"
                                                    class="form-label form-input-labels">Company VAT
                                                    number</label>
                                                <input type="tel" class="form-control form-inputs-field"
                                                    placeholder="Enter company vat number " id="company-vat-number"
                                                    name="company-vat-number"
                                                    value="<?php echo e($user->profile->vat_number ?? ''); ?>" disabled>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service</p>
                                        
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">My Services</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php $__empty_1 = true; $__currentLoopData = $user->services ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            <?php echo e($service->name); ?>

                                                        </p>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Services Found</p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Product Certifications</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#edit-product-certifications-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php $__empty_1 = true; $__currentLoopData = $user->product_cerficates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $productCertification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                        <p
                                                            class="fs-14 sora light-black normal service-details align-items-center">
                                                            <span>
                                                                <img src="<?php echo e(asset('website') . '/' . $productCertification->image); ?>"
                                                                    class="h-25px w-25px object-fit-contain rounded-pill top-rated-image"
                                                                    alt="card-image" />
                                                            </span>
                                                            <span> <?php echo e($productCertification->name ?? ''); ?></span>
                                                        </p>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Product Certifications Found</p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Certifications & Licenses</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#edit-certifications&licenses-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <?php $__empty_1 = true; $__currentLoopData = $user->certificates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $certificate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-6">
                                                    <div class="card card-box p-0 certificate-card"
                                                        data-certificate-id="<?php echo e($certificate->id); ?>">
                                                        <!-- Main Certificate Content -->
                                                        <div class="certificate-main-content p-3 d-flex gap-5  <?php echo e($certificate->exception ? 'cursor-pointer certificate-expandable' : ''); ?>"
                                                            data-certificate-id="<?php echo e($certificate->id); ?>"
                                                            <?php if($certificate->exception): ?> data-has-exception="true" <?php endif; ?>
                                                            <?php if($certificate->approval == 2 && $certificate->rejection_reason): ?> data-has-rejection="true" <?php endif; ?>>
                                                            <div class="certificate-image-container">
                                                                <?php if($certificate->image): ?>
                                                                    <img src="<?php echo e(asset('website') . '/' . $certificate->image); ?>"
                                                                        class="h-50px w-50px object-fit-contain top-rated-image cursor-pointer certificate-image"
                                                                        alt="certificate-image"
                                                                        onerror="this.src='<?php echo e(asset('website/assets/images/image_input_holder.png')); ?>'" />
                                                                <?php else: ?>
                                                                    <div
                                                                        class="h-50px w-50px d-flex align-items-center justify-content-center rounded">
                                                                        <i class="fas fa-certificate text-muted"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="certificate-info flex-grow-1">
                                                                <div class="d-flex align-items-center gap-2">
                                                                    <p class="black sora fs-16 semi_bold m-0">
                                                                        <?php echo e($certificate->title ?? ''); ?>

                                                                    </p>
                                                                    <?php if($certificate->exception || ($certificate->approval == 2 && $certificate->rejection_reason)): ?>
                                                                        <i class="fas fa-chevron-down certificate-toggle-icon"
                                                                            id="toggle-icon-<?php echo e($certificate->id); ?>"></i>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <p class="black fs-14 normal m-0 mt-1">
                                                                    <span class="link-gray">Issued by:
                                                                    </span><?php echo e($certificate->issued_by ?? ''); ?>

                                                                </p>
                                                                <p class="black fs-14 normal m-0">
                                                                    <span class="link-gray">Issue Date:
                                                                    </span><?php echo e($certificate->issued_date ?? ''); ?>

                                                                </p>
                                                                <p class="black fs-14 normal m-0">
                                                                    <span class="link-gray">End Date:
                                                                    </span><?php echo e($certificate->end_date ?? ''); ?>

                                                                </p>
                                                                <!-- Certificate Status and Exception -->
                                                                <div class="mt-2">
                                                                    <?php if($certificate->exception): ?>
                                                                        <span class="badge me-1"
                                                                            style="background-color: #ffc107; color: #FFF;">Exception</span>
                                                                    <?php endif; ?>
                                                                    
                                                                    <?php if($certificate->approval == 1): ?>
                                                                        <span class="badge bg-success text-white">Approved</span>
                                                                    <?php elseif($certificate->approval == 2): ?>
                                                                        <span
                                                                            class="badge bg-danger text-white">Rejected</span>
                                                                    <?php else: ?>
                                                                        <span
                                                                            class="badge blue-badge text-white">Pending</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- Exception Reason (Hidden by default) -->
                                                        <?php if($certificate->exception): ?>
                                                            <div class="certificate-exception-details"
                                                                id="exception-details-<?php echo e($certificate->id); ?>"
                                                                style="display: none;">
                                                                <div class="border-top p-3"
                                                                    style="background-color: #fff3cd;">
                                                                    <p class="fs-14 text-dark mb-0">
                                                                        <strong>Reason:</strong>
                                                                        <?php echo e($certificate->exception_reason ?? 'No reason provided'); ?>

                                                                    </p>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>

                                                        <!-- Rejection Reason (Shown by default) -->
                                                        <?php if($certificate->approval == 2 && $certificate->rejection_reason): ?>
                                                            <div class="certificate-rejection-details"
                                                                id="rejection-details-<?php echo e($certificate->id); ?>">
                                                                <div class="border-top p-3"
                                                                    style="background-color: #f8d7da;">
                                                                    <p class="fs-14 text-dark mb-0">
                                                                        <strong>Rejection Reason:</strong>
                                                                        <?php echo e($certificate->rejection_reason); ?>

                                                                    </p>
                                                                </div>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p class="fs-14 sora light-black normal service-details">No Certifications
                                                    Found</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Availability</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#edit-availability-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <p class="fs-14 black bold">Weekly Availability </p>
                                                <div class="d-flex gap-2 flex-wrap">
                                                    <?php $__empty_1 = true; $__currentLoopData = $user->openingHours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $availability): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            <?php echo e($availability->day); ?>

                                                            (<?php echo e(date('H:i', strtotime($availability->open))); ?> -
                                                            <?php echo e(date('H:i', strtotime($availability->close))); ?>)
                                                        </p>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Availability Found</p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <p class="fs-14 black regular">Holidays Availability</p>
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php $__empty_1 = true; $__currentLoopData = $user->allHolidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            <?php echo e($holiday->name); ?>

                                                            (<?php echo e($holiday->date); ?>)
                                                        </p>
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            <?php if(!$holiday->is_full_day && $holiday->start_time && $holiday->end_time): ?>
                                                                <?php echo e(date('H:i', strtotime($holiday->start_time))); ?> -
                                                                <?php echo e(date('H:i', strtotime($holiday->end_time))); ?>

                                                            <?php endif; ?>
                                                        </p>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            Holidays Availability Found</p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Intro Card</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#intro-cards-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <?php $__empty_1 = true; $__currentLoopData = $user->introCards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $introCard): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-6">
                                                    <div
                                                        class="card card-box p-0 flex-row justify-content-center align-items-center p-3 gap-5">
                                                        <div
                                                            class="card-header border-0 p-0 justify-content-center align-items-center">
                                                            <img src="<?php echo e(asset('website/' . $introCard->image)); ?>"
                                                                class="h-35px w-35px top-rated-image"
                                                                alt="card-image"
                                                                onerror="this.src='<?php echo e(asset('website/assets/images/default.png')); ?>'" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora w-700 m-0 dark-blue">
                                                                <?php echo e($introCard->heading ?? ''); ?>

                                                            </p>
                                                            <p class="fs-14 sora normal  m-0 light-gray">
                                                                <?php echo e($introCard->description ?? ''); ?>

                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p class="fs-14 sora light-black normal service-details">No
                                                    Intro Cards Found</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Portfolio</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal" data-bs-target="#edit-media-modal"
                                            data-media-type="portfolio">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5 profile-gallery">
                                            <?php $__empty_1 = true; $__currentLoopData = $user->portfolios; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $portfolio): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                <div class="col-md-3 ">
                                                    <img src="<?php echo e(asset('website') . '/' . $portfolio->image); ?>"
                                                      class="img-fluid top-rated-image" alt="card-image" />
                                                </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                <p>No Images Found</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- end business -->
                        <?php elseif(auth()->check() && auth()->user()->hasRole('customer')): ?>
                            <!-- customer -->
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Personal Info</p>
                                    </div>
                                    <div class="card-body">
                                        <form id="personal-info-form" action="<?php echo e(route('personal-info.update')); ?>"
                                            method="POST" enctype="multipart/form-data">
                                            <?php echo csrf_field(); ?>
                                            <div class="row row-gap-5">
                                                <div class="col-md-12">
                                                    <label for="specification-image"
                                                        class="form-label form-input-labels">Image</label>
                                                    <div class="position-relative form-add-services">

                                                        <div class="image-input image-input-empty image-input-circle"
                                                            data-kt-image-input="true"
                                                            style="background-image: url(<?php echo e(asset('website') . '/' . auth()->user()->profile?->pic ?? 'images/image_input_holder.png'); ?>)">
                                                            <div class="image-input-wrapper w-125px h-125px">
                                                            </div>
                                                            <label
                                                                class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                                data-kt-image-input-action="change"
                                                                data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                title="Change avatar">
                                                                <i class="ki-duotone ki-pencil fs-6"><span
                                                                        class="path1"></span><span
                                                                        class="path2"></span></i>

                                                                <input type="file" name="image"
                                                                    accept=".png, .jpg, .jpeg" />
                                                                <input type="hidden" name="avatar_remove" />
                                                            </label>
                                                            <span
                                                                class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="cancel"
                                                                data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                title="Cancel avatar">
                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                            </span>
                                                            <span
                                                                class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                                data-kt-image-input-action="remove"
                                                                data-bs-toggle="tooltip" data-bs-dismiss="click"
                                                                title="Remove avatar">
                                                                <i class="ki-outline ki-cross fs-3"></i>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6 ">
                                                    <label for="customer-full-name"
                                                        class="form-label form-input-labels">Full
                                                        name</label>
                                                    <input type="text" class="form-control form-inputs-field"
                                                        placeholder="Enter full-name" id="customer-full-name"
                                                        name="name" value="<?php echo e($user->name ?? ''); ?>">
                                                </div>
                                                <div class="col-md-6 ">
                                                    <label for="customer-email" class="form-label form-input-labels">Email
                                                    </label>
                                                    <input type="email" class="form-control form-inputs-field"
                                                        placeholder="Enter email address" id="customer-email"
                                                        name="email" value="<?php echo e($user->email ?? ''); ?>" disabled>
                                                </div>
                                            </div>
                                            <div class="col-md-12 my-4">
                                                <label for="customer-location"
                                                    class="form-label form-input-labels">Location</label>
                                                <input id="pac-input" type="text"
                                                    class="form-control form-inputs-field mb-6"
                                                    placeholder="Enter your location" id="customer-location"
                                                    name="location"
                                                    value="<?php echo e(old('location', auth()->user()->profile?->location ?? '')); ?>">
                                                <input type="hidden" name="lat"
                                                    value="<?php echo e(auth()->user()->profile?->lat ?? ''); ?>" id="latitude">
                                                <input type="hidden" name="lng"
                                                    value="<?php echo e(auth()->user()->profile?->lat ?? ''); ?>" id="longitude">
                                                <div class="custom_loc">
                                                    <div id="map" style="height: 300px"></div>
                                                </div>
                                            </div>

                                            <div class="col-md-12 text-end mt-6">
                                                <button type="submit" class="add-btn">Update</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Service Preferences</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0"
                                            data-bs-toggle="modal"
                                            data-bs-target="#service-preferences-modal">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <div class="d-flex gap-4 flex-wrap">
                                                    <?php $__empty_1 = true; $__currentLoopData = Auth::user()->service_preferences()->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                                        <p class="fs-14 sora light-black normal service-details">
                                                            <?php echo e($category->name); ?>

                                                        </p>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                                        <p class="fs-14 sora light-black normal service-details">No
                                                            preferences added</p>
                                                    <?php endif; ?>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Hair Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="hair">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <?php
                                                    $hairSpec = Auth::user()->getBodySpecificationByType('hair');
                                                ?>
                                                <?php if($hairSpec): ?>
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start customer-profile-img">
                                                            <img src="<?php echo e($hairSpec->image ? asset('website' . '/' . $hairSpec->image) : asset('website/assets/images/wax.png')); ?>"
                                                              class="img-fluid"  alt="hair-type-image" />
                                                        </div>
                                                        <div
                                                            class="card-body d-flex flex-column justify-content-center  p-0">
                                                            <p class="fs-16 mb-0 sora black semi_bold">
                                                                <?php echo e($hairSpec->name); ?>

                                                            </p>
                                                            <?php echo $hairSpec->description ?? ''; ?>

                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <p class="fs-15 black normal text-center">Hair type not added yet</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Skin Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="skin">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <?php
                                                    $skinSpec = Auth::user()->getBodySpecificationByType('skin');
                                                ?>
                                                <?php if($skinSpec): ?>
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start customer-profile-img">
                                                            <img src="<?php echo e($skinSpec->image ? asset('website' . '/' . $skinSpec->image) : asset('website/assets/images/wax.png')); ?>"
                                                              class="img-fluid"  alt="skin-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold"><?php echo e($skinSpec->name); ?>

                                                            </p>
                                                            <?php echo $skinSpec->description ?? ''; ?>

                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <p class="fs-15 black normal text-center">Skin type not added yet</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Body Type</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="body">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <?php
                                                    $bodySpec = Auth::user()->getBodySpecificationByType('body');
                                                ?>
                                                <?php if($bodySpec): ?>
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start customer-profile-img">
                                                            <img src="<?php echo e($bodySpec->image ? asset('website' . '/' . $bodySpec->image) : asset('website/assets/images/wax.png')); ?>"
                                                                class="img-fluid"
                                                                alt="body-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold"><?php echo e($bodySpec->name); ?>

                                                            </p>
                                                            <?php echo $bodySpec->description ?? ''; ?>

                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <p class="fs-15 black normal text-center">Body type not added yet</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="card card-box p-0">
                                    <div class="card-header align-items-center">
                                        <p class="sora black fs-16 semi_bold m-0">Allergies</p>
                                        <button type="button"
                                            class="fs-16 deep-blue semi_bold bg-transparent p-0 border-0 edit-body-spec-btn"
                                            data-type="allergy">Edit</button>
                                    </div>
                                    <div class="card-body">
                                        <div class="row row-gap-5">
                                            <div class="col-md-12">
                                                <?php
                                                    $allergySpec = Auth::user()->getBodySpecificationByType('allergy');
                                                ?>
                                                <?php if($allergySpec): ?>
                                                    <div class="card flex-row gap-4 border-0 shadow-none">
                                                        <div class="card-header p-0 border-0 align-items-start customer-profile-img">
                                                            <img src="<?php echo e($allergySpec->image ? asset('website' . '/' . $allergySpec->image) : asset('website/assets/images/wax.png')); ?>"
                                                              class="img-fluid"  alt="body-type-image" />
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <p class="fs-16 sora black semi_bold"><?php echo e($allergySpec->name); ?>

                                                            </p>
                                                            <p class="fs-14 sora normal  m-0 light-gray"> <?php echo $allergySpec->description ?? ''; ?> </p>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <p class="fs-15 black normal text-center">Allergies not added yet</p>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--  end customer -- -->
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    <?php echo $__env->make('dashboard.profile_settings.modal.edit-social-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make('dashboard.profile_settings.modal.edit-personal-info-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php if(auth()->user()->hasRole('customer')): ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.body-specifications-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.service-preferences-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php else: ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.edit-media-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.edit-company-details-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.edit-product-certifications-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.edit-certifications&licenses-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.edit-availability-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('dashboard.profile_settings.modal.intro-cards-modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <!-- jQuery is already loaded in master layout -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(config('services.google.api_key')); ?>&libraries=places&v=weekly"
        async defer></script>

    <script>
        // Certificate expandable functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Set initial state for toggle icons
            document.querySelectorAll('.certificate-expandable').forEach(element => {
                const certificateId = element.dataset.certificateId;
                const toggleIcon = document.getElementById(`toggle-icon-${certificateId}`);
                const exceptionDetails = document.getElementById(`exception-details-${certificateId}`);
                const rejectionDetails = document.getElementById(`rejection-details-${certificateId}`);
                
                if (toggleIcon) {
                    const hasVisibleDetails = (exceptionDetails && exceptionDetails.style.display === 'block') || 
                                           (rejectionDetails && rejectionDetails.style.display !== 'none');
                    if (hasVisibleDetails) {
                        toggleIcon.classList.add('rotated');
                    }
                }
            });

            // Function to toggle certificate details (exception and rejection)
            function toggleCertificateDetails(certificateId) {
                const exceptionDetails = document.getElementById(`exception-details-${certificateId}`);
                const rejectionDetails = document.getElementById(`rejection-details-${certificateId}`);
                const toggleIcon = document.getElementById(`toggle-icon-${certificateId}`);

                // Toggle exception details if exists
                if (exceptionDetails) {
                    if (exceptionDetails.style.display === 'none' || exceptionDetails.style.display === '') {
                        exceptionDetails.style.display = 'block';
                    } else {
                        exceptionDetails.style.display = 'none';
                    }
                }

                // Toggle rejection details if exists
                if (rejectionDetails) {
                    if (rejectionDetails.style.display === 'none') {
                        rejectionDetails.style.display = 'block';
                    } else {
                        rejectionDetails.style.display = 'none';
                    }
                }

                // Update toggle icon if any details are shown
                if (toggleIcon) {
                    const hasVisibleDetails = (exceptionDetails && exceptionDetails.style.display === 'block') || 
                                           (rejectionDetails && rejectionDetails.style.display !== 'none');
                    if (hasVisibleDetails) {
                        toggleIcon.classList.add('rotated');
                    } else {
                        toggleIcon.classList.remove('rotated');
                    }
                }
            }

            // Event delegation for certificate expansion
            document.addEventListener('click', function(event) {
                const certificateContent = event.target.closest('.certificate-expandable');
                if (certificateContent && (certificateContent.dataset.hasException === 'true' || certificateContent.dataset.hasRejection === 'true')) {
                    const certificateId = certificateContent.dataset.certificateId;
                    toggleCertificateDetails(certificateId);
                }
            });

            // Event delegation for certificate images - open in new tab
            document.addEventListener('click', function(event) {
                if (event.target.classList.contains('certificate-image')) {
                    event.stopPropagation();
                    const imageSrc = event.target.src;
                    window.open(imageSrc, '_blank');
                }
            });

            // Add keyboard support for accessibility
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Enter' || event.key === ' ') {
                    const target = event.target.closest('.certificate-expandable');
                    if (target && (target.dataset.hasException === 'true' || target.dataset.hasRejection === 'true')) {
                        event.preventDefault();
                        const certificateId = target.dataset.certificateId;
                        toggleCertificateDetails(certificateId);
                    }
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $('.edit-body-spec-btn').on('click', function() {
                var type = $(this).data('type');
                $('#type').val(type);
                var title = type.charAt(0).toUpperCase() + type.slice(1) + ' Type';
                $('#body-modal-title').text(title);
                $('#body_name').val('');
                $('#description').val('');
                // Reset image input
                var imageInput = $('#body-specifications-modal .image-input[data-kt-image-input="true"]');
                imageInput.addClass('image-input-empty').removeClass('image-input-changed');
                imageInput.find('.image-input-wrapper').css('background-image', 'none');
                imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');
                $("#body-specifications-modal").modal('show');
                $.ajax({
                    url: '<?php echo e(route('body-specifications.get')); ?>',
                    type: 'GET',
                    data: {
                        type: type
                    },
                    success: function(response) {
                        if (response.success && response.data) {
                            var data = response.data;
                            $('#body_name').val(data.name);
                            // Set CKEditor content if it exists
                            if (window.bodySpecEditor) {
                                window.bodySpecEditor.setData(data.description || '');
                            } else {
                                $('#description').val(data.description || '');
                            }
                            if (data.image) {
                                var baseImageUrl = $('meta[name="asset-url"]').attr(
                                    'content') || '/website';
                                var imageUrl = baseImageUrl + '/' + data.image;
                                var wrapper = imageInput.find('.image-input-wrapper');
                                wrapper.css('background-image', 'url(' + imageUrl + ')');
                                imageInput.removeClass('image-input-empty').addClass(
                                    'image-input-changed');
                                imageInput.find('[data-kt-image-input-action="remove"]')
                                    .removeClass('d-none');
                                imageInput.find('[data-kt-image-input-action="cancel"]')
                                    .removeClass('d-none');
                                // Store original image for cancel functionality
                                imageInput.data('original-image', imageUrl);
                            } else {
                                // Clear original image data
                                imageInput.removeData('original-image');
                            }
                        }
                    },
                    error: function() {
                        console.log('Error loading data, showing empty form');
                    }
                });
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            // Add custom validation method for CKEditor
            $.validator.addMethod("ckeditorRequired", function(value, element) {
                if (window.bodySpecEditor) {
                    var editorData = window.bodySpecEditor.getData();
                    return editorData.length > 0;
                }
                return value.length > 0;
            }, "Please enter description");

            $("#body-specifications-form").validate({
                submitHandler: function(form) {
                    var isValid = true;

                    // Remove all previous error messages
                    $('.error').remove();

                    // Validate name field
                    var nameValue = $('#body_name').val().trim();
                    if (!nameValue) {
                        isValid = false;
                        $('#body_name').after(
                            '<label class="error text-danger">Please enter name</label>');
                    }

                    // Validate image field
                    var fileInput = $('input[name="image"]')[0];
                    var hasFile = fileInput && fileInput.files.length > 0;
                    var hasExistingImage = $('.image-input').hasClass('image-input-changed');

                    if (!hasFile && !hasExistingImage) {
                        isValid = false;
                        $('input[name="image"]').closest('.form-add-services').after(
                            '<label class="error text-danger">Please upload an image</label>');
                    }

                    // Validate description field (CKEditor)
                    var descriptionValue = '';
                    if (window.bodySpecEditor) {
                        descriptionValue = window.bodySpecEditor.getData().trim();
                        $('#description').val(descriptionValue);
                    } else {
                        descriptionValue = $('#description').val().trim();
                    }

                    if (!descriptionValue || descriptionValue === '<p>&nbsp;</p>' ||
                        descriptionValue === '<p></p>') {
                        isValid = false;
                        if ($('.ck-editor').length > 0) {
                            $('.ck-editor').after(
                                '<label class="error text-danger">Please enter description</label>');
                        } else {
                            $('#description').after(
                                '<label class="error text-danger">Please enter description</label>');
                        }
                    }

                    // Submit form only if all validations pass
                    if (isValid) {
                        form.submit();
                    }

                    return false;
                }
            });

            $('#service-preferences-form').validate({
                rules: {
                    'categories[]': {
                        required: true
                    }
                },
                messages: {
                    'categories[]': {
                        required: "Please select at least one category"
                    }
                },
                errorElement: 'div',
                errorClass: 'text-danger fw-bold',
                errorPlacement: function(error, element) {
                    if (element.attr('name') === 'categories[]') {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });
        });
    </script>
    <script>
        // Initialize CKEditor for body specifications description
        $(document).ready(function() {
            $('#body-specifications-modal').on('shown.bs.modal', function() {
                if (!window.bodySpecEditor) {
                    ClassicEditor
                        .create(document.querySelector('#description'), {
                            toolbar: ['heading', '|', 'bold', 'italic', 'link', 'bulletedList',
                                'numberedList', '|', 'outdent', 'indent', '|', 'blockQuote',
                                'insertTable', '|', 'undo', 'redo'
                            ]
                        })
                        .then(editor => {
                            window.bodySpecEditor = editor;

                            // Add real-time validation for CKEditor
                            editor.model.document.on('change:data', () => {
                                const data = editor.getData();
                                if (data.trim() !== '' && data !== '<p>&nbsp;</p>') {
                                    // Remove error if content exists
                                    $('.ck-editor').next('.error').remove();
                                }
                            });
                        })
                        .catch(error => {
                            console.error('CKEditor initialization error:', error);
                        });
                }
            });

            // Clean up CKEditor when modal is hidden
            $('#body-specifications-modal').on('hidden.bs.modal', function() {
                if (window.bodySpecEditor) {
                    window.bodySpecEditor.destroy()
                        .then(() => {
                            window.bodySpecEditor = null;
                        })
                        .catch(error => {
                            console.error('CKEditor cleanup error:', error);
                        });
                }
                // Reset validation and clear errors
                $('#body-specifications-form').validate().resetForm();
                $('#body-specifications-form').find('.error').remove();
                $('.error').remove();
            });

            // Clear errors when modal opens
            $('#body-specifications-modal').on('shown.bs.modal', function() {
                $('.error').remove();
            });

            // Real-time validation - clear individual errors when fields are filled
            $(document).on('input', '#body_name', function() {
                if ($(this).val().trim()) {
                    $(this).next('.error').remove();
                }
            });

            $(document).on('change', 'input[name="image"]', function() {
                if (this.files.length > 0) {
                    $(this).closest('.form-add-services').next('.error').remove();
                }
            });
        });
    </script>
    <script>
        // Dynamic validation for edit-social-form (supports multiple social cards)
        $(document).ready(function() {
            // add custom URL rule
            $.validator.addMethod("validUrl", function(value, element) {
                if (value === "") return true;
                const re = /^(https?:\/\/)?([^\s.]+\.[^\s]{2,}|localhost)(\/\S*)?$/i;
                return re.test(value);
            }, "Enter a valid URL.");

            $("#edit-social-form").validate({
                ignore: ":hidden:not(:file)",
                errorClass: "is-invalid",
                errorElement: "div",
                errorPlacement: function(error, element) {
                    error.addClass("error-message text-danger mt-1");
                    if (element.attr("type") === "file") {
                        element.closest(".form-add-services").after(error);
                    } else {
                        error.insertAfter(element);
                    }
                },
                submitHandler: function(form) {
                    var isValid = true;

                    // Remove all previous error messages
                    $('.social-block .error-message').remove();
                    $('.social-block .is-invalid').removeClass('is-invalid');

                    // Check for duplicate social platforms first
                    var selectedPlatforms = [];
                    var duplicateFound = false;

                    $('.social-block').each(function(index) {
                        var nameInput = $(this).find('select[name*="[social_platform_id]"]');
                        var platformValue = nameInput.val();

                        if (platformValue && selectedPlatforms.includes(platformValue)) {
                            duplicateFound = true;
                            nameInput.addClass('is-invalid');
                            nameInput.after(
                                '<div class="error-message text-danger mt-1">This social platform is already selected.</div>'
                            );
                            isValid = false;
                        } else if (platformValue) {
                            selectedPlatforms.push(platformValue);
                        }
                    });

                    // Validate each social block - all fields are mandatory
                    $('.social-block').each(function(index) {
                        var nameInput = $(this).find('select[name*="[social_platform_id]"]');
                        var linkInput = $(this).find('input[name*="[link]"]');

                        // Validate select field (only if no duplicate error already shown)
                        if (!nameInput.val()) {
                            isValid = false;
                            nameInput.addClass('is-invalid');
                            nameInput.after(
                                '<div class="error-message text-danger mt-1">This field is required.</div>'
                            );
                        }

                        // Validate link field
                        if (!linkInput.val().trim()) {
                            isValid = false;
                            linkInput.addClass('is-invalid');
                            linkInput.after(
                                '<div class="error-message text-danger mt-1">This field is required.</div>'
                            );
                        } else {
                            // Validate URL format
                            const re =
                                /^(https?:\/\/)?([^\s.]+\.[^\s]{2,}|localhost)(\/\S*)?$/i;
                            if (!re.test(linkInput.val().trim())) {
                                isValid = false;
                                linkInput.addClass('is-invalid');
                                linkInput.after(
                                    '<div class="error-message text-danger mt-1">Enter a valid URL.</div>'
                                );
                            }
                        }
                    });

                    if (isValid) {
                        form.submit();
                    }

                    return false;
                }
            });

            // Real-time validation on input
            $(document).on('change', '.social-block select', function() {
                var currentSelect = $(this);
                var currentValue = currentSelect.val();

                // Remove previous error messages for this field
                currentSelect.removeClass('is-invalid is-valid');
                currentSelect.next('.error-message').remove();

                if (currentValue) {
                    // Check for duplicates
                    var isDuplicate = false;
                    $('.social-block select[name*="[social_platform_id]"]').not(currentSelect).each(
                        function() {
                            if ($(this).val() === currentValue) {
                                isDuplicate = true;
                                return false; // break the loop
                            }
                        });

                    if (isDuplicate) {
                        currentSelect.addClass('is-invalid');
                        currentSelect.after(
                            '<div class="error-message text-danger mt-1">This social platform is already selected.</div>'
                        );
                    } else {
                        currentSelect.addClass('is-valid');
                    }
                }
            });

            // Clear errors when modal opens
            $('#edit-social-modal').on('shown.bs.modal', function() {
                $('.social-block .error-message').remove();
                $('.social-block .is-invalid').removeClass('is-invalid');
            });
        });
    </script>
    <script>
        // Simple validation for intro-cards-form
        $(document).ready(function() {
            $('#intro-cards-form').on('submit', function(e) {
                e.preventDefault(); // prevent default form submission

                // Clear previous errors
                $('.error-message').remove();
                $('.is-invalid').removeClass('is-invalid');

                let formValid = true; // assume form is valid
                let cardsWithData = 0;

                // Loop through each card block
                $('.intro-card-block').each(function() {
                    const $block = $(this);
                    const $image = $block.find('input[type="file"]');
                    const $heading = $block.find('input[name*="[heading]"]');
                    const $description = $block.find('textarea[name*="[description]"]');

                    const heading = $heading.val().trim();
                    const description = $description.val().trim();
                    const imageFile = $image[0]?.files[0];

                    // Check for existing background image
                    const backgroundImage = $block.find('.image-input').css('background-image');
                    const hasExistingImage = backgroundImage &&
                        backgroundImage !== 'none' &&
                        !backgroundImage.includes('image_input_holder.png') &&
                        !backgroundImage.includes('default.png');

                    const hasData = heading || description || imageFile || hasExistingImage;

                    // Always validate all visible cards, regardless of whether they have data
                    // Image validation
                    if (!imageFile && !hasExistingImage) {
                        const $imageContainer = $image.closest('.form-add-services');
                        if ($imageContainer.next('.error-message').length === 0) {
                            $imageContainer.after(
                                '<div class="error-message text-danger mt-1">Image is required.</div>'
                            );
                        }
                        formValid = false;
                    }

                    // Heading validation
                    if (!heading) {
                        $heading.addClass('is-invalid');
                        if ($heading.next('.error-message').length === 0) {
                            $heading.after(
                                '<div class="error-message text-danger mt-1">Heading is required.</div>'
                            );
                        }
                        formValid = false;
                    } else if (heading.length > 64) {
                        $heading.addClass('is-invalid');
                        if ($heading.next('.error-message').length === 0) {
                            $heading.after(
                                '<div class="error-message text-danger mt-1">Heading must not exceed 64 characters.</div>'
                            );
                        }
                        formValid = false;
                    }

                    // Description validation
                    if (!description) {
                        $description.addClass('is-invalid');
                        if ($description.next('.error-message').length === 0) {
                            $description.after(
                                '<div class="error-message text-danger mt-1">Description is required.</div>'
                            );
                        }
                        formValid = false;
                    } else if (description.length > 150) {
                        $description.addClass('is-invalid');
                        if ($description.next('.error-message').length === 0) {
                            $description.after(
                                '<div class="error-message text-danger mt-1">Description must not exceed 150 characters.</div>'
                            );
                        }
                        formValid = false;
                    }

                    // Only count cards with data for the 3,6,9 rule
                    if (hasData) {
                        cardsWithData++;
                    }
                });

                // First check if individual fields are valid
                if (!formValid) {
                    return;
                }

                // Only after field validation passes, check card count and show toast
                if (![0, 3, 6, 9].includes(cardsWithData)) {
                    toastr.clear();
                    toastr.error('Please submit exactly 3, 6, or 9 intro cards.',
                    'Invalid Number of Cards');
                    return;
                }

                // Submit the form properly with files
                $(this)[0].submit();
            });

            $(document).on('input', '.intro-card-block input, .intro-card-block textarea', function() {
                const value = $(this).val().trim();
                if (value) {
                    $(this).removeClass('is-invalid');
                    $(this).next('.error-message').remove();
                }
            });

            $(document).on('change', '.intro-card-block input[type="file"]', function() {
                if (this.files.length > 0) {
                    $(this).closest('.form-add-services').next('.error-message').remove();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            let socialIndex = $('.social-block').length;

            // Add More Social Block
            $('.add-social-btn').on('click', function() {
                const newSocialBlock = `
                <div class="gray-card social-block mb-4" data-index="${socialIndex}">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="fs-20 mb-0">Social ${socialIndex + 1}</h5>
                        <button type="button" class="btn btn-danger btn-sm delete-social-btn">Delete Block</button>
                    </div>
                    <div class="row row-gap-3">
                        <div class="col-md-6">
                            <div>
                            <label for="social-name-${socialIndex}" class="form-label form-input-labels">Social Name</label>
                            <select class="form-select form-select-field" id="social-platform-${socialIndex}" name="socials[${socialIndex}][social_platform_id]" data-control="select2" data-placeholder="Select an option">
                                <option selected disabled>Select Social</option>
                                <?php $__currentLoopData = $socialPlatforms; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $platform): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($platform->id); ?>"><?php echo e($platform->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>

                            </div>

                        </div>
                        <div class="col-md-6">
                            <label for="social-link-${socialIndex}" class="form-label form-input-labels">Social Link</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter social link"
                                id="social-link-${socialIndex}" name="socials[${socialIndex}][link]" value="">
                        </div>
                    </div>
                </div>
            `;

                $('#social-blocks-container').append(newSocialBlock);
                socialIndex++;

                // Reinitialize image input for the new block
                KTImageInput.createInstances();
            });

            // Delete Social Block
            $(document).on('click', '.delete-social-btn', function() {
                $(this).closest('.social-block').remove();
                reindexSocialBlocks();
            });

            // Reindex social blocks after deletion
            function reindexSocialBlocks() {
                $('.social-block').each(function(index) {
                    $(this).attr('data-index', index);
                    $(this).find('h5').text('Social ' + (index + 1));

                    // Update all input names and IDs
                    $(this).find('select[name*="[social_platform_id]"]').attr('name', 'socials[' + index +
                        '][social_platform_id]').attr(
                        'id', 'social-platform-' + index);
                    $(this).find('input[name*="[link]"]').attr('name', 'socials[' + index + '][link]').attr(
                        'id', 'social-link-' + index);
                    $(this).find('label[for*="social-platform"]').attr('for', 'social-platform-' + index);
                    $(this).find('label[for*="social-link"]').attr('for', 'social-link-' + index);
                });

                socialIndex = $('.social-block').length;
            }
        });
    </script>
    <script>
        $(document).ready(function() {
            let currentMediaType = '';
            let mediaIndex = 0;

            // Dynamic modal handler
            $('[data-bs-target="#edit-media-modal"]').on('click', function() {
                currentMediaType = $(this).data('media-type');
                loadMediaModal(currentMediaType);
            });

            function loadMediaModal(type) {
                const isGallery = type === 'galleries';
                const config = getMediaConfig(type);

                // Update modal content
                $('#media-modal-title').text(isGallery ? 'Edit User Galleries' : 'Edit Portfolio');
                $('#media-section-title').text(isGallery ? 'Gallery Images' : 'Portfolio Images');
                $('#edit-media-form').attr('action', `/media/${type}/save`);

                // Load existing media items
                loadExistingMedia(type, config);

                // Initialize functionality
                initializeMediaFunctionality(config);
                initializeValidation(config);
            }

            function getMediaConfig(type) {
                const isGallery = type === 'galleries';
                return {
                    type: type,
                    isGallery: isGallery,
                    blockClass: isGallery ? 'gallery-block' : 'portfolio-block',
                    deleteBtnClass: isGallery ? 'delete-gallery-block-btn' : 'delete-portfolio-block-btn',
                    inputPrefix: isGallery ? 'galleries' : 'portfolio',
                    imagePrefix: isGallery ? 'gallery-image' : 'portfolio-image'
                };
            }

            function loadExistingMedia(type, config) {
                const mediaItems = type === 'galleries' ? <?php echo json_encode($user->galleries, 15, 512) ?> :
                    <?php echo json_encode($user->portfolios, 15, 512) ?>;
                let html = '';

                if (mediaItems && mediaItems.length > 0) {
                    mediaItems.forEach((item, index) => {
                        html += generateMediaBlock(config, index, item.image, true);
                    });
                    mediaIndex = mediaItems.length;
                } else {
                    html = generateMediaBlock(config, 0, null, false);
                    mediaIndex = 1;
                }

                $('#media-blocks-container').html(html);
                KTImageInput.createInstances();
                updateDeleteButtonVisibility(config);
            }

            function generateMediaBlock(config, index, imageUrl = null, hasExisting = false) {
                const imageClass = hasExisting ? 'image-input-changed' : 'image-input-empty';
                const imageStyle = hasExisting ?
                    `style="background-image: url('/website/${imageUrl}')"` :
                    `style="background-image: url('/website/assets/images/image_input_holder.png')"`;
                const oldImageInput = hasExisting ?
                    `<input type="hidden" name="${config.inputPrefix}[${index}][old_image]" value="${imageUrl}" />` :
                    '';
                const deleteVisibility = (index === 0) ? 'd-none' : '';

                return `
                        <div class="${config.blockClass} mb-4" data-index="${index}">
                            <div class="row align-items-center">
                                <div class="col-md-10">
                                    <label for="${config.imagePrefix}-${index}" class="form-label form-input-labels">Image ${index + 1}</label>
                                    <div class="position-relative form-add-services">
                                        <div class="image-input ${imageClass} image-input-circle" data-kt-image-input="true" ${imageStyle}>
                                            <div class="image-input-wrapper w-125px h-125px" ${hasExisting ? imageStyle : ''}></div>
                                            <label class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                data-kt-image-input-action="change" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Change image">
                                                <i class="ki-duotone ki-pencil fs-6"><span class="path1"></span><span class="path2"></span></i>
                                                <input type="file" name="${config.inputPrefix}[${index}][image]" accept=".png,.jpg,.jpeg,.webp" />
                                                ${oldImageInput}
                                            </label>
                                            <span class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Cancel image">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                            <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                data-kt-image-input-action="remove" data-bs-toggle="tooltip" data-bs-dismiss="click" title="Remove image">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                        </div>
                                    </div>

                                     <div class="d-flex justify-content-center mt-4">
                                    <button type="button" class="btn btn-danger btn-sm ${config.deleteBtnClass} ${deleteVisibility}">Delete</button>
                                </div>
                                </div>

                            </div>
                        </div>
                    `;
            }

            function initializeMediaFunctionality(config) {
                // Remove previous event handlers
                $('#add-media-btn').off('click');
                $(document).off('click', `.${config.deleteBtnClass}`);

                // Add More Block
                $('#add-media-btn').on('click', function() {
                    const newBlock = generateMediaBlock(config, mediaIndex, null, false);
                    $('#media-blocks-container').append(newBlock);
                    mediaIndex++;
                    updateDeleteButtonVisibility(config);
                    KTImageInput.createInstances();
                });

                // Delete Block
                $(document).on('click', `.${config.deleteBtnClass}`, function() {
                    $(this).closest(`.${config.blockClass}`).remove();
                    reindexBlocks(config);
                    updateDeleteButtonVisibility(config);
                });

                // Handle image removal
                $(document).off('click', '[data-kt-image-input-action="remove"]').on('click',
                    '[data-kt-image-input-action="remove"]',
                    function() {
                        const block = $(this).closest(`.${config.blockClass}`);
                        if (block.length) {
                            const oldImageInput = block.find('input[name*="[old_image]"]');
                            if (oldImageInput.length) {
                                oldImageInput.val('');
                            }
                            // Clear validation errors
                            block.find('.error').remove();
                        }
                    });
            }

            function reindexBlocks(config) {
                $(`.${config.blockClass}`).each(function(index) {
                    $(this).attr('data-index', index);
                    $(this).find('input[type="file"]').attr('name',
                        `${config.inputPrefix}[${index}][image]`);
                    $(this).find('input[name*="[old_image]"]').attr('name',
                        `${config.inputPrefix}[${index}][old_image]`);
                    $(this).find(`label[for*="${config.imagePrefix}"]`).attr('for',
                        `${config.imagePrefix}-${index}`).text(`Image ${index + 1}`);
                });
                mediaIndex = $(`.${config.blockClass}`).length;
            }

            function updateDeleteButtonVisibility(config) {
                const blocks = $(`.${config.blockClass}`);
                blocks.each(function(index) {
                    const deleteBtn = $(this).find(`.${config.deleteBtnClass}`);
                    if (blocks.length === 1 || index === 0) {
                        deleteBtn.addClass('d-none');
                    } else {
                        deleteBtn.removeClass('d-none');
                    }
                });
            }

            function initializeValidation(config) {
                // Remove previous validation
                $('#edit-media-form').off('submit').removeData('validator');

                $('#edit-media-form').validate({
                    ignore: [],
                    errorClass: "error",
                    errorElement: "label",
                    errorPlacement: function(error, element) {
                        element.closest('.position-relative').after(error);
                    },
                    submitHandler: function(form) {
                        if (validateAllBlocks(config)) {
                            form.submit();
                        }
                        return false;
                    }
                });

                // Real-time validation on file change
                $(document).off('change', `input[type="file"][name*="${config.inputPrefix}"]`)
                    .on('change', `input[type="file"][name*="${config.inputPrefix}"]`, function() {
                        validateSingleBlock($(this).closest(`.${config.blockClass}`), config);
                    });

                // Clear errors when modal opens
                $('#edit-media-modal').off('shown.bs.modal').on('shown.bs.modal', function() {
                    $(`.${config.blockClass} .error`).remove();
                });
            }

            function validateAllBlocks(config) {
                let isValid = true;
                $(`.${config.blockClass} .error`).remove();

                $(`.${config.blockClass}`).each(function() {
                    if (!validateSingleBlock($(this), config)) {
                        isValid = false;
                    }
                });

                return isValid;
            }

            function validateSingleBlock($block, config) {
                const fileInput = $block.find('input[type="file"]')[0];
                const oldImageInput = $block.find('input[name*="[old_image]"]');
                const hasOldImage = oldImageInput.length > 0 && oldImageInput.val() !== '';
                const hasNewImage = fileInput && fileInput.files.length > 0;
                const imageContainer = $block.find('.position-relative');

                // Remove existing errors
                $block.find('.error').remove();

                // Check if image is required
                if (!hasOldImage && !hasNewImage) {
                    imageContainer.after('<label class="error text-danger">This image is required</label>');
                    return false;
                }

                // Validate new file if uploaded
                if (hasNewImage) {
                    const file = fileInput.files[0];
                    const fileName = file.name.toLowerCase();
                    const fileSize = file.size;
                    const maxSize = 5 * 1024 * 1024; // 5MB
                    const allowedTypes = ['jpg', 'jpeg', 'png', 'webp'];
                    const fileExtension = fileName.split('.').pop();

                    // File type validation
                    if (!allowedTypes.includes(fileExtension)) {
                        imageContainer.after(
                            '<label class="error text-danger">Please select JPG, JPEG, PNG, or WebP image</label>'
                        );
                        return false;
                    }

                    // File size validation
                    if (fileSize > maxSize) {
                        imageContainer.after(
                            '<label class="error text-danger">Image size must be less than 5MB</label>');
                        return false;
                    }

                    // Image dimensions validation (optional)
                    validateImageDimensions(file, imageContainer);
                }

                return true;
            }

            function validateImageDimensions(file, container) {
                const img = new Image();
                const url = URL.createObjectURL(file);

                img.onload = function() {
                    const width = this.naturalWidth;
                    const height = this.naturalHeight;

                    // Minimum dimensions check
                    if (width < 100 || height < 100) {
                        container.find('.error').remove();
                        container.after(
                            '<label class="error text-danger">Image must be at least 100x100 pixels</label>'
                        );
                    }

                    // Maximum dimensions check
                    if (width > 5000 || height > 5000) {
                        container.find('.error').remove();
                        container.after(
                            '<label class="error text-danger">Image must not exceed 5000x5000 pixels</label>'
                        );
                    }

                    URL.revokeObjectURL(url);
                };

                img.src = url;
            }
        });

        // Certifications & Licenses Modal JavaScript
        let certIndex = 0;

        function initFileUpload($group) {
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];
            const $dropArea = $group.find(".upload-box");
            const $fileInput = $group.find("input[type='file']");
            const $previewArea = $group.find(".preview-container");

            $dropArea.off();
            $fileInput.off();

            $dropArea.on("click", function() {
                $fileInput.click();
            });

            $fileInput.on("change", function(e) {
                const file = e.target.files[0];
                if (file) handleFile(file);
            });

            $dropArea.on("dragover", function(e) {
                e.preventDefault();
                $dropArea.css("background", "#eee");
            });

            $dropArea.on("dragleave", function(e) {
                e.preventDefault();
                $dropArea.css("background", "");
            });

            $dropArea.on("drop", function(e) {
                e.preventDefault();
                $dropArea.css("background", "");
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    handleFile(files[0]);
                }
            });

            function handleFile(file) {
                if (!allowedImages.includes(file.type)) {
                    swal.fire({
                        title: "Error",
                        text: "Only PNG, JPEG, and JPG files are allowed.",
                        icon: "error"
                    });
                    return;
                }

                if (file.size > 2 * 1024 * 1024) {
                    swal.fire({
                        title: "Error",
                        text: "File size must be less than 2MB.",
                        icon: "error"
                    });
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    // Remove existing images (both new previews and existing images)
                    $previewArea.find('.preview-box, .preview-item').remove();

                    $previewArea.append(`
                            <div class="preview-box new-image">
                                <img src="${e.target.result}" alt="Preview">
                                <button type="button" class="remove-image">×</button>
                            </div>
                        `);
                };
                reader.readAsDataURL(file);

                // Set the file to the input
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(file);
                $fileInput[0].files = dataTransfer.files;
            }

            // Remove preview
            $previewArea.on('click', '.remove-preview, .remove-image', function() {
                $(this).closest('.preview-box, .preview-item').remove();
                $fileInput.val('');
            });
        }

        // Function to remove existing images
        function removeExistingImage(button, index) {
            const $previewBox = $(button).closest('.preview-box');
            const $group = $(button).closest('.file-upload-group, .gray-card');

            // Remove the preview box
            $previewBox.remove();

            // Clear the old_image hidden input to indicate deletion
            $group.find(`input[name="certificates[${index}][old_image]"]`).val('');

            // Add a flag to indicate the image should be deleted
            if (!$group.find(`input[name="certificates[${index}][delete_image]"]`).length) {
                $group.append(`<input type="hidden" name="certificates[${index}][delete_image]" value="1">`);
            }
        }

        function updateCertificateNumbers() {
            $('.gray-card').filter(function() {
                return $(this).find('input[name*="certificates"]').length > 0;
            }).each(function(index) {
                $(this).find('h6').text(`Certificate #${index + 1}`);
            });
        }

        function addCertificationBlock() {
            const wrapper = document.getElementById('certifications-wrapper');
            // Count only main certificate containers, not nested file-upload-groups
            const currentCount = $('.gray-card').filter(function() {
                return $(this).find('input[name*="certificates"]').length > 0;
            }).length;
            certIndex = currentCount;

            const newBlock = `
                    <div class="gray-card my-5 file-upload-group share-certificates">
                        <h6 class="mb-8">Certificate #${certIndex + 1}</h6>
                        <!-- Hidden field for certificate ID (empty for new certificates) -->
                        <input type="hidden" name="certificates[${certIndex}][id]" value="">
                        <div class="row">
                            <div class="col-md-12 mb-4">
                                <label class="fieldlabels">Certification Title*</label>
                                <input class="w-100" type="text" name="certificates[${certIndex}][title]" placeholder="Enter certification title">

                            </div>

                            <div class="col-md-12 mb-4">
                                <label class="fieldlabels">Issued by*</label>
                                <input class="no_validate w-100" type="text" name="certificates[${certIndex}][issued_by]" placeholder="Enter name">
                            </div>

                            <div class="col-md-6">
                                <label class="fieldlabels">Issued Date*</label>
                                <input class="no_validate w-100 issued-date" type="date" name="certificates[${certIndex}][issued_date]" placeholder="Enter issued date" data-index="${certIndex}">
                                <label class="error text-danger" style="display: none;"></label>
                            </div>
                            <div class="col-md-6">
                                <label class="fieldlabels">End Date*</label>
                                <input class="no_validate w-100 end-date" type="date" name="certificates[${certIndex}][end_date]" placeholder="Enter end date" data-index="${certIndex}" disabled style="background-color: #f8f9fa; color: #6c757d;">
                                <label class="error text-danger" style="display: none;"></label>
                            </div>
                            <div class="col-md-12 form-border">
                            <p class="manrope fw-600 light-black">Share Certificates</p>
                            <div class="file-upload-group">
                                <label class="upload-box">
                                    <img src="/website/assets/images/upload.svg" alt="Upload Icon">
                                    <p>Upload Certificate</p>
                                    <p class="mb-0">Maximum file size: 2 MB</p>
                                    <p>Supported format: JPG and PNG</p>
                                    <span class="add-file">
                                        <p class="upload-cert-btn no_validate">Upload</p>
                                    </span>
                                    <input type="file" name="certificates[${certIndex}][image]" class="file-input no_validate" hidden="">
                                </label>
                                <div class="preview-container"></div>
                            </div>
                            <div class="exception-checkbox">
                                <label class="cert-excep">
                                    <input class="no_validate" type="checkbox" name="certificates[${certIndex}][exception]" id="exceptionToggle_${certIndex}">
                                    <span class="checkmark">Certificate Exception</span>
                                </label>
                                <div class="exception-textarea">
                                    <label class="mb-2" for="w3review_${certIndex}">Reason for Exception</label>
                                    <textarea class="mb-0 no_validate w-100" id="w3review_${certIndex}" name="certificates[${certIndex}][exception_reason]" rows="4" cols="50" placeholder="Write reason for exception"></textarea>
                                </div>
                            </div>
                            </div>
                            <div class="mt-3 d-flex justify-content-between">
                                <button type="button" class="delete-block">Delete This Block</button>
                            </div>
                        </div>
                    </div>
                `;

            wrapper.insertAdjacentHTML('beforeend', newBlock);

            const $newGroup = $('.file-upload-group').last();
            initFileUpload($newGroup);

            // Initialize date functionality for the new block
            const $newIssuedDate = $newGroup.find('.issued-date');
            if ($newIssuedDate.length && typeof handleIssuedDateChange === 'function') {
                handleIssuedDateChange($newIssuedDate[0]);
            }

            $newGroup.find('.delete-block').on('click', function() {
                $newGroup.remove();
                updateCertificateNumbers();
                // updateDeleteButtonVisibility();
            });

            // Update numbering after adding
            updateCertificateNumbers();
            // updateDeleteButtonVisibility();
        }

        // Initialize certifications modal
        $(document).ready(function() {
            $('.file-upload-group').each(function() {
                initFileUpload($(this));
            });

            $(document).on('click', '.delete-block', function() {
                const $group = $(this).closest('.file-upload-group');
                const $grayCard = $(this).closest('.gray-card');

                if ($group.length > 0) {
                    $group.remove();
                } else if ($grayCard.length > 0) {
                    $grayCard.remove();
                }

                updateCertificateNumbers();
                // updateDeleteButtonVisibility();
            });

            $(document).on("dragover drop", function(e) {
                e.preventDefault();
            });

            // Exception checkbox toggle
            $(document).on('change', 'input[name*="[exception]"]', function() {
                const $checkbox = $(this);
                const $exceptionContainer = $checkbox.closest('.exception-checkbox');
                const $textarea = $exceptionContainer.find('.exception-textarea');
                const $uploadBox = $(this).closest('.gray-card').find('.upload-box');
                const $group = $(this).closest('.gray-card');
                const $previewArea = $group.find('.preview-container');

                if ($checkbox.is(':checked')) {
                    // Show textarea with expand effect
                    $textarea.show();
                    $exceptionContainer.addClass('expanded');
                    $uploadBox.addClass('disabled');

                    // Mark existing images for deletion when exception is checked
                    const $oldImageInput = $group.find('input[name*="[old_image]"]');
                    if ($oldImageInput.length && $oldImageInput.val()) {
                        // Get the index from the input name
                        const inputName = $oldImageInput.attr('name');
                        const indexMatch = inputName.match(/certificates\[(\d+)\]/);
                        if (indexMatch) {
                            const index = indexMatch[1];
                            // Clear old image value
                            $oldImageInput.val('');
                            // Add delete flag
                            if (!$group.find(`input[name="certificates[${index}][delete_image]"]`).length) {
                                $group.append(
                                    `<input type="hidden" name="certificates[${index}][delete_image]" value="1">`
                                );
                            }
                        }
                    }

                    // Remove all preview images (both existing and newly uploaded)
                    $previewArea.find('.preview-box').remove();
                    $previewArea.empty();

                    // Clear file input
                    $group.find('input[type="file"]').val('');
                } else {
                    // Hide textarea
                    $textarea.hide();
                    $exceptionContainer.removeClass('expanded');
                    $textarea.find('textarea').val('');
                    $uploadBox.removeClass('disabled');

                    // Remove delete flag when unchecking exception
                    $group.find('input[name*="[delete_image]"]').remove();
                }
            });

            // Initialize exception textarea visibility on page load
            $('input[name*="[exception]"]').each(function() {
                const $checkbox = $(this);
                const $exceptionContainer = $checkbox.closest('.exception-checkbox');
                const $textarea = $exceptionContainer.find('.exception-textarea');
                const $uploadBox = $(this).closest('.gray-card').find('.upload-box');

                if ($checkbox.is(':checked')) {
                    $textarea.show();
                    $exceptionContainer.addClass('expanded');
                    $uploadBox.addClass('disabled');
                } else {
                    $textarea.hide();
                    $exceptionContainer.removeClass('expanded');
                    $uploadBox.removeClass('disabled');
                }
            });

            updateDeleteButtonVisibility();
        });

        // Add More button for certifications
        $(document).on('click', '#addMoreBtn', function() {
            addCertificationBlock();
        });

        // Certifications & Licenses Validation
        $(document).ready(function() {
            // Real-time validation for end date
            $(document).on('change', 'input[name$="[end_date]"]', function() {
                validateCertificateDates($(this));
            });

            // Function to validate certificate dates
            function validateCertificateDates($endDateInput) {
                const endDateName = $endDateInput.attr('name');
                const issuedDateName = endDateName.replace('[end_date]', '[issued_date]');
                const $issuedDateInput = $(`input[name="${issuedDateName}"]`);

                const issuedDate = $issuedDateInput.val();
                const endDate = $endDateInput.val();

                // Clear previous error
                $endDateInput.next('.error').remove();

                if (!issuedDate || !endDate) {
                    return true; // Don't validate if either is empty
                }

                if (new Date(endDate) <= new Date(issuedDate)) {
                    $endDateInput.after(
                        '<label class="error text-danger">End date must be after issued date</label>');
                    $endDateInput.focus();
                    return false;
                }
                return true;
            }

            // Form submission validation
            $('#edit-certifications\\&licenses-form').on('submit', function(e) {
                let hasErrors = false;

                // Clear all previous errors
                $('.error').remove();

                // Get all certificate blocks
                const $certificateBlocks = $('.gray-card').filter(function() {
                    return $(this).find('input[name*="certificates"]').length > 0;
                });

                // Validate each certificate block
                $certificateBlocks.each(function(index) {
                    const $block = $(this);
                    const blockNumber = index + 1;

                    // Get field values
                    const $titleInput = $block.find('input[name$="[title]"]');
                    const $issuedByInput = $block.find('input[name$="[issued_by]"]');
                    const $issuedDateInput = $block.find('input[name$="[issued_date]"]');
                    const $endDateInput = $block.find('input[name$="[end_date]"]');
                    const $fileInput = $block.find('input[type="file"]');
                    const $exceptionCheckbox = $block.find('input[name$="[exception]"]');
                    const $exceptionTextarea = $block.find('textarea[name$="[exception_reason]"]');

                    const title = $titleInput.val()?.trim();
                    const issuedBy = $issuedByInput.val()?.trim();
                    const issuedDate = $issuedDateInput.val();
                    const endDate = $endDateInput.val();
                    const hasFile = $fileInput[0]?.files?.length > 0;
                    const hasOldImage = $block.find('input[name$="[old_image]"]').length > 0;
                    const isExceptionChecked = $exceptionCheckbox.is(':checked');
                    const exceptionReason = $exceptionTextarea.val()?.trim();

                    // Required field validation
                    if (!title) {
                        $titleInput.after(
                            '<label class="error text-danger">Title is required</label>');
                        hasErrors = true;
                    }

                    if (!issuedBy) {
                        $issuedByInput.after(
                            '<label class="error text-danger">Issued by is required</label>');
                        hasErrors = true;
                    }

                    if (!issuedDate) {
                        $issuedDateInput.after(
                            '<label class="error text-danger">Issued date is required</label>');
                        hasErrors = true;
                    }

                    if (!endDate) {
                        $endDateInput.after(
                            '<label class="error text-danger">End date is required</label>');
                        hasErrors = true;
                    }

                    // Date validation
                    if (issuedDate && endDate && new Date(endDate) <= new Date(issuedDate)) {
                        $endDateInput.after(
                            '<label class="error text-danger">End date must be after issued date</label>'
                        );
                        hasErrors = true;
                    }

                    // File upload or exception validation
                    if (!hasFile && !hasOldImage && !isExceptionChecked) {
                        const $uploadSection = $block.find('.form-border');
                        if ($uploadSection.length && !$uploadSection.next('.error').length) {
                            $uploadSection.after(
                                '<label class="error text-danger">Either upload a certificate image or check certificate exception</label>'
                            );
                        }
                        hasErrors = true;
                    }

                    // Exception reason validation
                    if (isExceptionChecked && !exceptionReason) {
                        $exceptionTextarea.after(
                            '<label class="error text-danger">Exception reason is required</label>'
                        );
                        hasErrors = true;
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    return false;
                }
            });

            // Real-time validation for individual fields
            $(document).on('input', 'input[name$="[title]"], input[name$="[issued_by]"]', function() {
                if ($(this).val().trim()) {
                    $(this).next('.error').remove();
                }
            });

            // Real-time validation for date fields
            $(document).on('change', 'input[name$="[issued_date]"], input[name$="[end_date]"]', function() {
                if ($(this).val()) {
                    $(this).next('.error').remove();
                }
            });

            // Real-time validation for file upload
            $(document).on('change', 'input[type="file"]', function() {
                if (this.files.length > 0) {
                    const $block = $(this).closest('.file-upload-group, .gray-card');
                    $block.find('.form-border').next('.error').remove();
                }
            });

            // Real-time validation for exception checkbox
            $(document).on('change', 'input[name$="[exception]"]', function() {
                const $block = $(this).closest('.file-upload-group, .gray-card');
                const $reasonTextarea = $block.find('textarea[name$="[exception_reason]"]');

                // Clear file upload error when exception is checked
                if ($(this).is(':checked')) {
                    $block.find('.form-border').next('.error').remove();
                } else {
                    $reasonTextarea.next('.error').remove();
                }
            });

            // Real-time validation for exception reason
            $(document).on('input', 'textarea[name$="[exception_reason]"]', function() {
                if ($(this).val().trim()) {
                    $(this).next('.error').remove();
                }
            });

            // Clear errors when modal opens
            $('#edit-certifications\\&licenses-modal').on('shown.bs.modal', function() {
                $('.error').remove();

                // Initialize existing image remove buttons
                $('.preview-container .existing-image .remove-image').off('click').on('click', function() {
                    const $group = $(this).closest('.gray-card');
                    const inputName = $group.find('input[name*="[old_image]"]').attr('name');
                    if (inputName) {
                        const indexMatch = inputName.match(/certificates\[(\d+)\]/);
                        if (indexMatch) {
                            removeExistingImage(this, indexMatch[1]);
                        }
                    }
                });

                // Initialize exception textarea visibility for modal content
                $('#edit-certifications\\&licenses-modal input[name*="[exception]"]').each(function() {
                    const $checkbox = $(this);
                    const $exceptionContainer = $checkbox.closest('.exception-checkbox');
                    const $textarea = $exceptionContainer.find('.exception-textarea');
                    const $uploadBox = $checkbox.closest('.gray-card').find('.upload-box');

                    if ($checkbox.is(':checked')) {
                        $textarea.show();
                        $exceptionContainer.addClass('expanded');
                        $uploadBox.addClass('disabled');
                    } else {
                        $textarea.hide();
                        $exceptionContainer.removeClass('expanded');
                        $uploadBox.removeClass('disabled');
                    }
                });
            });
        });

        // Availability Modal JavaScript
        $(document).ready(function() {
            let holidayIndex = $('.custom-holiday-div').length;

            // Initialize flatpickr for existing time inputs in modals only
            $('#edit-availability-modal .flatpickr-time').each(function() {
                if (!this._flatpickr) {
                    flatpickr(this, {
                        enableTime: true,
                        noCalendar: true,
                        dateFormat: "H:i",
                        time_24hr: true,
                        position: "below"
                    });
                }
            });

            // Function to validate availability time following the existing pattern
            function validateAvailabilityTime($endInput) {
                // Get the corresponding start input
                var endInputName = $endInput.attr('name');
                var startInputName = endInputName.replace('[end]', '[start]').replace('[end_time]', '[start_time]');
                var $startInput = $(`input[name="${startInputName}"]`);

                var startTime = $startInput.val();
                var endTime = $endInput.val();

                // Check if both times are provided
                if (!startTime || !endTime) {
                    return true; // Don't validate if either is empty
                }

                // Compare times
                if (startTime >= endTime) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Invalid Time Range',
                        text: 'End time must be after start time',
                        confirmButtonText: 'OK'
                    });
                    $endInput.focus();
                    return false;
                }
                return true;
            }

            // Real-time validation for availability end times
            $(document).on('change', 'input[name^="availability"][name$="[end]"]', function() {
                validateAvailabilityTime($(this));
            });

            // Real-time validation for holiday end times
            $(document).on('change', 'input[name^="holidays"][name$="[end_time]"]', function() {
                validateAvailabilityTime($(this));
            });

            // Real-time validation for custom holiday end times
            $(document).on('change', 'input[name^="custom_holidays"][name$="[end_time]"]', function() {
                validateAvailabilityTime($(this));
            });

            // Day checkbox toggle functionality
            $(document).on('change', '.time-picker-calendar2 input[type="checkbox"]', function() {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();

                    // Initialize flatpickr for time inputs
                    $checkedTime.find('.flatpickr-time').each(function() {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        }
                    });
                } else {
                    $checkedTime.hide();
                    $closedTime.show();

                    // Clear time values
                    $checkedTime.find('.flatpickr-time').val('');
                }
            });

            // Holiday checkbox toggle functionality
            $(document).on('change', '.day-checkbox', function() {
                const $timeContainer = $(this).closest('.time-picker-calendar').find('.start-time');

                if ($(this).is(':checked')) {
                    ;
                    $timeContainer.show();
                    $timeContainer.find('.flatpickr-time').each(function() {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true,
                                position: "below"
                            });
                        }
                    });
                } else {
                    $timeContainer.hide();
                    $timeContainer.find('.flatpickr-time').val('');
                }
            });

            // Select All holidays functionality
            $(document).on('change', '.select-all', function() {
                const isChecked = $(this).is(':checked');
                $('.day-checkbox').prop('checked', isChecked).trigger('change');
            });

            // Custom holiday modal
            $(document).on('click', '.add-custom-holiday-btn', function() {
                $('#customHolidayModal').show();
            });

            $(document).on('click', '.close', function() {
                $('#customHolidayModal').hide();
            });

            // Save custom holiday
            $(document).on('click', '#saveCustomHoliday', function() {
                const name = $('#customHolidayName').val().trim();
                const date = $('#customHolidayDate').val().trim();

                if (!name || !date) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Missing Information',
                        text: 'Both holiday name and date are required',
                        confirmButtonText: 'OK'
                    });
                    return;
                }

                if (name && date) {
                    const newHoliday = `
                            <div class="custom-holiday-div">
                                <div class="time-picker-calendar">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <label class="days">
                                            <input type="checkbox" name="holidays[${holidayIndex}][holiday_id]" value="custom_${holidayIndex}" class="day-checkbox" checked>
                                            <span class="checkmark">${name}</span>
                                            <input type="hidden" name="custom_holidays[${holidayIndex}][name]" value="${name}">
                                            <input type="hidden" name="custom_holidays[${holidayIndex}][date]" value="${date}">
                                        </label>
                                        <p>${date}</p>
                                    </div>
                                    <div class="start-time" style="display: flex;">
                                        <div class="d-flex gap-10 justify-content-start">
                                            <input type="text" class="flatpickr-time no_validate" name="custom_holidays[${holidayIndex}][start_time]" placeholder="Start Time">
                                            <p class="mb-0"> - </p>
                                            <input type="text" class="flatpickr-time no_validate" name="custom_holidays[${holidayIndex}][end_time]" placeholder="End Time">
                                        </div>
                                        <button type="button" class="delete-holiday">Delete</button>
                                    </div>
                                </div>
                            </div>
                        `;

                    $('.add-custom-holiday-btn').before(newHoliday);
                    holidayIndex++;

                    // Initialize flatpickr for new time inputs
                    $('.custom-holiday-div').last().find('.flatpickr-time').each(function() {
                        flatpickr(this, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "H:i",
                            time_24hr: true,
                            position: "below"
                        });
                    });

                    $('#customHolidayName').val('');
                    $('#customHolidayDate').val('');
                    $('#customHolidayModal').hide();
                }
            });

            // Delete custom holiday
            $(document).on('click', '.delete-holiday', function() {
                $(this).closest('.custom-holiday-div').remove();
            });

            // Initialize flatpickr for custom holiday date picker
            flatpickr('#edit-availability-modal #customHolidayDate', {
                dateFormat: "F j"
            });

            // Initialize existing state
            $('.time-picker-calendar2 input[type="checkbox"]').each(function() {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();
                } else {
                    $checkedTime.hide();
                    $closedTime.show();
                }
            });

            // Initialize existing holiday checkboxes state
            $('.day-checkbox').each(function() {
                const $timeContainer = $(this).closest('.time-picker-calendar').find('.start-time');
                if (!$(this).is(':checked')) {
                    $timeContainer.hide();
                }
            });

            // Form submission validation
            $('#edit-availability-form').on('submit', function(e) {
                let hasErrors = false;
                let errorMessages = [];

                // Check availability times
                $('.time-picker-calendar2 input[type="checkbox"]:checked').each(function() {
                    const $dayRow = $(this).closest('.time-picker-calendar2');
                    const $startInput = $dayRow.find('input[name$="[start]"]');
                    const $endInput = $dayRow.find('input[name$="[end]"]');
                    const dayName = $(this).val();

                    const startTime = $startInput.val();
                    const endTime = $endInput.val();

                    if (!startTime || !endTime) {
                        errorMessages.push(`${dayName}: Both start and end times are required`);
                        hasErrors = true;
                    } else if (startTime >= endTime) {
                        errorMessages.push(`${dayName}: End time must be after start time`);
                        hasErrors = true;
                    }
                });

                // Check holiday times (only for checked holidays with visible time inputs)
                $('.day-checkbox:checked').each(function() {
                    const $holidayRow = $(this).closest('.time-picker-calendar');
                    const $timeContainer = $holidayRow.find('.start-time');

                    if ($timeContainer.is(':visible')) {
                        const $startInput = $timeContainer.find('input[name$="[start_time]"]');
                        const $endInput = $timeContainer.find('input[name$="[end_time]"]');
                        const holidayName = $(this).siblings('.checkmark').text();

                        const startTime = $startInput.val();
                        const endTime = $endInput.val();

                        if (!startTime || !endTime) {
                            errorMessages.push(
                                `${holidayName}: Both start and end times are required`);
                            hasErrors = true;
                        } else if (startTime >= endTime) {
                            errorMessages.push(`${holidayName}: End time must be after start time`);
                            hasErrors = true;
                        }
                    }
                });

                // Check custom holiday times
                $('.custom-holiday-div .day-checkbox:checked').each(function() {
                    const $customHolidayRow = $(this).closest('.custom-holiday-div');
                    const $timeContainer = $customHolidayRow.find('.start-time');

                    if ($timeContainer.is(':visible')) {
                        const $startInput = $timeContainer.find('input[name$="[start_time]"]');
                        const $endInput = $timeContainer.find('input[name$="[end_time]"]');
                        const holidayName = $(this).siblings('.checkmark').text();

                        const startTime = $startInput.val();
                        const endTime = $endInput.val();

                        if (!startTime || !endTime) {
                            errorMessages.push(
                                `${holidayName}: Both start and end times are required`);
                            hasErrors = true;
                        } else if (startTime >= endTime) {
                            errorMessages.push(`${holidayName}: End time must be after start time`);
                            hasErrors = true;
                        }
                    }
                });

                if (hasErrors) {
                    e.preventDefault();
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        html: errorMessages.join('<br>'),
                        confirmButtonText: 'OK'
                    });
                    return false;
                }
            });
        });

        // Intro Cards Modal JavaScript
        $(document).ready(function() {
            let cardIndex = <?php echo e(auth()->user()->introCards ? auth()->user()->introCards->count() : 1); ?>;

            // Add More Intro Card functionality
            $('#add-more-intro-card').on('click', function() {
                const newCardHtml = `
                        <div class="gray-card my-5 intro-card-block" data-index="${cardIndex}">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="mb-0">Card #${cardIndex + 1}</h4>
                                <button type="button" class="btn btn-sm btn-danger delete-intro-card">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                            <div class="row row-gap-4">
                                <div class="col-md-12">
                                    <label for="intro-image-${cardIndex}" class="form-label form-input-labels">Image</label>
                                    <div class="position-relative form-add-services">
                                        <div class="image-input image-input-empty image-input-circle"
                                            data-kt-image-input="true"
                                            style="background-image: url('<?php echo e(asset('website/assets/images/image_input_holder.png')); ?>')">
                                            <div class="image-input-wrapper w-125px h-125px"></div>
                                            <label class="image-button btn btn-icon ms-6 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px shadow"
                                                data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                                data-bs-dismiss="click" title="Change image">
                                                <i class="ki-duotone ki-pencil fs-6">
                                                    <span class="path1"></span>
                                                    <span class="path2"></span>
                                                </i>
                                                <input type="file" name="introCards[${cardIndex}][image]" accept=".png, .jpg, .jpeg" />
                                                <input type="hidden" name="avatar_remove" />
                                            </label>
                                            <span class="btn btn-icon ms-4 btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                                data-bs-dismiss="click" title="Cancel">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                            <span class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                                data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                                data-bs-dismiss="click" title="Remove">
                                                <i class="ki-outline ki-cross fs-3"></i>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label for="intro-heading-${cardIndex}" class="form-label form-input-labels">Heading</label>
                                    <input type="text" class="form-control form-inputs-field"
                                        placeholder="Enter Heading" id="intro-heading-${cardIndex}"
                                        name="introCards[${cardIndex}][heading]">
                                </div>
                                <div class="col-md-12">
                                    <label for="intro-description-${cardIndex}" class="form-label form-input-labels">Description</label>
                                    <textarea class="form-control form-inputs-field" rows="3"
                                        placeholder="Enter description" id="intro-description-${cardIndex}"
                                        name="introCards[${cardIndex}][description]"></textarea>
                                </div>
                            </div>
                        </div>
                    `;

                $('#intro-cards-wrapper').append(newCardHtml);

                // Initialize image input for the new card
                if (typeof KTImageInput !== 'undefined') {
                    KTImageInput.createInstances();
                }

                cardIndex++;
                updateIntroCardNumbers();

                // Note: Validation automatically works for new cards due to event delegation
                // The validation uses $(document).on() which handles dynamically added elements
            });

            // Delete Intro Card functionality using event delegation
            $(document).on('click', '.delete-intro-card', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Check if there's more than one card before deleting
                const totalCards = $('.intro-card-block').length;

                // if (totalCards <= 1) {
                //     alert('You must have at least one intro card.');
                //     return;
                // }

                // Remove the card
                $(this).closest('.intro-card-block').remove();

                // Update card numbers and delete buttons
                updateIntroCardNumbers();
            });

            // Update card numbers after deletion
            function updateIntroCardNumbers() {
                $('.intro-card-block').each(function(index) {
                    const $card = $(this);

                    // Update card title
                    $card.find('h4').text(`Card #${index + 1}`);
                    $card.attr('data-index', index);

                    // Update form field names and IDs
                    $card.find('input, textarea').each(function() {
                        const $input = $(this);

                        // Update name attribute
                        if ($input.attr('name') && $input.attr('name').includes('introCards[')) {
                            const newName = $input.attr('name').replace(/introCards\[\d+\]/,
                                `introCards[${index}]`);
                            $input.attr('name', newName);
                        }

                        // Update id attribute
                        if ($input.attr('id') && $input.attr('id').includes('intro-')) {
                            const newId = $input.attr('id').replace(/-\d+$/, `-${index}`);
                            $input.attr('id', newId);
                        }
                    });

                    // Update labels
                    $card.find('label[for]').each(function() {
                        const $label = $(this);
                        if ($label.attr('for') && $label.attr('for').includes('intro-')) {
                            const newFor = $label.attr('for').replace(/-\d+$/, `-${index}`);
                            $label.attr('for', newFor);
                        }
                    });
                });
            }
        });
    </script>
    <script>
        // Initialize Google Maps for Individual Service
        function initIndividualServiceMap() {
            // Only use existing coordinates if we have them in the form fields (from old input or editing)
            const existingLat = document.getElementById('latitude').value;
            const existingLng = document.getElementById('longitude').value;
            const existingLocation = document.getElementById('pac-input').value;

            const userLat = existingLat && existingLat !== '' ? parseFloat(existingLat) : null;
            const userLng = existingLng && existingLng !== '' ? parseFloat(existingLng) : null;
            const userLocation = existingLocation || '';



            let map, marker;

            // Only initialize map if we have existing coordinates
            if (userLat !== null && userLng !== null) {

                // Initialize map with existing coordinates
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: {
                        lat: userLat,
                        lng: userLng
                    }
                });

                // Create marker at existing location
                marker = new google.maps.Marker({
                    position: {
                        lat: userLat,
                        lng: userLng
                    },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Your Business Location'
                });

                // Set the form values
                document.getElementById('latitude').value = userLat;
                document.getElementById('longitude').value = userLng;
            } else {

                // Show placeholder instead of map
                const mapElement = document.getElementById('map');
                mapElement.innerHTML =
                    '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px;"></i><br>Map will appear after you search for a location</div></div>';
            }

            // Initialize autocomplete
            const input = document.getElementById('pac-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            // Only bind to map bounds if map exists
            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function() {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    return;
                }

                // Initialize map if it doesn't exist yet
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    // Add drag listener for new marker
                    marker.addListener('dragend', function() {
                        const position = marker.getPosition();
                        document.getElementById('latitude').value = position.lat();
                        document.getElementById('longitude').value = position.lng();

                        // Reverse geocoding to update address
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({
                            location: position
                        }, function(results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('pac-input').value = results[0]
                                    .formatted_address;
                            }
                        });
                    });
                } else {
                    // Update existing map and marker
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                // Update hidden inputs
                document.getElementById('latitude').value = place.geometry.location.lat();
                document.getElementById('longitude').value = place.geometry.location.lng();
            });

            // Handle marker drag (only if marker exists)
            if (marker) {
                marker.addListener('dragend', function() {
                    const position = marker.getPosition();
                    document.getElementById('latitude').value = position.lat();
                    document.getElementById('longitude').value = position.lng();

                    // Reverse geocoding to update address
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: position
                    }, function(results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('pac-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize when Google Maps is ready
        $(document).ready(function() {
            // Wait for Google Maps to load, then initialize
            function waitForGoogleMaps() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    initIndividualServiceMap();
                } else {
                    setTimeout(waitForGoogleMaps, 100);
                }
            }
            waitForGoogleMaps();
        });

        // Media validation is now handled dynamically in the modal

        // Deactivate account confirmation
        function confirmDeactivation() {
            Swal.fire({
                title: 'Are you sure?',
                text: 'Do you want to delete your account? This action will log you out and delete your profile permanently after 30 days of inactivity contact admin before that to reactivate your account.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Yes, delete it!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = "<?php echo e(route('deactivate.account')); ?>";
                }
            });
        }

        // Global function to handle issued date changes for certificates
        window.handleIssuedDateChange = function(issuedDateInput) {
            const index = $(issuedDateInput).data('index');
            const endDateInput = $(`input[name="certificates[${index}][end_date]"]`);
            const issuedDate = $(issuedDateInput).val();

            if (issuedDate) {
                // Enable end date field
                endDateInput.prop('disabled', false);
                endDateInput.css({
                    'background-color': '',
                    'color': ''
                });

                // Set minimum date for end date to be the issued date
                endDateInput.attr('min', issuedDate);

                // If end date is already set and is before issued date, clear it
                const currentEndDate = endDateInput.val();
                if (currentEndDate && currentEndDate < issuedDate) {
                    endDateInput.val('');
                }
            } else {
                // Disable end date field if no issued date is set
                endDateInput.prop('disabled', true);
                endDateInput.css({
                    'background-color': '#f8f9fa',
                    'color': '#6c757d'
                });
                endDateInput.val('');
                endDateInput.removeAttr('min');
            }
        };

        // Handle issued date changes for all certificates
        $(document).on('change', '.issued-date', function() {
            handleIssuedDateChange(this);
        });

        // Initialize existing certificates on page load
        $(document).ready(function() {
            $('.issued-date').each(function() {
                handleIssuedDateChange(this);
            });
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make(auth()->check() && auth()->user()->hasRole('customer') ? 'website.layout.master' : 'dashboard.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\git-file\anders\resources\views/dashboard/profile_settings/index.blade.php ENDPATH**/ ?>