<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class RefundRequestsExport implements FromCollection, WithHeadings, WithMapping, WithStyles
{
    protected $refunds;

    public function __construct($refunds)
    {
        $this->refunds = $refunds;
    }

    public function collection()
    {
        return $this->refunds;
    }

    public function headings(): array
    {
        return [
            'Booking ID',
            'Customer Name',
            'Email Address',
            'Service Name',
            'Amount',
            'Request Date',
            'Reason',
            'Status'
        ];
    }

    public function map($refund): array
    {
        return [
            $refund->booking_number ?? 'N/A',
            $refund->user->name ?? 'N/A',
            $refund->user->email ?? 'N/A',
            $refund->service->name ?? 'N/A',
            '$' . number_format($refund->total_amount ?? 0, 2),
            $refund->updated_at ? $refund->updated_at->format('d M, Y') : 'N/A',
            $refund->cancel_reason ?? 'N/A',
            $refund->status == 2 ? 'Pending' : ($refund->status == 3 ? 'Refunded' : ($refund->status == 4 ? 'Denied' : 'Unknown'))
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true]],
        ];
    }
}
