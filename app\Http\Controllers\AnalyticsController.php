<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\Subscription;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Display admin analytics dashboard
     */
    public function adminAnalytics()
    {
        $data = [
            'totalDailyRevenue' => $this->getTotalDailyRevenue(),
            'revenueByService' => $this->getRevenueByService(),
            'netRevenue' => $this->getNetRevenue(),
            'totalBookings' => $this->getTotalBookings(),
            'userAnalytics' => $this->getUserAnalytics(),
            'subscriptionAnalytics' => $this->getSubscriptionAnalytics(),
        ];

        return view('dashboard.admin.analytics', $data);
    }

    /**
     * Get total daily revenue for today
     */
    private function getTotalDailyRevenue()
    {
        $today = Carbon::today();

        return Booking::whereDate('created_at', $today)
            ->whereIn('status', [1, 0])
            ->sum('total_amount');
    }

    /**
     * Get revenue breakdown by service
     */
    private function getRevenueByService()
    {
        $completedBookings = Booking::with('service')
            ->where('status', 1)
            ->get();

        $allBookings = Booking::with('service')->get();

        $revenueByService = [];
        $customerBookingsByService = [];

        // Track customer bookings per service for rebooking rate
        foreach ($allBookings as $booking) {
            $serviceName = $booking->service->name ?? 'Unknown Service';
            $customerId = $booking->user_id;

            if (!isset($customerBookingsByService[$serviceName])) {
                $customerBookingsByService[$serviceName] = [];
            }

            if (!isset($customerBookingsByService[$serviceName][$customerId])) {
                $customerBookingsByService[$serviceName][$customerId] = 0;
            }

            $customerBookingsByService[$serviceName][$customerId]++;
        }

        foreach ($completedBookings as $booking) {
            $serviceName = $booking->service->name ?? 'Unknown Service';

            if (!isset($revenueByService[$serviceName])) {
                $revenueByService[$serviceName] = [
                    'service_name' => $serviceName,
                    'total_revenue' => 0,
                    'booking_count' => 0,
                    'service_duration' => $booking->service->duration ?? 'N/A'
                ];
            }

            $revenueByService[$serviceName]['total_revenue'] += $booking->total_amount;
            $revenueByService[$serviceName]['booking_count']++;
        }

        // Calculate metrics for each service
        foreach ($revenueByService as $serviceName => &$service) {
            // Average ticket value
            $service['avg_ticket_value'] = $service['booking_count'] > 0
                ? $service['total_revenue'] / $service['booking_count']
                : 0;

            // Rebooking rate calculation
            $totalCustomers = count($customerBookingsByService[$serviceName] ?? []);
            $rebookingCustomers = 0;

            if (isset($customerBookingsByService[$serviceName])) {
                foreach ($customerBookingsByService[$serviceName] as $bookingCount) {
                    if ($bookingCount > 1) {
                        $rebookingCustomers++;
                    }
                }
            }

            $service['rebooking_rate'] = $totalCustomers > 0
                ? ($rebookingCustomers / $totalCustomers) * 100
                : 0;
        }

        // Sort by revenue descending
        usort($revenueByService, function($a, $b) {
            return $b['total_revenue'] <=> $a['total_revenue'];
        });

        return collect($revenueByService);
    }

    /**
     * Calculate net revenue
     */
    private function getNetRevenue()
    {
        $totalRevenue = Booking::where('status', 1)->sum('total_amount');
        $totalVat = Booking::where('status', 1)->sum('vat_amount');

        return [
            'total_revenue' => $totalRevenue,
            'total_vat' => $totalVat,
            'net_revenue' => $totalRevenue
        ];
    }

    /**
     * Get total bookings count (all statuses)
     */
    private function getTotalBookings()
    {
        return Booking::count();
    }

    /**
     * Get user analytics data
     */
    private function getUserAnalytics()
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        // Total users (all registered users)
        $totalUsers = User::count();

        // Active users (users with status = 1)
        $activeUsers = User::where('status', 1)->count();

        // Inactive users (users with status != 1)
        $inactiveUsers = User::where('status', '!=', 1)->count();

        // New signups in last 30 days
        $newSignups = User::where('created_at', '>=', $thirtyDaysAgo)->count();

        // Users who became inactive in last 30 days (churn)
        $churnedUsers = User::where('status', '!=', 1)
            ->where('updated_at', '>=', $thirtyDaysAgo)
            ->count();

        // Calculate churn rate (churned users / total active users at start of period)
        $activeUsersAtStartOfPeriod = $activeUsers + $churnedUsers;
        $churnRate = $activeUsersAtStartOfPeriod > 0
            ? ($churnedUsers / $activeUsersAtStartOfPeriod) * 100
            : 0;

        // Net growth (new signups - churned users)
        $netGrowth = $newSignups - $churnedUsers;

        return [
            'total_users' => $totalUsers,
            'active_users' => $activeUsers,
            'inactive_users' => $inactiveUsers,
            'new_signups' => $newSignups,
            'churned_users' => $churnedUsers,
            'churn_rate' => $churnRate,
            'net_growth' => $netGrowth,
        ];
    }

    /**
     * Get subscription analytics data
     */
    private function getSubscriptionAnalytics()
    {
        $thirtyDaysAgo = Carbon::now()->subDays(30);

        // Get all subscription plans
        $subscriptionPlans = Subscription::all();
        $planAnalytics = [];

        foreach ($subscriptionPlans as $plan) {
            // Users per plan (active subscriptions)
            $usersCount = UserSubscription::where('subscription_id', $plan->id)
                ->where('user_subscriptions.status', 1)
                ->count();

            // Revenue per plan (monthly recurring revenue)
            $monthlyRevenue = $usersCount * $plan->price;

            // New subscriptions (created in last 30 days)
            $newSubscriptions = UserSubscription::where('subscription_id', $plan->id)
                ->where('user_subscriptions.created_at', '>=', $thirtyDaysAgo)
                ->count();

            // Plan changes (subscriptions updated but not newly created in last 30 days)
            $planChanges = UserSubscription::where('subscription_id', $plan->id)
                ->where('user_subscriptions.updated_at', '>=', $thirtyDaysAgo)
                ->where('user_subscriptions.created_at', '<', $thirtyDaysAgo)
                ->where('user_subscriptions.status', 1) // Only active subscriptions
                ->count();

            // Cancellations (subscriptions that became inactive in last 30 days)
            $cancellations = UserSubscription::where('subscription_id', $plan->id)
                ->where('user_subscriptions.status', 0)
                ->where('user_subscriptions.updated_at', '>=', $thirtyDaysAgo)
                ->count();

            $planAnalytics[] = [
                'plan_name' => $plan->name,
                'plan_type' => $plan->type,
                'plan_price' => $plan->price,
                'users_count' => $usersCount,
                'monthly_revenue' => $monthlyRevenue,
                'new_subscriptions' => $newSubscriptions,
                'plan_changes' => $planChanges,
                'cancellations' => $cancellations,
            ];
        }

        // Overall subscription metrics
        $totalActiveSubscriptions = UserSubscription::where('user_subscriptions.status', 1)->count();
        $totalMonthlyRevenue = UserSubscription::where('user_subscriptions.status', 1)
            ->join('subscriptions', 'user_subscriptions.subscription_id', '=', 'subscriptions.id')
            ->sum('subscriptions.price');

        $totalNewSubscriptions = UserSubscription::where('user_subscriptions.created_at', '>=', $thirtyDaysAgo)->count();
        $totalPlanChanges = UserSubscription::where('user_subscriptions.updated_at', '>=', $thirtyDaysAgo)
            ->where('user_subscriptions.created_at', '<', $thirtyDaysAgo)
            ->where('user_subscriptions.status', 1)
            ->count();
        $totalCancellations = UserSubscription::where('user_subscriptions.status', 0)
            ->where('user_subscriptions.updated_at', '>=', $thirtyDaysAgo)
            ->count();

        return [
            'plan_analytics' => collect($planAnalytics),
            'total_active_subscriptions' => $totalActiveSubscriptions,
            'total_monthly_revenue' => $totalMonthlyRevenue,
            'total_new_subscriptions' => $totalNewSubscriptions,
            'total_plan_changes' => $totalPlanChanges,
            'total_cancellations' => $totalCancellations,
        ];
    }
}
