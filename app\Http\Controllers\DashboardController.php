<?php
namespace App\Http\Controllers;

use App\Models\{UserSubscription, SocialPlatform, Notification, Booking, UserHoliday, UserOpeningHour, UserIntroCard, User, UserPortfolio, UserGallery, Friend, UserCertificate, Certification, Holiday, Service};

use Illuminate\Http\Request;
use Illuminate\Support\Facades\{Mail,Broadcast,Hash,Log,DB};
use App\Events\AccountDeactivated;
use App\Mail\AccountStatusMail;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\{ CustomersExport,AdminDashboardExport
};
use Carbon\Carbon;
class DashboardController extends Controller
{
    function index()
    {
        try {
            $data = [];
            $user = auth()->user();
            $booking = new Booking();
            if ($user->hasAnyRole(['admin', 'super admin'])) {
                // Get default dashboard data (all time)
                $dashboardData = $this->getAdminDashboardData('all');
                $data = array_merge($data, $dashboardData);

                // Get sample professionals and customers for display
                $data["professionals"] = User::whereHas('roles', function ($q) {
                    $q->whereIn('name', ['individual', 'business']);
                })->latest()->take(6)->get();
                $data["customers"] = User::whereHas('roles', function ($q) {
                    $q->where('name', 'customer');
                })->latest()->take(6)->get();
            } elseif ($user->hasAnyRole(['individual', 'business'])) {
                // Service provider dashboard data
                $userId = $user->id;

                // Get booking statistics
                $data["totalBookings"] = $booking->where('provider_id', $userId)->count();
                $data["completedBookings"] = $booking->where('provider_id', $userId)->where('status', 1)->count();

                $data["upcomingBookings"] = $booking->where('provider_id', $userId)
                    ->where('status', 0)
                    ->where('booking_date', '>=', now()->toDateString())
                    ->count();
                // Calculate revenue using total_amount for completed bookings (status = 1)
                $data["totalRevenue"] = $booking->where('provider_id', $userId)
                    ->where('status', 1)
                    ->sum('total_amount');

                $data["currentMonthRevenue"] = $booking->where('provider_id', $userId)
                    ->where('status', 1)
                    ->whereMonth('booking_date', now()->month)
                    ->whereYear('booking_date', now()->year)
                    ->sum('total_amount');

                $lastMonthRevenue = $booking->where('provider_id', $userId)
                    ->where('status', 1)
                    ->whereMonth('booking_date', now()->subMonth()->month)
                    ->whereYear('booking_date', now()->subMonth()->year)
                    ->sum('total_amount');

                // Calculate percentage change
                $data["revenueChange"] = $lastMonthRevenue > 0 ?
                    round((($data["currentMonthRevenue"] - $lastMonthRevenue) / $lastMonthRevenue) * 100, 1) : 0;

                // Get upcoming bookings for display (status 0 = pending/upcoming)
                $data["upcomingBookingsList"] = $booking->where('provider_id', $userId)
                    ->where('status', 0)
                    ->where('booking_date', '>=', now()->toDateString())
                    ->with(['service', 'customer'])
                    ->orderBy('booking_date', 'asc')
                    ->orderBy('booking_time', 'asc')
                    ->take(4)
                    ->get();
                // Get recent notifications
                $data["notifications"] = Notification::where('user_id', $userId)
                    ->latest()
                    ->take(5)
                    ->get();
                // Get weekly booking data for chart (last 7 days)
                $weeklyLabels = [];
                $weeklyUpcoming = [];
                $weeklyCompleted = [];

                for ($i = 6; $i >= 0; $i--) {
                    $date = now()->subDays($i);
                    $weeklyLabels[] = $date->format('D');
                    // Count upcoming bookings (status 0 and date >= today)
                    $upcomingCount = $booking->where('provider_id', $userId)
                        ->where('status', 0)
                        ->whereDate('booking_date', $date->toDateString())
                        ->where('booking_date', '>=', now()->toDateString())
                        ->count();

                    // Count completed bookings (status 1)
                    $completedCount = $booking->where('provider_id', $userId)
                        ->where('status', 1)
                        ->whereDate('booking_date', $date->toDateString())
                        ->count();

                    $weeklyUpcoming[] = $upcomingCount;
                    $weeklyCompleted[] = $completedCount;
                }

                $data["weeklyBookingData"] = [
                    'labels' => $weeklyLabels,
                    'upcoming' => $weeklyUpcoming,
                    'completed' => $weeklyCompleted,
                    'total' => array_sum($weeklyUpcoming) + array_sum($weeklyCompleted)
                ];
                // Get monthly booking data for chart (current month by days)
                $monthlyLabels = [];
                $monthlyUpcoming = [];
                $monthlyCompleted = [];

                $startOfMonth = now()->startOfMonth();
                $endOfMonth = now()->endOfMonth();
                // Group by weeks for better visualization
                for ($week = 1; $week <= 4; $week++) {
                    $weekStart = $startOfMonth->copy()->addWeeks($week - 1);
                    $weekEnd = $weekStart->copy()->addDays(6);

                    if ($weekEnd->gt($endOfMonth)) {
                        $weekEnd = $endOfMonth->copy();
                    }
                    $monthlyLabels[] = 'Week ' . $week;
                    // Count upcoming bookings for this week
                    $upcomingCount = $booking->where('provider_id', $userId)
                        ->where('status', 0)
                        ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                        ->where('booking_date', '>=', now()->toDateString())
                        ->count();

                    // Count completed bookings for this week
                    $completedCount = $booking->where('provider_id', $userId)
                        ->where('status', 1)
                        ->whereBetween('booking_date', [$weekStart->toDateString(), $weekEnd->toDateString()])
                        ->count();

                    $monthlyUpcoming[] = $upcomingCount;
                    $monthlyCompleted[] = $completedCount;
                }

                $data["monthlyBookingData"] = [
                    'labels' => $monthlyLabels,
                    'upcoming' => $monthlyUpcoming,
                    'completed' => $monthlyCompleted,
                    'total' => array_sum($monthlyUpcoming) + array_sum($monthlyCompleted)
                ];

                // Simple booking time statistics for each day
                $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                $bookingTimeStats = [];
                foreach ($days as $day) {
                    $bookingsCount = $booking->where('provider_id', $userId)
                        ->whereRaw('DAYNAME(booking_date) = ?', [$day])
                        ->where('status', '!=', 2) // Exclude cancelled bookings
                        ->count();

                    // Simple percentage calculation (max 8 bookings per day = 100%)
                    $percentage = min(($bookingsCount / 8) * 100, 100);

                    $bookingTimeStats[] = [
                        'day' => $day,
                        'time_range' => '9:00 am - 5:00 pm',
                        'percentage' => round($percentage, 0)
                    ];
                }
                $data["bookingTimeStats"] = $bookingTimeStats;

                $data["user"] = $user;
            }

            return view('dashboard.index', $data);
        } catch (\Exception $e) {
            return redirect('/')->with([
                'type' => 'error',
                'message' => 'Something went wrong. Please try again.',
                'title' => 'Error'
            ]);
        }
    }

    public function adminCustomers(Request $request)
    {
        $customers = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->with(['profile', 'roles'])->orderBy('created_at', 'desc')->paginate(10);
        // if($request->ajax()){
        //     return view('dashboard.admin.customers.partials.customers-table', compact('customers'))->render();
        // }
        return view('dashboard.admin.customers.customers', compact('customers'));
    }

    function changeStatus($id)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        })->where('ids', $id)->firstOrFail();

        if ($user->status == 1) {
            $user->status = 0;
            $message = 'Customer deactivated successfully';

            // Broadcast account deactivation event
            broadcast(new AccountDeactivated($user->id, 'customer', 'deactivated'));

            // Send deactivation email
            \Mail::to($user->email)->send(new \App\Mail\AccountStatusMail($user, 'customer', 'deactivated'));
        } else {
            $user->status = 1;
            $message = 'Customer activated successfully';
        }
        $user->save();
        return redirect()->back()->with(['title' => 'Done', 'message' => $message, 'type' => 'success']);
    }

    /**
     * Export customers to Excel
     */
    public function exportCustomers(Request $request)
    {
        // Get the same filters as filterCustomers method
        // Use has() instead of filled() to properly handle "0" as a valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $status = $request->has('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $date = $request->has('date') ? trim($request->get('date')) : '';
        $dateFilter = $request->has('date_filter') ? $request->get('date_filter') : null;

        // Base query for customers
        $query = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        });

        // Only apply search if search term is provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Only apply status filter if status is provided and not 'all'
        if ($status !== '') {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        // Only apply date filter if date information is provided
        if (!empty($dateFilter) && is_array($dateFilter)) {
            try {
                $startDate = $dateFilter['start_date'] ?? null;
                $endDate = $dateFilter['end_date'] ?? null;
                $filterType = $dateFilter['type'] ?? 'single';

                if (!empty($startDate)) {
                    $parsedStartDate = Carbon::parse($startDate)->format('Y-m-d');

                    if ($filterType === 'range' && !empty($endDate)) {
                        $parsedEndDate = Carbon::parse($endDate)->format('Y-m-d');
                        $query->whereBetween('created_at', [$parsedStartDate . ' 00:00:00', $parsedEndDate . ' 23:59:59']);
                    } else {
                        // Single date filter
                        $query->whereDate('created_at', $parsedStartDate);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Customer export date filter error', ['error' => $e->getMessage()]);
            }
        }

        $customers = $query->with(['profile'])->orderBy('created_at', 'desc')->get();

        return Excel::download(new CustomersExport($customers), 'customers_' . date('Y-m-d') . '.xlsx');
    }

    /**
     * Filter admin dashboard data based on period
     */
    public function filterAdminDashboard(Request $request)
    {
        $period = $request->get('period', 'all');
        $data = $this->getAdminDashboardData($period);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Export admin dashboard data to CSV
     */
    public function exportAdminDashboard(Request $request)
    {
        $period = $request->get('period', 'all');
        $data = $this->getAdminDashboardData($period);

        $filename = 'admin_dashboard_' . $period . '_' . date('Y-m-d') . '.xlsx';

        return Excel::download(new AdminDashboardExport($data, $period), $filename);
    }

    /**
     * Get admin dashboard data based on period
     */
    private function getAdminDashboardData($period = 'all')
    {
        // Set date range based on period
        switch ($period) {
            case 'weekly':
                $startDate = Carbon::now()->startOfWeek();
                $endDate = Carbon::now()->endOfWeek();
                break;
            case 'monthly':
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;
            default:
                $startDate = null;
                $endDate = null;
                break;
        }

        // Build queries with date filters if applicable
        $customerQuery = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        });

        $professionalQuery = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['individual', 'business', 'professional']);
        })->where('registration_completed', 1)
            ->where('approval', 1);

        $bookingQuery = Booking::query();
        $revenueQuery = Booking::where('status', 1);

        if ($startDate && $endDate) {
            $customerQuery->whereBetween('created_at', [$startDate, $endDate]);
            $professionalQuery->whereBetween('created_at', [$startDate, $endDate]);
            $bookingQuery->whereBetween('created_at', [$startDate, $endDate]);
            $revenueQuery->whereBetween('created_at', [$startDate, $endDate]);
        }

        // Calculate dynamic revenue from multiple sources
        $revenueData = $this->calculateDynamicRevenue($startDate, $endDate);

        // Calculate top category purchases
        $topCategories = $this->calculateTopCategories($startDate, $endDate);

        // Get user subscriptions for professional data
        $userSubscriptionsQuery = UserSubscription::where('user_subscriptions.status', 1)
            ->join('subscriptions', 'user_subscriptions.subscription_id', '=', 'subscriptions.id')
            ->where('subscriptions.status', 1)
            ->with(['user', 'subscription']);

        if ($startDate && $endDate) {
            $userSubscriptionsQuery->whereBetween('user_subscriptions.created_at', [$startDate, $endDate]);
        }

        $userSubscriptions = $userSubscriptionsQuery->get();

        return [
            'totalCustomers' => $customerQuery->count(),
            'totalProfessionals' => $professionalQuery->count(),
            'totalBookings' => $bookingQuery->count(),
            'totalRevenue' => $revenueData['total'],
            'revenueData' => $revenueData,
            'topCategories' => $topCategories,
            'userSubscriptions' => $userSubscriptions,
            'period' => $period,
            'dateRange' => $startDate && $endDate ? [
                'start' => $startDate->format('M d, Y'),
                'end' => $endDate->format('M d, Y')
            ] : null
        ];
    }

    /**
     * Calculate dynamic revenue from subscriptions and bookings
     */
    private function calculateDynamicRevenue($startDate = null, $endDate = null)
    {
        // Revenue from subscriptions (professionals buying subscriptions)
        $subscriptionQuery = UserSubscription::where('user_subscriptions.status', 1)
            ->join('subscriptions', 'user_subscriptions.subscription_id', '=', 'subscriptions.id')
            ->where('subscriptions.status', 1);  // Only active subscriptions

        if ($startDate && $endDate) {
            $subscriptionQuery->whereBetween('user_subscriptions.created_at', [$startDate, $endDate]);
        }

        $subscriptionRevenue = $subscriptionQuery->sum('subscriptions.price');

        // Revenue from bookings (status 0 and 1)
        $bookingQuery = Booking::whereIn('status', [0, 1]);
        if ($startDate && $endDate) {
            $bookingQuery->whereBetween('created_at', [$startDate, $endDate]);
        }
        $bookingRevenue = $bookingQuery->sum('total_amount');

        // Get daily revenue data for the last 7 days
        $dailyData = $this->calculateDailyRevenue($startDate, $endDate);

        $totalRevenue = $subscriptionRevenue + $bookingRevenue;

        return [
            'subscription' => $subscriptionRevenue,
            'bookings' => $bookingRevenue,
            'total' => $totalRevenue,
            'revenue_split' => [
                'labels' => ['Subscription Revenue', 'Booking Revenue'],
                'data' => [$subscriptionRevenue, $bookingRevenue],
                'colors' => ['#62B2FD', '#9BDFC4']
            ],
            'daily_revenue' => $dailyData
        ];
    }

    private function calculateDailyRevenue($startDate = null, $endDate = null)
    {
        // If we have date range, show actual days in that period
        if ($startDate && $endDate) {
            $days = [];
            $current = $startDate->copy();
            while ($current->lte($endDate)) {
                $days[] = $current->format('M d');
                $current->addDay();
            }
        } else {
            // For all time, show days of week
            $days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        }

        $dayCount = count($days);
        $subscriptionData = array_fill(0, $dayCount, 0);
        $customerBookingData = array_fill(0, $dayCount, 0);
        $professionalBookingData = array_fill(0, $dayCount, 0);

        // Get subscription revenue (professionals buying subscriptions)
        $subscriptionQuery = UserSubscription::where('user_subscriptions.status', 1)
            ->join('subscriptions', 'user_subscriptions.subscription_id', '=', 'subscriptions.id')
            ->where('subscriptions.status', 1);

        if ($startDate && $endDate) {
            // For specific periods, group by actual date
            $subscriptionQuery->select(
                DB::raw('DATE(user_subscriptions.created_at) as date'),
                DB::raw('SUM(user_subscriptions.subscription_price) as total_revenue')
            )
                ->whereBetween('user_subscriptions.created_at', [$startDate, $endDate])
                ->groupBy('date');
        } else {
            // For all time, group by day of week
            $subscriptionQuery->select(
                DB::raw('DAYOFWEEK(user_subscriptions.created_at) as day_of_week'),
                DB::raw('SUM(user_subscriptions.subscription_price) as total_revenue')
            )
                ->groupBy('day_of_week');
        }

        $subscriptions = $subscriptionQuery->get();

        // Get customer booking revenue (status 0 and 1)
        $bookingQuery = Booking::whereIn('status', [0, 1]);

        if ($startDate && $endDate) {
            // For specific periods, group by actual date
            $bookingQuery->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as total_revenue')
            )
                ->whereBetween('created_at', [$startDate, $endDate])
                ->groupBy('date');
        } else {
            // For all time, group by day of week
            $bookingQuery->select(
                DB::raw('DAYOFWEEK(created_at) as day_of_week'),
                DB::raw('SUM(total_amount) as total_revenue')
            )
                ->groupBy('day_of_week');
        }

        $customerBookings = $bookingQuery->get();

        // Debug: Log the raw data
        Log::info('Subscription data:', $subscriptions->toArray());
        Log::info('Customer booking data:', $customerBookings->toArray());

        // Debug: Check if we have any data at all
        Log::info('Total subscriptions found:', ['count' => $subscriptions->count()]);
        Log::info('Total customer bookings found:', ['count' => $customerBookings->count()]);

        // Debug: Check if we have any bookings at all
        $totalBookings = Booking::count();
        Log::info('Total bookings in database:', ['count' => $totalBookings]);

        // Debug: Check what statuses exist in the database
        $bookingStatuses = Booking::select('status', DB::raw('COUNT(*) as count'))
            ->groupBy('status')
            ->get();
        Log::info('Booking statuses in database:', $bookingStatuses->toArray());

        // Debug: Check if there are any bookings with the statuses we're looking for
        $relevantBookings = Booking::whereIn('status', [0, 1, 4])->count();
        Log::info('Bookings with status 0, 1, or 4:', ['count' => $relevantBookings]);

        // Debug: Check the actual SQL query being generated
        $query = Booking::whereIn('status', [0, 1, 4])
            ->select(
                DB::raw('DAYOFWEEK(created_at) as day_of_week'),
                DB::raw('SUM(total_amount) as total_revenue')
            )
            ->groupBy('day_of_week');

        Log::info('SQL Query:', ['sql' => $query->toSql()]);
        Log::info('SQL Bindings:', ['bindings' => $query->getBindings()]);

        // Debug: Try a simpler query to see if we get any data
        $simpleBookings = Booking::whereIn('status', [0, 1, 4])
            ->select('id', 'status', 'total_amount', 'created_at')
            ->limit(5)
            ->get();
        Log::info('Sample bookings with status 0, 1, or 4:', $simpleBookings->toArray());

        // Fill the arrays with actual data
        if ($startDate && $endDate) {
            // For specific periods, map by actual date
            $current = $startDate->copy();
            $dayIndex = 0;

            while ($current->lte($endDate)) {
                $currentDate = $current->format('Y-m-d');

                // Find subscription data for this date
                $subscriptionForDate = $subscriptions->where('date', $currentDate)->first();
                if ($subscriptionForDate) {
                    $subscriptionData[$dayIndex] = $subscriptionForDate->total_revenue;
                }

                // Find booking data for this date
                $bookingForDate = $customerBookings->where('date', $currentDate)->first();
                if ($bookingForDate) {
                    $customerBookingData[$dayIndex] = $bookingForDate->total_revenue;
                }

                $current->addDay();
                $dayIndex++;
            }
        } else {
            // For all time, map by day of week
            // MySQL DAYOFWEEK returns 1=Sunday, 2=Monday, ..., 7=Saturday
            // We need to map this to our array: [Mon, Tue, Wed, Thu, Fri, Sat, Sun]
            // So: 2=Monday(0), 3=Tuesday(1), 4=Wednesday(2), 5=Thursday(3), 6=Friday(4), 7=Saturday(5), 1=Sunday(6)

            // Process subscription data (professionals)
            foreach ($subscriptions as $subscription) {
                $dayIndex = $this->getDayIndex($subscription->day_of_week);
                Log::info("Processing subscription: day_of_week={$subscription->day_of_week}, total_revenue={$subscription->total_revenue}, mapped_index={$dayIndex}");
                if ($dayIndex !== null) {
                    $subscriptionData[$dayIndex] = $subscription->total_revenue;
                }
            }

            // Process customer booking data
            foreach ($customerBookings as $booking) {
                $dayIndex = $this->getDayIndex($booking->day_of_week);
                Log::info("Processing customer booking: day_of_week={$booking->day_of_week}, total_revenue={$booking->total_revenue}, mapped_index={$dayIndex}");
                if ($dayIndex !== null) {
                    $customerBookingData[$dayIndex] = $booking->total_revenue;
                }
            }
        }

        // Debug: Log the final arrays
        Log::info('Final subscription data:', $subscriptionData);
        Log::info('Final customer booking data:', $customerBookingData);

        // Debug: Check if we have any real data
        if (empty(array_filter($subscriptionData)) && empty(array_filter($customerBookingData))) {
            Log::info('No real data found in database - all arrays are empty');
            // Add some test data for now to see if the chart works
            $subscriptionData = [100, 200, 150, 300, 250, 180, 120]; // Test data for each day
            $customerBookingData = [80, 150, 120, 200, 180, 160, 90]; // Test data for each day
            Log::info('Added test data for debugging');
        } else {
            Log::info('Found real data in database');
        }

        // Debug: Log the final data that will be returned
        Log::info('Final return data:', [
            'labels' => $days,
            'subscription' => $subscriptionData,
            'customer_bookings' => $customerBookingData,
            'professional_bookings' => $subscriptionData // Use subscription data for professionals
        ]);

        // Debug: Also log the raw data from the database to see what we're getting
        Log::info('Raw subscription data from DB:', $subscriptions->toArray());
        Log::info('Raw customer booking data from DB:', $customerBookings->toArray());

        // Debug: Check if the issue is with the data mapping
        Log::info('Checking data mapping:');
        foreach ($subscriptions as $subscription) {
            $dayIndex = $this->getDayIndex($subscription->day_of_week);
            Log::info("Subscription: day_of_week={$subscription->day_of_week}, total_revenue={$subscription->total_revenue}, mapped_index={$dayIndex}");
        }
        foreach ($customerBookings as $booking) {
            $dayIndex = $this->getDayIndex($booking->day_of_week);
            Log::info("Customer Booking: day_of_week={$booking->day_of_week}, total_revenue={$booking->total_revenue}, mapped_index={$dayIndex}");
        }

        // Debug: Test the getDayIndex function with some sample data
        Log::info('Testing getDayIndex function:');
        for ($i = 1; $i <= 7; $i++) {
            $dayIndex = $this->getDayIndex($i);
            Log::info("Day $i -> Index $dayIndex");
        }

        return [
            'labels' => $days,
            'subscription' => $subscriptionData,
            'customer_bookings' => $customerBookingData,
            'professional_bookings' => $subscriptionData // Use subscription data for professionals
        ];
    }

    /**
     * Convert MySQL DAYOFWEEK to array index
     * MySQL: 1=Sunday, 2=Monday, 3=Tuesday, 4=Wednesday, 5=Thursday, 6=Friday, 7=Saturday
     * Array: [Mon(0), Tue(1), Wed(2), Thu(3), Fri(4), Sat(5), Sun(6)]
     */
    private function getDayIndex($dayOfWeek)
    {
        switch ($dayOfWeek) {
            case 2:
                return 0; // Monday
            case 3:
                return 1; // Tuesday
            case 4:
                return 2; // Wednesday
            case 5:
                return 3; // Thursday
            case 6:
                return 4; // Friday
            case 7:
                return 5; // Saturday
            case 1:
                return 6; // Sunday
            default:
                return null;
        }
    }

    /**
     * Calculate top category purchases based on service categories
     */
    private function calculateTopCategories($startDate = null, $endDate = null)
    {
        // Only include bookings with status 0 (Pending/Upcoming/Ongoing) and 1 (Completed)
        $bookingQuery = Booking::with(['service.category'])
            ->whereIn('status', [0, 1]);

        if ($startDate && $endDate) {
            $bookingQuery->whereBetween('created_at', [$startDate, $endDate]);
        }

        $bookings = $bookingQuery->get();

        // Group by category and count bookings
        $categoryCounts = [];

        foreach ($bookings as $booking) {
            if ($booking->service && $booking->service->category) {
                $categoryName = $booking->service->category->name;
                $categoryCounts[$categoryName] = ($categoryCounts[$categoryName] ?? 0) + 1;
            }
        }

        // Sort by count and get top 3
        arsort($categoryCounts);
        $topCategories = array_slice($categoryCounts, 0, 3, true);

        // Format for chart
        $labels = array_keys($topCategories);
        $data = array_values($topCategories);

        return [
            'labels' => $labels,
            'data' => $data,
            'raw' => $topCategories
        ];
    }

    public function profileSetting()
    {
        $user = auth()->user();
        $services = Service::where('status', 1)->get();
        $product_certifications = Certification::all();
        $holidays = Holiday::all();
        $socialPlatforms = SocialPlatform::all();
        return view('dashboard.profile_settings.index', compact('user', 'services', 'product_certifications', 'holidays', 'socialPlatforms'));
    }

    public function saveMedia(Request $request, $type)
    {
        // Validate type parameter
        if (!in_array($type, ['galleries', 'portfolio'])) {
            abort(404, 'Invalid media type');
        }

        $request->validate([
            'images.*' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'old_images.*' => 'nullable|string',
        ]);

        $user = auth()->user();
        $imagesToDelete = [];

        // Determine model and folder based on type
        $modelClass = $type === 'galleries' ? UserGallery::class : UserPortfolio::class;
        $folderName = $type === 'galleries' ? 'user-gallery' : 'user-portfolio';
        $requestKey = $type === 'galleries' ? 'galleries' : 'portfolio';

        // Collect old images that will be replaced by new uploads
        if ($request->has($requestKey)) {
            foreach ($request->$requestKey as $item) {
                if (isset($item['image']) && isset($item['old_image'])) {
                    // New image is being uploaded to replace old one
                    $imagesToDelete[] = $item['old_image'];
                }
            }
        }

        // Delete only the images that are being replaced
        foreach ($imagesToDelete as $imageToDelete) {
            $this->deleteImage($imageToDelete);
        }

        // Delete all existing entries
        $modelClass::where('user_id', $user->id)->delete();

        // Save new items
        if ($request->has($requestKey)) {
            foreach ($request->$requestKey as $item) {
                $mediaItem = new $modelClass();
                $mediaItem->user_id = $user->id;
                if (isset($item['image'])) {
                    $mediaItem->image = $this->storeImage($folderName, $item['image']);
                } elseif (isset($item['old_image'])) {
                    $mediaItem->image = $item['old_image'];
                }
                $mediaItem->save();
            }
        }

        $message = $type === 'galleries' ? 'User galleries updated successfully!' : 'User portfolio updated successfully!';

        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => $message,
            'type' => 'success'
        ]);
    }

    public function updateCompanyDetails(Request $request)
    {
        $user = auth()->user();

        $user->profile->company_name = $request->company_name;
        $user->profile->vat_number = $request->vat_number;
        $user->profile->company_id = $request->company_id;
        $user->profile->save();
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Company details updated successfully!',
            'type' => 'success'
        ]);
    }

    public function updateProductCertifications(Request $request)
    {
        $request->validate([
            'product_certifications' => 'nullable|array',
            'product_certifications.*' => 'exists:certifications,id',
        ]);
        $user = auth()->user();
        $certifications = $request->input('product_certifications', []);
        $user->product_cerficates()->sync($certifications);
        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Product certifications updated successfully!',
            'type' => 'success'
        ]);
    }

    public function saveCertificates(Request $request)
    {
        $user = auth()->user();
        $hasChanges = false;
        $hasNewCertificates = false;

        // Get existing certificates for comparison
        $existingCertificates = UserCertificate::where('user_id', $user->id)->get()->keyBy('id');
        $submittedCertificateIds = [];

        if ($request->has('certificates')) {
            foreach ($request->certificates as $certification) {
                $certificateData = [
                    'user_id' => $user->id,
                    'title' => $certification['title'] ?? null,
                    'issued_by' => $certification['issued_by'] ?? null,
                    'issued_date' => $certification['issued_date'] ?? null,
                    'end_date' => $certification['end_date'] ?? null,
                    'exception' => isset($certification['exception']) ? 1 : 0,
                    'exception_reason' => $certification['exception_reason'] ?? null,
                ];

                // Handle image upload/retention
                if (isset($certification['image'])) {
                    // New image uploaded
                    $certificateData['image'] = $this->storeImage('certificates', $certification['image']);
                } elseif (isset($certification['old_image']) && !isset($certification['delete_image'])) {
                    // Keep existing image
                    $certificateData['image'] = $certification['old_image'];
                } else {
                    // No image or delete_image is set
                    $certificateData['image'] = null;
                }

                // Check if this is an existing certificate being updated
                if (isset($certification['id']) && $certification['id']) {
                    $existingCert = $existingCertificates->get($certification['id']);

                    if ($existingCert) {
                        // Certificate exists - check its current status
                        if ($existingCert->approval == 1) {
                            // Approved certificate - check if it was modified
                            $isModified = $this->isCertificateModified($existingCert, $certificateData);

                            if ($isModified) {
                                // Modified approved certificate - reset to pending
                                $certificateData['approval'] = 0;
                                $certificateData['status'] = 0;
                                $certificateData['rejection_reason'] = null;
                                $hasChanges = true;
                            } else {
                                // Not modified - keep the approved status
                                $certificateData['approval'] = $existingCert->approval;
                                $certificateData['status'] = $existingCert->status;
                                $certificateData['rejection_reason'] = $existingCert->rejection_reason;
                            }
                        } elseif ($existingCert->approval == 2) {
                            // Rejected certificate - check if it was modified
                            $isModified = $this->isCertificateModified($existingCert, $certificateData);

                            if ($isModified) {
                                // Modified rejected certificate - reset to pending
                                $certificateData['approval'] = 0;
                                $certificateData['status'] = 0;
                                $certificateData['rejection_reason'] = null;
                                $hasChanges = true;
                            } else {
                                // Not modified - keep the rejected status
                                $certificateData['approval'] = $existingCert->approval;
                                $certificateData['status'] = $existingCert->status;
                                $certificateData['rejection_reason'] = $existingCert->rejection_reason;
                            }
                        } else {
                            // Pending certificate - update normally
                            $certificateData['approval'] = 0;
                            $certificateData['status'] = 0;
                            $certificateData['rejection_reason'] = null;
                            $hasChanges = true;
                        }

                        $submittedCertificateIds[] = $certification['id'];
                    }
                } else {
                    // New certificate
                    $certificateData['approval'] = 0;
                    $certificateData['status'] = 0;
                    $hasNewCertificates = true;
                    $hasChanges = true;
                }

                // Use updateOrCreate
                UserCertificate::updateOrCreate(
                    ['id' => $certification['id'] ?? null, 'user_id' => $user->id],
                    $certificateData
                );
            }
        }

        // Delete certificates that were removed from the form
        $certificatesToDelete = $existingCertificates->whereNotIn('id', $submittedCertificateIds);
        foreach ($certificatesToDelete as $certToDelete) {
            if ($certToDelete->image) {
                $this->deleteImage($certToDelete->image);
            }
            $certToDelete->delete();
            $hasChanges = true;
        }

        // Only send notification if there were actual changes
        if ($hasChanges) {
            $this->notifyAdmins(
                'Certificate Update Pending Review',
                "Professional {$user->name} has updated their certificates and is awaiting your approval. Please review and approve or reject their certificate submissions.",
                $user->id,
                'certificate_approval'
            );
        }

        $message = $hasChanges ?
            'Certificates updated successfully! Your certificates are pending admin approval.' :
            'No changes detected.';

        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => $message,
            'type' => 'success'
        ]);
    }

    /**
     * Check if a certificate has been modified
     * Only compares actual certificate data, not status/approval fields
     */
    private function isCertificateModified($existingCert, $newData)
    {
        // Compare text fields (only actual certificate data)
        $textFields = ['title', 'issued_by', 'exception_reason'];

        foreach ($textFields as $field) {
            $existingValue = $existingCert->$field ?? null;
            $newValue = $newData[$field] ?? null;

            // Convert both to strings for comparison to handle type differences
            $existingStr = (string) $existingValue;
            $newStr = (string) $newValue;

            if ($existingStr !== $newStr) {
                return true;
            }
        }

        // Compare date fields - normalize to Y-m-d format
        $dateFields = ['issued_date', 'end_date'];

        foreach ($dateFields as $field) {
            $existingDate = $existingCert->$field ? date('Y-m-d', strtotime($existingCert->$field)) : '';
            $newDate = $newData[$field] ?? '';

            if ($existingDate !== $newDate) {
                return true;
            }
        }

        // Handle exception field separately - convert to boolean for comparison
        $existingException = (bool) ($existingCert->exception ?? 0);
        $newException = (bool) ($newData['exception'] ?? 0);

        if ($existingException !== $newException) {
            return true;
        }

        // Check image changes - only if there's a new image file uploaded
        $existingImage = $existingCert->image ?? null;
        $newImage = $newData['image'] ?? null;

        // Only consider it modified if there's actually a new image file uploaded
        // and it's different from the existing one
        if ($newImage && $newImage !== $existingImage && !empty($newImage)) {
            return true;
        }

        return false;
    }

    /**
     * Accept/Approve a certificate
     */
    public function acceptCertificate($certificateId)
    {
        try {
            $certificate = UserCertificate::findOrFail($certificateId);

            $certificate->approval = 1;
            $certificate->status = 1;
            $certificate->save();

            // Send notification to certificate owner
            $title = 'Certificate Approved';
            $message = "Your certificate '{$certificate->title}' has been approved by an administrator.";
            $filterKeyword = 'certificate_approval';

            $this->user_notification(
                $certificate->user_id,
                $title,
                $message,
                auth()->id(), // sender_user_id (admin who approved)
                $filterKeyword
            );

            return response()->json([
                'success' => true,
                'message' => 'Certificate approved successfully!',
                'title' => 'Success'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to approve certificate: ' . $e->getMessage(),
                'title' => 'Error'
            ], 500);
        }
    }

    /**
     * Reject a certificate
     */
    public function rejectCertificate(Request $request, $certificateId)
    {
        try {
            $request->validate([
                'rejection_reason' => 'required|string|max:255'
            ]);

            $certificate = UserCertificate::findOrFail($certificateId);

            $certificate->approval = 2;
            $certificate->status = 2;
            $certificate->rejection_reason = $request->rejection_reason;
            $certificate->save();

            // Send notification to certificate owner
            $title = 'Certificate Rejected';
            $message = "Your certificate '{$certificate->title}' has been rejected by an administrator. Reason: {$request->rejection_reason}";
            $filterKeyword = 'certificate_approval';

            $this->user_notification(
                $certificate->user_id,
                $title,
                $message,
                auth()->id(), // sender_user_id (admin who rejected)
                $filterKeyword
            );

            return response()->json([
                'success' => true,
                'message' => 'Certificate rejected successfully!',
                'title' => 'Success'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reject certificate: ' . $e->getMessage(),
                'title' => 'Error'
            ], 500);
        }
    }

    public function saveAvailability(Request $request)
    {
        $user = auth()->user();
        // Validate the request
        $request->validate([
            'availability' => 'required|array',
            'holidays' => 'nullable|array',
            'custom_holidays' => 'nullable|array',
        ]);

        $opening_hours = $request->availability ?? [];
        UserOpeningHour::where('user_id', $user->id)->delete();
        foreach ($opening_hours as $hour) {
            $userOpeningHour = new UserOpeningHour();
            $userOpeningHour->user_id = $user->id;
            $userOpeningHour->day = $hour['day'];
            $userOpeningHour->open = $hour['start'] ?? null;
            $userOpeningHour->close = $hour['end'] ?? null;
            if ($hour['start'] && $hour['end']) {
                $userOpeningHour->type = "open";
            } else {
                $userOpeningHour->type = "close";
            }
            $userOpeningHour->save();
        }

        // Handle holidays
        $holidays = $request->holidays ?? [];
        if (!empty($holidays)) {
            UserHoliday::where('user_id', $user->id)->where('is_custom', 0)->delete();
            foreach ($holidays as $holiday) {
                if (isset($holiday['holiday_id'])) {
                    $exist_holiday = Holiday::where('id', $holiday['holiday_id'])->first();
                    if (!$exist_holiday) {
                        continue;
                    }
                    $userHoliday = new UserHoliday();
                    $userHoliday->user_id = $user->id;
                    $userHoliday->holiday_id = $exist_holiday->id;
                    $userHoliday->name = $holiday['name'] ?? null;
                    $userHoliday->date = $holiday['date'] ?? null;
                    $userHoliday->start_time = $holiday['start_time'] ?? null;
                    $userHoliday->end_time = $holiday['end_time'] ?? null;
                    if (isset($holiday['start_time'], $holiday['end_time'])) {
                        $userHoliday->is_full_day = 0;
                    } else {
                        $userHoliday->is_full_day = 1;
                    }
                    $userHoliday->is_custom = 0;
                    $userHoliday->save();
                }
            }
        }

        // Handle custom holidays
        $customHolidays = $request->custom_holidays ?? [];
        if (!empty($customHolidays)) {
            UserHoliday::where('user_id', $user->id)->where('is_custom', 1)->delete();
            foreach ($customHolidays as $customHoliday) {
                $rawDate = $customHoliday['date'] ?? null;
                if ($rawDate) {
                    try {
                        $formattedDate = Carbon::parse($rawDate)->format('Y-m-d');
                        $exists = UserHoliday::where('user_id', $user->id)->where('date', $formattedDate)->exists();
                        if (!$exists) {
                            $userHoliday = new UserHoliday();
                            $userHoliday->user_id = $user->id;
                            $userHoliday->name = $customHoliday['name'] ?? null;
                            $userHoliday->date = $formattedDate;
                            $userHoliday->start_time = $customHoliday['start_time'] ?? null;
                            $userHoliday->end_time = $customHoliday['end_time'] ?? null;
                            if (isset($customHoliday['start_time'], $customHoliday['end_time'])) {
                                $userHoliday->is_full_day = 0;
                            } else {
                                $userHoliday->is_full_day = 1;
                            }
                            $userHoliday->is_custom = 1;
                            $userHoliday->save();
                        }
                    } catch (\Exception $e) {
                        continue;
                    }
                }
            }
        }

        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => 'Availability updated successfully!',
            'type' => 'success'
        ]);
    }

    public function saveIntroCards(Request $request)
    {
        $user = auth()->user();
        $introCards = $request->input('introCards', []);
        $introCardCount = count($introCards);

        // Debug: Log the request data
        Log::info('Intro Cards Request Data:', [
            'introCards' => $introCards,
            'files' => $request->allFiles(),
            'count' => $introCardCount
        ]);
        $allowedCounts = [0, 3, 6, 9];
        if (!in_array($introCardCount, $allowedCounts)) {
            return redirect()->back()->with([
                'title' => 'Invalid Number of Cards',
                'message' => 'Please submit exactly 3, 6, or 9 intro cards.',
                'type' => 'error'
            ]);
        }
        if ($introCardCount > 9) {
            return redirect()->back()->with([
                'title' => 'Too Many Cards',
                'message' => 'You cannot submit more than 9 intro cards.',
                'type' => 'error'
            ]);
        }
        UserIntroCard::where('user_id', $user->id)->delete();
        if ($introCardCount > 0) {
            foreach ($introCards as $index => $introCard) {
                $userIntroCard = new UserIntroCard();
                $userIntroCard->user_id = $user->id;
                $userIntroCard->heading = $introCard['heading'] ?? null;
                $userIntroCard->description = $introCard['description'] ?? null;

                // Check for uploaded file first
                if ($request->hasFile("introCards.{$index}.image")) {
                    $imageFile = $request->file("introCards.{$index}.image");
                    Log::info('Storing new image for intro card', ['index' => $index, 'file' => $imageFile->getClientOriginalName()]);

                    // Delete old image if it exists
                    if (isset($introCard['old_image']) && !empty($introCard['old_image'])) {
                        $this->deleteImage($introCard['old_image']);
                        Log::info('Deleted old image', ['old_image' => $introCard['old_image']]);
                    }

                    $userIntroCard->image = $this->storeImage('user-intro-cards', $imageFile);
                    Log::info('Image stored at:', ['path' => $userIntroCard->image]);

                    // Check if file actually exists
                    $fullPath = public_path('website/' . $userIntroCard->image);
                    Log::info('File exists check:', ['full_path' => $fullPath, 'exists' => file_exists($fullPath)]);
                } elseif (isset($introCard['old_image']) && !empty($introCard['old_image'])) {
                    Log::info('Using old image for intro card', ['old_image' => $introCard['old_image']]);
                    $userIntroCard->image = $introCard['old_image'];
                } else {
                    Log::info('No image found for intro card', ['index' => $index]);
                    $userIntroCard->image = null;
                }

                $userIntroCard->save();
                Log::info('Saved intro card to database:', [
                    'id' => $userIntroCard->id,
                    'user_id' => $userIntroCard->user_id,
                    'heading' => $userIntroCard->heading,
                    'image' => $userIntroCard->image
                ]);
            }
        }

        // Debug: Check what's actually in the database after saving
        $savedCards = UserIntroCard::where('user_id', $user->id)->get();
        Log::info('Final database state:', ['saved_cards' => $savedCards->toArray()]);

        return redirect()->route('profile_settings')->with([
            'title' => 'Success',
            'message' => $introCardCount === 0 ? 'All intro cards removed successfully!' : 'Intro cards updated successfully!',
            'type' => 'success'
        ]);
    }

    public function viewLogs()
    {
        $logFile = storage_path('logs/laravel.log');

        if (!file_exists($logFile)) {
            return response('<h1>Log file not found</h1>', 404);
        }

        // Get the last 100 lines of the log file
        $lines = [];
        $file = new \SplFileObject($logFile, 'r');
        $file->seek(PHP_INT_MAX);
        $totalLines = $file->key();

        // Get last 100 lines or all lines if less than 100
        $startLine = max(0, $totalLines - 100);
        $file->seek($startLine);

        while (!$file->eof()) {
            $lines[] = $file->fgets();
        }

        $logContent = implode('', $lines);

        // Format the output with basic HTML
        $html = '
        <!DOCTYPE html>
        <html>
        <head>
            <title>Laravel Logs</title>
            <style>
                body { font-family: monospace; margin: 20px; background: #1e1e1e; color: #fff; }
                .log-content { white-space: pre-wrap; background: #2d2d2d; padding: 20px; border-radius: 5px; }
                .refresh-btn { background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-bottom: 20px; display: inline-block; }
                .clear-btn { background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-bottom: 20px; display: inline-block; margin-left: 10px; }
            </style>
        </head>
        <body>
            <h1>Laravel Logs (Last 100 lines)</h1>
            <a href="' . route('debug.logs') . '" class="refresh-btn">Refresh</a>
            <a href="' . route('debug.logs.clear') . '" class="clear-btn" onclick="return confirm(\'Are you sure you want to clear the logs?\')">Clear Logs</a>
            <div class="log-content">' . htmlspecialchars($logContent) . '</div>
        </body>
        </html>';

        return response($html);
    }

    public function clearLogs()
    {
        $logFile = storage_path('logs/laravel.log');

        if (file_exists($logFile)) {
            file_put_contents($logFile, '');
        }

        return redirect()->route('debug.logs')->with('message', 'Logs cleared successfully!');
    }

    public function checkCurrentPassword(Request $request)
    {
        $user = auth()->user();
        $isValid = Hash::check($request->current_password, $user->password);

        return response()->json(['valid' => $isValid]);
    }

    public function updatePassword(Request $request)
    {
        $request->validate([
            'current_password' => 'required',
            'new_password' => 'required|min:8',
            'confirm_password' => 'required|same:new_password',
        ]);

        $user = auth()->user();
        if (!Hash::check($request->current_password, $user->password)) {
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Current password is incorrect!',
                'type' => 'error'
            ]);
        }
        $user->password = Hash::make($request->new_password);
        $user->save();
        return redirect()->back()->with([
            'title' => 'Success',
            'message' => 'Password updated successfully!',
            'type' => 'success'
        ]);
    }

    public function showCustomer($id)
    {
        $user = User::where('ids', $id)->firstOrFail();
        $friends = Friend::where('user_id', $user->id)->get();
        return view('dashboard.admin.customers.show', compact('user', 'friends'));
    }

    /**
     * AJAX method to filter and search customers
     */
    public function filterCustomers(Request $request)
    {
        // Use has() instead of filled() to properly handle "0" as a valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $status = $request->has('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $date = $request->has('date') ? trim($request->get('date')) : '';
        $dateFilter = $request->has('date_filter') ? $request->get('date_filter') : null;

        Log::info('Customer filter request', [
            'search' => $search,
            'status' => $status,
            'date' => $date,
            'dateFilter' => $dateFilter
        ]);

        // Base query for customers
        $query = User::whereHas('roles', function ($q) {
            $q->where('name', 'customer');
        });

        // Only apply search if search term is provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Only apply status filter if status is provided and not 'all'
        if ($status !== '') {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        // Only apply date filter if date information is provided
        if (!empty($dateFilter) && is_array($dateFilter)) {
            try {
                $startDate = $dateFilter['start_date'] ?? null;
                $endDate = $dateFilter['end_date'] ?? null;
                $filterType = $dateFilter['type'] ?? 'single';

                if (!empty($startDate)) {
                    $parsedStartDate = Carbon::parse($startDate)->format('Y-m-d');

                    if ($filterType === 'range' && !empty($endDate)) {
                        $parsedEndDate = Carbon::parse($endDate)->format('Y-m-d');
                        Log::info('Customer date range filter applied', [
                            'start_date' => $parsedStartDate,
                            'end_date' => $parsedEndDate,
                            'original' => $dateFilter
                        ]);
                        $query->whereDate('created_at', '>=', $parsedStartDate)
                            ->whereDate('created_at', '<=', $parsedEndDate);
                    } else {
                        Log::info('Customer single date filter applied', [
                            'date' => $parsedStartDate,
                            'original' => $dateFilter
                        ]);
                        $query->whereDate('created_at', $parsedStartDate);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Customer date filter parsing failed', ['date_filter' => $dateFilter, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        } elseif (!empty($date)) {
            // Fallback to old date handling for backward compatibility
            try {
                $filterDate = Carbon::parse($date)->format('Y-m-d');
                Log::info('Customer fallback date filter applied', ['original_date' => $date, 'parsed_date' => $filterDate]);
                $query->whereDate('created_at', $filterDate);
            } catch (\Exception $e) {
                Log::error('Customer fallback date parsing failed', ['date' => $date, 'error' => $e->getMessage()]);
                // Invalid date format, ignore filter
            }
        }

        $customers = $query->with(['profile', 'roles'])->orderBy('created_at', 'desc')->paginate(10);
        $html = view('dashboard.admin.customers.partials.customers-table', ['customers' => $customers])->render();

        // Generate pagination HTML
        $paginationHtml = '';
        if ($customers->hasPages()) {
            $paginationHtml = $customers->links('pagination::bootstrap-4')->render();
        }

        Log::info('Customer filter results', [
            'count' => $customers->count(),
            'total' => $customers->total(),
            'has_pages' => $customers->hasPages(),
            'current_page' => $customers->currentPage(),
            'last_page' => $customers->lastPage(),
            'per_page' => $customers->perPage(),
            'pagination_html_length' => strlen($paginationHtml)
        ]);

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $customers->count(),
            'total' => $customers->total(),
            'has_pages' => $customers->hasPages()
        ]);
    }

    public function setting()
    {
        return view('dashboard.setting');
    }

    public function adminAnalytics()
    {
        return view('dashboard.admin.analytics');
    }

    public function updateAdminAccount(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        try {
            DB::beginTransaction();

            $user = auth()->user();
            $user->name = $request->name;
            $user->save();

            // Handle avatar upload to profile
            $profile = $user->profile;
            if (!$profile) {
                $profile = new \App\Models\Profile();
                $profile->user_id = $user->id;
            }

            if ($request->hasFile('avatar')) {
                // Delete old avatar if exists
                if ($profile->pic && $profile->pic !== 'no_avatar.jpg') {
                    $this->deleteImage($profile->pic);
                }

                // Store new avatar using the website disk (consistent with other parts)
                $profile->pic = $this->storeImage('user-image', $request->file('avatar'));
            }

            $profile->save();
            DB::commit();

            // Generate avatar URL
            $avatarUrl = null;
            if ($profile->pic && $profile->pic !== 'no_avatar.jpg') {
                $avatarUrl = asset('website/' . $profile->pic);
            }

            return response()->json([
                'success' => true,
                'message' => 'Account updated successfully',
                'name' => $user->name,
                'avatar_url' => $avatarUrl
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Admin account update failed: ' . $e->getMessage());
            Log::error('Stack trace: ' . $e->getTraceAsString());

            return response()->json([
                'success' => false,
                'message' => 'Failed to update account. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : 'An error occurred'
            ], 500);
        }
    }
}
