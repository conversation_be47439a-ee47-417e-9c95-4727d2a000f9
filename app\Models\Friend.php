<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasUuid;

class Friend extends Model
{
    use HasFactory,HasUuid;
    protected $fillable = [
        'ids',
        'user_id',
        'friend_user_id',
        'profile_pic',
        'name',
        'relationship',
        'service_preferences',
        'status',
        'type',
    ];

    protected $casts = [
        'service_preferences' => 'array', // Since it's stored as string but used as array
    ];

    // The owner of this friend relationship
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // The friend user (only for above-13, null for under-13)
    public function friendUser()
    {
        return $this->belongsTo(User::class, 'friend_user_id');
    }
}
