@forelse ($transactions as $transaction)
    <tr>
        <td data-label="PROFESSIONAL NAME">{{ $transaction->user->name ?? 'N/A' }}</td>
        <td data-label="SUBSCRIPTION NAME">{{ $transaction->subscription->name ?? 'N/A' }}</td>
        <td data-label="SUBSCRIPTION TYPE">{{ $transaction->subscription_type ?? 'N/A' }}</td>
        <td data-label="PRICE">${{ number_format($transaction->subscription_price ?? 0, 2) }}</td>
        <td data-label="STATUS" class="request-status status
            @if ($transaction->status == 1) paid-status
            @else unpaid-status @endif">
          <span class="status-text">  {{ $transaction->status == 1 ? 'Active' : 'Inactive' }} </span>
        </td>
        <td data-label="START DATE">
            {{ $transaction->start_date ? \Carbon\Carbon::parse($transaction->start_date)->format('d M, Y') : 'N/A' }}
        </td>
        <td data-label="END DATE">
            {{ $transaction->end_date ? \Carbon\Carbon::parse($transaction->end_date)->format('d M, Y') : 'N/A' }}
        </td>
        <td data-label="ACTION">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton{{ $transaction->id }}"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ $transaction->id }}">
                    <li>
                        <a class="dropdown-item view fs-14 regular" href="{{ route('subscription.detail', ['id' => $transaction->id]) }}">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="8" class="text-center">No Professional Transactions Found</td>
    </tr>
@endforelse
