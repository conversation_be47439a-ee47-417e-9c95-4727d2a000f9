<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use App\Models\Certification;
use App\Http\Requests\CertificationRequest;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class CertificationsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:certifications-list|certifications-create|certifications-edit|certifications-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:certifications-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:certifications-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:certifications-delete', ['only' => ['destroy']]);
        $this->middleware('permission:certifications-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $offset = $request->get('offset', 0);
        $limit = 10;
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';

        // Build query
        $query = Certification::query();

        // Apply search filter if provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get records with offset and limit
        $certifications = $query->offset($offset)->limit($limit)->get();

        // If this is an AJAX request, return JSON response
        if ($request->ajax()) {
            $html = view('dashboard.admin.certifications.partials.certification-cards', [
                'certifications' => $certifications
            ])->render();

            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $certifications->count(),
                'total' => $totalCount,
                'offset' => $offset,
                'next_offset' => $offset + $certifications->count(),
                'has_more' => ($offset + $limit) < $totalCount
            ]);
        }

        // Regular page load
        return view('dashboard.admin.certifications.certifications', [
            'certifications' => $certifications,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Load more certification records via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadMore(Request $request)
    {
        $offset = $request->get('offset', 0);
        $limit = 10;
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';

        // Build query
        $query = Certification::query();

        // Apply search filter if provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where('name', 'LIKE', '%' . $search . '%');
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get records with offset and limit
        $certifications = $query->offset($offset)->limit($limit)->get();

        $html = view('dashboard.admin.certifications.partials.certification-cards', [
            'certifications' => $certifications
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $certifications->count(),
            'total' => $totalCount,
            'offset' => $offset,
            'next_offset' => $offset + $certifications->count(),
            'has_more' => ($offset + $limit) < $totalCount
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('certifications.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  CertificationRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(CertificationRequest $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'alt_tag' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.alt_tag_length')],
            'image_description' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.image_description_length')],
        ],[
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'alt_tag.required' => 'Please enter alt tag',
            'alt_tag.regex' => $messagesConfig['input']['regex'],
            'alt_tag.max' => $messagesConfig['alt_tag']['max'],
            'image_description.required' => 'Please enter image description',
            'image_description.regex' => $messagesConfig['input']['regex'],
            'image_description.max' => $messagesConfig['image_description']['max'],
            'name.required' => 'Please enter category name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['name']['max'],
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $certificationData = $validator->validated();
            if ($request->hasFile("avatar")) {
                $certificationData['image'] = $this->storeImage("certification-images", $request->file('avatar'));
            }
            Certification::create($certificationData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Certification added successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $certification = Certification::where('ids', $id)->firstOrFail();
        return view('certifications.show', ['certification' => $certification]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $certification = Certification::where('ids', $id)->firstOrFail();
        return response()->json($certification);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  CertificationRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(CertificationRequest $request, $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'alt_tag' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.alt_tag_length')],
            'image_description' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.image_description_length')],
        ],[
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'alt_tag.required' => 'Please enter alt tag',
            'alt_tag.regex' => $messagesConfig['input']['regex'],
            'alt_tag.max' => $messagesConfig['alt_tag']['max'],
            'image_description.required' => 'Please enter image description',
            'image_description.regex' => $messagesConfig['input']['regex'],
            'image_description.max' => $messagesConfig['image_description']['max'],
            'name.required' => 'Please enter category name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['name']['max'],
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $certification = Certification::where('ids', $id)->firstOrFail();
            $certificationData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($certification->image);
                $certificationData['image'] = $this->storeImage('certification-images', $request->file('avatar'));
            }
            $certification->update($certificationData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Created", "message" => 'Certification updated successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $certification = Certification::where('ids', $id)->firstOrFail();
        $this->deleteImage($certification->image);
        $certification->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Certification deleted successfully'
        ]);
    }
}
