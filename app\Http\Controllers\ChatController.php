<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\ConversationUserSetting;
use App\Models\Message;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Events\MessageSent;
use App\Events\MessageDelivered;
use App\Events\MessageRead;
use App\Events\UserTyping;
use App\Events\UserMessageReceived;

class ChatController extends Controller
{
    /**
     * Display the chat interface
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $conversationIdParam = $request->get('conversation_id');
        $professionalId = $request->get('professional_id');

        // Convert conversation_id from IDS to database ID if needed
        $conversationId = null;
        if ($conversationIdParam) {
            // Try to find by IDS first, then by database ID
            $conversation = Conversation::where('ids', $conversationIdParam)->first();
            if (!$conversation) {
                $conversation = Conversation::find($conversationIdParam);
            }
            $conversationId = $conversation ? $conversation->id : null;
        }

        // If professional_id is provided, create or get conversation and redirect
        if ($professionalId) {
            $professional = User::where('ids', $professionalId)->first();
            if ($professional && $user->hasRole('customer')) {
                $conversation = Conversation::createOrGet($user->id, $professional->id);
                // Redirect to clean URL and pass conversation ID to view
                $conversationId = $conversation->id;
                return view('dashboard.chats.index', compact('conversationId'));
            }
        }

        return view('dashboard.chats.index', compact('conversationId'));
    }

    /**
     * Get conversations for sidebar
     */
    public function getConversations(Request $request)
    {
        try {
            $user = Auth::user();
            $type = $request->get('type', 'active'); // 'active' or 'archived'

            // Check if user is admin or super admin
            $isAdmin = $user->hasRole(['admin', 'super admin']);

            if ($isAdmin) {
                // For admin/super admin: show ALL conversations
                $conversations = Conversation::where('is_active', true)
                    ->with(['sender.profile', 'receiver.profile', 'latestMessage.sender'])
                    ->get()
                    ->filter(function ($conversation) use ($type, $user) {
                        // For admin, check if admin has archived this conversation for their own view
                        $isArchivedForAdmin = $conversation->isArchivedForUser($user->id);
                        
                        return $type === 'active' ? !$isArchivedForAdmin : $isArchivedForAdmin;
                    })
                    ->sortByDesc('last_message_at')
                    ->values()
                    ->map(function ($conversation) use ($user) {
                        // For admin view, show both participants
                        $sender = $conversation->sender;
                        $receiver = $conversation->receiver;
                        
                        // Check if conversation is archived for admin user
                        $isArchived = $conversation->isArchivedForUser($user->id);
                        
                        return [
                            'id' => $conversation->id,
                            'ids' => $conversation->ids,
                            'sender_user' => [
                                'id' => $sender->id,
                                'name' => $sender->name,
                                'profile_pic' => $sender->profile->pic ?? null,
                                'role' => $sender->getRoleNames()->first(),
                                'is_online' => $sender->is_online ?? false,
                                'online_at' => $sender->online_at
                            ],
                            'receiver_user' => [
                                'id' => $receiver->id,
                                'name' => $receiver->name,
                                'profile_pic' => $receiver->profile->pic ?? null,
                                'role' => $receiver->getRoleNames()->first(),
                                'is_online' => $receiver->is_online ?? false,
                                'online_at' => $receiver->online_at
                            ],
                            'last_message' => $conversation->latestMessage ? [
                                'content' => $this->getLastMessageContent($conversation->latestMessage),
                                'created_at' => $conversation->latestMessage->created_at,
                                'is_sender' => $conversation->latestMessage->sender_id == $user->id,
                                'sender_name' => $conversation->latestMessage->sender->name
                            ] : null,
                            'unread_count' => 0, // Admin doesn't have unread count
                            'updated_at' => $conversation->last_message_at,
                            'is_archived' => $isArchived
                        ];
                    });
            } else {
                // For regular users: get conversations with user-specific archive settings
                $conversations = Conversation::where(function($query) use ($user) {
                        $query->where('sender_id', $user->id)
                              ->orWhere('receiver_id', $user->id);
                    })
                    ->where('is_active', true) // Only get active conversations from main table
                    ->with(['sender.profile', 'receiver.profile', 'latestMessage.sender'])
                    ->get()
                    ->filter(function ($conversation) use ($user, $type) {
                        // Filter based on user-specific archive status
                        $isArchivedForUser = $conversation->isArchivedForUser($user->id);
                        return $type === 'active' ? !$isArchivedForUser : $isArchivedForUser;
                    })
                    ->sortByDesc('last_message_at')
                    ->values()
                    ->map(function ($conversation) use ($user) {
                        $otherUser = $conversation->getOtherParticipant($user->id);
                        return [
                            'id' => $conversation->id,
                            'ids' => $conversation->ids,
                            'other_user' => [
                                'id' => $otherUser->id,
                                'name' => $otherUser->name,
                                'profile_pic' => $otherUser->profile->pic ?? null,
                                'role' => $otherUser->getRoleNames()->first(),
                                'is_online' => $otherUser->is_online ?? false,
                                'online_at' => $otherUser->online_at
                            ],
                            'last_message' => $conversation->latestMessage ? [
                                'content' => $this->getLastMessageContent($conversation->latestMessage),
                                'created_at' => $conversation->latestMessage->created_at,
                                'is_sender' => $conversation->latestMessage->sender_id == $user->id,
                                'sender_name' => $conversation->latestMessage->sender->name
                            ] : null,
                            'unread_count' => $conversation->getUnreadCount($user->id),
                            'updated_at' => $conversation->last_message_at,
                            'is_archived' => $conversation->isArchivedForUser($user->id)
                        ];
                    });
            }

            return response()->json([
                'success' => true,
                'conversations' => $conversations
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading conversations: ' . $e->getMessage(),
                'conversations' => []
            ], 500);
        }
    }

    /**
     * Get messages for a conversation
     */
    public function getMessages(Request $request, $conversationId)
    {
        $user = Auth::user();
        $page = $request->get('page', 1);
        $perPage = 20;

        $conversation = Conversation::find($conversationId);

        // Check if user is admin or super admin
        $isAdmin = $user->hasRole(['admin', 'super admin']);

        if (!$conversation) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // For admin/super admin: allow viewing ANY conversation
        if ($isAdmin) {
            // Admin can view all conversations - no restrictions
        } else {
            // For regular users: check if they are participant
            if (!$conversation->isParticipant($user->id)) {
                return response()->json(['error' => 'Conversation not found'], 404);
            }
        }

        // Only mark messages as read for regular users, not for admin viewing
        if (!$isAdmin) {
            $conversation->markAsRead($user->id);
        }

        $messages = $conversation->messages()
            ->with('sender.profile')
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        $formattedMessages = $messages->items();
        $formattedMessages = array_reverse($formattedMessages);

        $messagesData = collect($formattedMessages)->map(function ($message) use ($user) {
            return [
                'id' => $message->id,
                'content' => $message->content,
                'message_type' => $message->message_type,
                'attachments' => $message->getFormattedAttachments(),
                'sender' => [
                    'id' => $message->sender->id,
                    'name' => $message->sender->name,
                    'profile_pic' => $message->sender->profile->pic ?? null,
                    'role' => $message->sender->getRoleNames()->first()
                ],
                'is_sender' => $message->sender_id == $user->id,
                'sending_status' => $message->sending_status,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'formatted_time' => $message->created_at->format('H:i'),
                'is_edited' => $message->is_edited
            ];
        });

        // Prepare conversation data based on user role
        if ($isAdmin) {
            $conversationData = [
                'id' => $conversation->id,
                'sender_user' => [
                    'id' => $conversation->sender->id,
                    'name' => $conversation->sender->name,
                    'profile_pic' => $conversation->sender->profile->pic ?? null,
                    'role' => $conversation->sender->getRoleNames()->first(),
                    'is_online' => $conversation->sender->is_online ?? false,
                    'online_at' => $conversation->sender->online_at
                ],
                'receiver_user' => [
                    'id' => $conversation->receiver->id,
                    'name' => $conversation->receiver->name,
                    'profile_pic' => $conversation->receiver->profile->pic ?? null,
                    'role' => $conversation->receiver->getRoleNames()->first(),
                    'is_online' => $conversation->receiver->is_online ?? false,
                    'online_at' => $conversation->receiver->online_at
                ]
            ];
        } else {
            $conversationData = [
                'id' => $conversation->id,
                'other_user' => [
                    'id' => $conversation->getOtherParticipant($user->id)->id,
                    'name' => $conversation->getOtherParticipant($user->id)->name,
                    'profile_pic' => $conversation->getOtherParticipant($user->id)->profile->pic ?? null,
                    'is_online' => $conversation->getOtherParticipant($user->id)->is_online ?? false,
                    'online_at' => $conversation->getOtherParticipant($user->id)->online_at
                ]
            ];
        }

        return response()->json([
            'success' => true,
            'messages' => $messagesData,
            'has_more' => $messages->hasMorePages(),
            'current_page' => $messages->currentPage(),
            'conversation' => $conversationData
        ]);
    }

    /**
     * Send a new message
     */
    public function sendMessage(Request $request)
    {
        $user = Auth::user();

        // Check if user is admin or super admin - they cannot send messages
        if ($user->hasRole(['admin', 'super admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Admin and Super Admin users cannot send messages. This is a read-only view for monitoring purposes.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'content' => 'required_without:attachments|string|max:5000',
            'message_type' => 'in:text,image,video,document',
            'attachments.*' => 'file|max:50000', // 50MB max per file
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $conversation = Conversation::find($request->conversation_id);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Handle file attachments
        $attachments = [];
        if ($request->hasFile('attachments')) {
            $attachments = $this->handleFileUploads($request->file('attachments'));
        }

        // Create message
        $message = Message::create([
            'conversation_id' => $conversation->id,
            'sender_id' => $user->id,
            'content' => $request->content,
            'message_type' => $request->message_type ?? 'text',
            'attachments' => $attachments,
            'sending_status' => 'sent',
            'sent_at' => now()
        ]);

        // Update conversation last message
        $conversation->updateLastMessage($message->id);

        // Load relationships for response
        $message->load('sender.profile');

        $messageData = [
            'id' => $message->id,
            'content' => $message->content,
            'message_type' => $message->message_type,
            'attachments' => $message->getFormattedAttachments(),
            'sender' => [
                'id' => $message->sender->id,
                'name' => $message->sender->name,
                'profile_pic' => $message->sender->profile->pic ?? null,
                'role' => $message->sender->getRoleNames()->first()
            ],
            'is_sender' => true,
            'sending_status' => $message->sending_status,
            'created_at' => $message->created_at->format('Y-m-d H:i:s'),
            'formatted_time' => $message->created_at->format('H:i'),
            'conversation_id' => $conversation->id
        ];

        // Broadcast message via Pusher to conversation channel
        broadcast(new MessageSent($messageData, $conversation->id, $conversation->ids))->toOthers();

        // Also broadcast to the receiver's user-specific channel for delivery notifications
        $receiverId = $conversation->sender_id == $user->id ? $conversation->receiver_id : $conversation->sender_id;

        try {
            broadcast(new UserMessageReceived($messageData, $conversation->id, $conversation->ids, $receiverId))->toOthers();
        } catch (\Exception $e) {
            // Handle broadcast error silently
        }

        // Don't auto-mark as delivered here - let the frontend handle it based on user online status
        // The message will be marked as delivered when the receiver comes online and receives the Pusher event

        return response()->json([
            'success' => true,
            'message' => $messageData
        ]);
    }

    /**
     * Handle file uploads without compression
     */
    private function handleFileUploads($files)
    {
        $attachments = [];
        $imageCount = 0;
        $videoCount = 0;
        $docCount = 0;

        foreach ($files as $file) {
            $mimeType = $file->getMimeType();
            $fileType = $this->getFileType($mimeType);

            // Check limits
            if ($fileType === 'image' && $imageCount >= 10) continue;
            if ($fileType === 'video' && $videoCount >= 3) continue;
            if ($fileType === 'document' && $docCount >= 5) continue;

            // Use storeImage method for all file types
            $path = $this->storeImage("chats/{$fileType}s", $file);

            if ($fileType === 'image') {
                $imageCount++;
            } elseif ($fileType === 'video') {
                $videoCount++;
            } else {
                $docCount++;
            }

            $attachments[] = [
                'name' => $file->getClientOriginalName(),
                'path' => $path,
                'type' => $fileType,
                'size' => $file->getSize(),
                'mime_type' => $mimeType
            ];
        }

        return $attachments;
    }

    /**
     * Handle file uploads via AJAX
     */
    public function uploadFiles(Request $request)
    {
        $user = Auth::user();

        // Check if user is admin or super admin - they cannot upload files
        if ($user->hasRole(['admin', 'super admin'])) {
            return response()->json([
                'success' => false,
                'message' => 'Admin and Super Admin users cannot upload files. This is a read-only view for monitoring purposes.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'conversation_id' => 'required|exists:conversations,id',
            'files.*' => 'required|file|max:51200', // 50MB max per file
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $conversation = Conversation::findOrFail($request->conversation_id);

            // Check if user is part of the conversation
            if (!$conversation->isParticipant($user->id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to conversation'
                ], 403);
            }

            // Handle file attachments
            $attachments = [];
            if ($request->hasFile('files')) {
                $attachments = $this->handleFileUploads($request->file('files'));
            }

            // Create message with attachments
            $message = Message::create([
                'conversation_id' => $conversation->id,
                'sender_id' => $user->id,
                'content' => '', // Empty content for file-only messages
                'attachments' => $attachments,
                'message_type' => 'file',
                'sending_status' => 'sent',
                'sent_at' => now()
            ]);

            // Update conversation's last message
            $conversation->update([
                'last_message_id' => $message->id,
                'last_message_at' => now()
            ]);

            // Broadcast the message
            $messageData = [
                'id' => $message->id,
                'conversation_id' => $conversation->id,
                'content' => $message->content,
                'message_type' => $message->message_type,
                'attachments' => $message->getFormattedAttachments(),
                'sender' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'profile_pic' => $user->profile->pic ?? null,
                    'role' => $user->getRoleNames()->first()
                ],
                'is_sender' => true,
                'sending_status' => $message->sending_status,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'formatted_time' => $message->created_at->format('H:i'),
                'is_edited' => $message->is_edited
            ];

            broadcast(new MessageSent($messageData, $conversation->id, $conversation->ids));

            return response()->json([
                'success' => true,
                'message' => 'Files uploaded successfully',
                'data' => [
                    'message_id' => $message->id,
                    'attachments' => $attachments,
                    'message_data' => $messageData
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload files: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get appropriate content for last message display
     */
    private function getLastMessageContent($message)
    {
        // If it's a text message, return the content
        if ($message->message_type === 'text' && !empty($message->content)) {
            return $message->content;
        }

        // If it's a file message, return appropriate text based on attachments
        if ($message->message_type === 'file' && $message->hasAttachments()) {
            $attachments = $message->attachments;

            // Handle both array and string cases for backward compatibility
            if (is_string($attachments)) {
                $attachments = json_decode($attachments, true) ?? [];
            }

            if (!empty($attachments)) {
                $firstAttachment = $attachments[0];
                $type = $firstAttachment['type'] ?? 'file';
                $count = count($attachments);

                switch ($type) {
                    case 'image':
                        return $count > 1 ? "📷 {$count} photos" : "📷 Photo";
                    case 'video':
                        return $count > 1 ? "🎥 {$count} videos" : "🎥 Video";
                    case 'document':
                        return $count > 1 ? "📄 {$count} documents" : "📄 Document";
                    default:
                        return $count > 1 ? "📎 {$count} files" : "📎 File";
                }
            }
        }

        // Fallback for other message types or empty content
        return $message->content ?: 'Message';
    }

    /**
     * Determine file type from mime type
     */
    private function getFileType($mimeType)
    {
        if (strpos($mimeType, 'image/') === 0) {
            return 'image';
        } elseif (strpos($mimeType, 'video/') === 0) {
            return 'video';
        } else {
            return 'document';
        }
    }

    /**
     * Mark message as delivered
     */
    public function markAsDelivered(Request $request, $messageId)
    {
        $user = Auth::user();
        $message = Message::find($messageId);

        if (!$message) {
            return response()->json(['error' => 'Message not found'], 404);
        }

        // Check if user is participant in this conversation
        $conversation = $message->conversation;
        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Only update if message is not from current user and status is 'sent'
        if ($message->sender_id != $user->id && $message->sending_status == 'sent') {
            $message->update([
                'sending_status' => 'delivered',
                'delivered_at' => now()
            ]);

            // Broadcast delivered status to sender
            broadcast(new \App\Events\MessageDelivered($message->id, $conversation->id))->toOthers();
        }

        return response()->json(['success' => true]);
    }

    /**
     * Mark messages as read
     */
    public function markAsRead(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Get unread message IDs before marking as read
        $unreadMessageIds = $conversation->messages()
            ->where('sender_id', '!=', $user->id)
            ->whereNull('read_at')
            ->pluck('id')
            ->toArray();
        $conversation->markAsRead($user->id);

        // Broadcast read event if there were unread messages
        if (!empty($unreadMessageIds)) {
            broadcast(new MessageRead($unreadMessageIds, $conversation->id))->toOthers();
        }

        return response()->json(['success' => true]);
    }

    /**
     * Handle typing indicator
     */
    public function typing(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        if (!$conversation || !$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        $isTyping = $request->get('is_typing', true);

        // Broadcast typing event
        broadcast(new UserTyping($user, $conversation->id, $isTyping))->toOthers();

        return response()->json(['success' => true]);
    }

    /**
     * Archive a conversation for the current user only
     */
    public function archiveConversation(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        // Check if user is admin or super admin
        $isAdmin = $user->hasRole(['admin', 'super admin']);

        if (!$conversation) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // For admin users, allow archiving any conversation for their own view
        if ($isAdmin) {
            // Admin can archive any conversation for their own monitoring view
            $conversation->archiveForUser($user->id);
            
            return response()->json([
                'success' => true,
                'message' => 'Conversation archived for your monitoring view'
            ]);
        }

        // For regular users, check if they are participant
        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Archive conversation only for the current user
        $conversation->archiveForUser($user->id);

        return response()->json([
            'success' => true,
            'message' => 'Conversation archived successfully'
        ]);
    }

    /**
     * Unarchive a conversation for the current user only
     */
    public function unarchiveConversation(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        // Check if user is admin or super admin
        $isAdmin = $user->hasRole(['admin', 'super admin']);

        if (!$conversation) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // For admin users, allow unarchiving any conversation for their own view
        if ($isAdmin) {
            // Admin can unarchive any conversation for their own monitoring view
            $conversation->unarchiveForUser($user->id);
            
            return response()->json([
                'success' => true,
                'message' => 'Conversation unarchived for your monitoring view'
            ]);
        }

        // For regular users, check if they are participant
        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Unarchive conversation only for the current user
        $conversation->unarchiveForUser($user->id);

        return response()->json([
            'success' => true,
            'message' => 'Conversation unarchived successfully'
        ]);
    }

    /**
     * Delete a conversation
     */
    public function deleteConversation(Request $request, $conversationId)
    {
        $user = Auth::user();
        $conversation = Conversation::find($conversationId);

        // Check if user is admin or super admin
        $isAdmin = $user->hasRole(['admin', 'super admin']);

        if (!$conversation) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // For admin users, they can't delete conversations (read-only access)
        if ($isAdmin) {
            return response()->json(['error' => 'Admin users cannot delete conversations'], 403);
        }

        // For regular users, check if they are participant
        if (!$conversation->isParticipant($user->id)) {
            return response()->json(['error' => 'Conversation not found'], 404);
        }

        // Delete all messages in the conversation
        $conversation->messages()->delete();

        // Delete the conversation
        $conversation->delete();

        return response()->json([
            'success' => true,
            'message' => 'Conversation deleted successfully'
        ]);
    }

    /**
     * Get unread conversations count for envelope counter
     */
    public function getUnreadCount()
    {
        $user = auth()->user();

        // Get conversations with unread messages, excluding archived ones for this user
        $conversations = Conversation::where(function($query) use ($user) {
            $query->where('sender_id', $user->id)
                  ->orWhere('receiver_id', $user->id);
        })
        ->where('is_active', true) // Only active conversations
        ->whereHas('messages', function($query) use ($user) {
            $query->where('sender_id', '!=', $user->id)
                  ->whereNull('read_at');
        })
        ->get()
        ->filter(function ($conversation) use ($user) {
            // Exclude conversations that are archived for this user
            return !$conversation->isArchivedForUser($user->id);
        });

        $unreadCount = $conversations->count();

        return response()->json([
            'success' => true,
            'count' => $unreadCount
        ]);
    }
}
