<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Mail\AdminPasswordMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Broadcast;
use App\Events\AccountDeactivated;
use App\Mail\AccountStatusMail;

class AdminController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $admins = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['admin']);
        })->with(['profile', 'roles'])->orderBy('created_at', 'desc')->paginate(10);
        return view('dashboard.admin.admins.index', compact('admins'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        try {
            DB::beginTransaction();
            $randomPassword = Str::random(12);
            $user = User::create([
                'ids' => Str::uuid(),
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($randomPassword),
                'email_verified_at' => now(),
                'approval' => 1,
                'registration_completed' => 1,
                'status' => 1,
            ]);

            // Update profile with image (profile is automatically created by User model boot method)
            $profile = $user->profile;
            $image = 'no_avatar.jpg';
            if ($request->hasFile('image')) {
                $image = $this->storeImage('user-image', $request->file('image'));
            }
            $profile->pic = $image;
            $profile->save();

            // Assign admin role
            $user->assignRole('admin');

            // Send password email
            Mail::to($user->email)->send(new AdminPasswordMail(
                $user->name,
                $user->email,
                $randomPassword
            ));

            DB::commit();

            return redirect()->back()->with([
                'title' => 'Success',
                'message' => 'Admin created successfully! Login credentials have been sent to their email.',
                'type' => 'success'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Error creating admin: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Filter admins based on search, status, and date
     */
    public function filter(Request $request)
    {
        // Use has() instead of filled() to properly handle "0" as a valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $status = $request->has('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $date = $request->has('date') ? trim($request->get('date')) : '';
        $dateFilter = $request->has('date_filter') ? $request->get('date_filter') : null;

        // Base query for admins
        $query = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['admin']);
        })->with(['profile', 'roles']);

        // Apply search filter (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Apply status filter
        if ($status !== '') {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        // Apply date filter
        if ($dateFilter && isset($dateFilter['start_date']) && isset($dateFilter['end_date'])) {
            $startDate = $dateFilter['start_date'];
            $endDate = $dateFilter['end_date'];

            $query->whereDate('created_at', '>=', $startDate)
                  ->whereDate('created_at', '<=', $endDate);
        }

        // Get paginated results
        $admins = $query->orderBy('created_at', 'desc')->paginate(10);

        // Generate HTML for the table
        $html = view('dashboard.admin.admins.partials.admins-table', compact('admins'))->render();

        // Generate pagination HTML
        $pagination = '';
        if ($admins->hasPages()) {
            $pagination = $admins->links('pagination::bootstrap-4')->render();
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'pagination' => $pagination,
            'count' => $admins->total(),
            'has_pages' => $admins->hasPages()
        ]);
    }

    /**
     * Change admin status (activate/deactivate)
     */
    public function changeStatus($id)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['admin']);
        })->where('ids', $id)->firstOrFail();

        if ($user->status == 1) {
            $user->status = 0;
            $message = 'Admin deactivated successfully';

            // Broadcast account deactivation event
            broadcast(new AccountDeactivated($user->id, 'admin', 'deactivated'));

            // Send deactivation email
            \Mail::to($user->email)->send(new \App\Mail\AccountStatusMail($user, 'admin', 'deactivated'));
        } else {
            $user->status = 1;
            $message = 'Admin activated successfully';
        }
        $user->save();

        return redirect()->back()->with(['title' => 'Done', 'message' => $message, 'type' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $user = User::whereHas('roles', function ($q) {
            $q->whereIn('name', ['admin']);
        })->where('ids', $id)->firstOrFail();

        // Broadcast account deletion event before deleting
        broadcast(new AccountDeactivated($user->id, 'admin', 'deleted'));

        // Send deletion email before deleting
        \Mail::to($user->email)->send(new \App\Mail\AccountStatusMail($user, 'admin', 'deleted'));

        $this->deleteImage($user->profile->pic);
        $user->profile->delete();
        $user->delete();
        return redirect()->back()->with(['title' => 'Done', 'message' => 'Admin deleted successfully', 'type' => 'success']);
    }

    /**
     * Check if email is unique in users table
     */
    public function checkEmail(Request $request)
    {
        $email = $request->email;
        $exists = User::where('email', $email)->exists();

        // Return true if email is available (not exists), false if already taken
        return response()->json(!$exists);
    }
}
