@forelse ($discountcoupons as $discountcoupon)
    <tr>
        <td data-label="Coupon name">{{ $discountcoupon->name ?? '' }}</td>
        <td data-label="Coupon code">{{ $discountcoupon->coupon_code ?? '' }}</td>
        <td data-label="Discount">
            @if($discountcoupon->type === 'percentage')
                {{ $discountcoupon->discount ?? '' }}%
            @else
                ${{ $discountcoupon->discount ?? '' }}
            @endif
        </td>
        <td data-label="Usage">{{ $discountcoupon->user_limit ?? '' }}</td>
        <td data-label="Start & End date">
            {{ $discountcoupon->start_date ? $discountcoupon->start_date->format('M d, Y') : '' }}
            -
            {{ $discountcoupon->end_date ? $discountcoupon->end_date->format('M d, Y') : '' }}
        </td>
        <td data-label="">
            <div class="toggle-container">
                <label class="switch">
                    <input type="checkbox" class="toggle-input discount-toggle"
                        data-discount-id="{{ $discountcoupon->ids }}"
                        {{ $discountcoupon->status == 1 ? 'checked' : '' }}>
                    <span class="slider"></span>
                </label>
                <span
                    class="toggle-label">{{ $discountcoupon->status == 1 ? 'Active' : 'Deactive' }}</span>
            </div>
        </td>
        <td data-label="">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    @can('discountcoupons-edit')
                        <li>
                            <a class="dropdown-item complete fs-14 regular "
                                href="{{ route('discount-coupons.edit', $discountcoupon->ids) }}">
                                <i class="bi bi-check-circle complete-icon"></i>
                                Edit
                            </a>
                        </li>
                    @endcan
                    @can('discountcoupons-delete')
                        <li>
                            <form
                                action="{{ route('discount-coupons.destroy', $discountcoupon->ids) }}"
                                method="POST" class="delete-form">
                                @csrf
                                @method('DELETE')
                                <button class="dropdown-item cancel fs-14 regular"
                                    type="button" onclick="showDeleteConfirmation(this)">
                                    <i class="fa-solid fa-xmark cancel-icon"></i>
                                    Delete
                                </button>
                            </form>
                        </li>
                    @endcan
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center">
            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                <i class="fas fa-ticket-alt text-muted mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                <h6 class="text-muted mb-2">No discount coupons found</h6>
                <p class="text-muted small mb-0">Try adjusting your search criteria or filters</p>
            </div>
        </td>
    </tr>
@endforelse
