<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Providers\RouteServiceProvider;
use Illuminate\Foundation\Auth\AuthenticatesUsers;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/dashboard';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }

    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function login(\Illuminate\Http\Request $request)
    {
        $this->validateLogin($request);

        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if (method_exists($this, 'hasTooManyLoginAttempts') &&
            $this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        if ($this->attemptLogin($request)) {
            $user = $this->guard()->user();
            
            // Check admin status before proceeding
            if ($user->hasAnyRole(['admin', 'super admin'])) {
                if ($user->status == 0) {
                    // Admin is inactive, logout and return error
                    $this->guard()->logout();
                    $request->session()->invalidate();
                    $request->session()->regenerateToken();
                    
                    // Debug logging
                    \Log::info('Admin login attempt with inactive account', [
                        'user_id' => $user->id,
                        'is_ajax' => $request->ajax(),
                        'wants_json' => $request->wantsJson(),
                        'accept_header' => $request->header('Accept'),
                        'x_requested_with' => $request->header('X-Requested-With')
                    ]);
                    
                    if ($request->ajax() || $request->wantsJson()) {
                        return response()->json([
                            'error' => true,
                            'title' => 'Account Not Active',
                            'message' => 'Your admin account is not active. Please contact the system administrator.'
                        ], 403);
                    }
                    
                    return redirect()->back()->with([
                        'title' => 'Account Not Active',
                        'message' => 'Your admin account is not active. Please contact the system administrator.',
                        'type' => 'error',
                    ]);
                }
            }
            
            if ($request->hasSession()) {
                $request->session()->put('auth.password_confirmed_at', time());
            }

            // For AJAX requests, return JSON success response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Login successful',
                    'redirect_url' => $this->redirectPath()
                ]);
            }

            return $this->sendLoginResponse($request);
        }

        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }

    /**
     * Get the post-login redirect path based on user role.
     *
     * @return string
     */
    public function redirectPath()
    {
        if (auth()->check()) {
            $user = auth()->user();

            // Check if registration is completed (but not for admins)
            if ($user->registration_completed == 0 && !$user->hasAnyRole(['admin', 'super admin'])) {
                // Redirect to continue registration based on user role
                if ($user->hasRole('customer')) {
                    return route('register.user_type', 'customer');
                } elseif ($user->hasAnyRole(['business', 'individual', 'professional'])) {
                    return route('register.user_type', 'professional');
                }
            }

            // Registration completed, redirect to role-based pages
            if ($user->hasRole('developer')) {
                return '/home';
            } elseif ($user->hasRole('customer')) {
                return '/';
            } elseif ($user->hasAnyRole(['admin', 'super admin', 'business', 'individual', 'professional'])) {
                return '/dashboard';
            }
        }

        return $this->redirectTo;
    }

    /**
     * Send the response after the user was authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Http\JsonResponse
     */
    protected function sendLoginResponse(\Illuminate\Http\Request $request)
    {
        $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        if ($response = $this->authenticated($request, $this->guard()->user())) {
            return $response;
        }

        // Role-based redirection
        $user = $this->guard()->user();

        // Update online status
        $user->update([
            'is_online' => true,
            'online_at' => now()
        ]);

        // Broadcast user online status
        broadcast(new \App\Events\UserOnline($user->id));

        // Check if registration is completed (but not for admins)
        if ($user->registration_completed == 0 && !$user->hasAnyRole(['admin', 'super admin'])) {
            // Redirect to continue registration based on user role
            if ($user->hasRole('customer')) {
                $redirectUrl = route('register.user_type', 'customer');
            } elseif ($user->hasAnyRole(['business', 'individual', 'professional'])) {
                $redirectUrl = route('register.user_type', 'professional');
            } else {
                // For other roles (admin, developer), proceed with normal login
                $redirectUrl = $this->getCompletedRegistrationRedirectUrl($user);
            }
        } else {
            // Registration completed, redirect to role-based pages
            $redirectUrl = $this->getCompletedRegistrationRedirectUrl($user);
        }

        return $request->wantsJson()
                    ? new \Illuminate\Http\JsonResponse([], 204)
                    : redirect()->intended($redirectUrl);
    }

    /**
     * Get redirect URL for users with completed registration
     *
     * @param  \App\Models\User  $user
     * @return string
     */
    private function getCompletedRegistrationRedirectUrl($user)
    {
        if ($user->hasRole('developer')) {
            return '/home';
        } elseif ($user->hasRole('customer')) {
            return '/';
        } elseif ($user->hasAnyRole(['admin', 'super admin', 'business', 'individual', 'professional'])) {
            return '/dashboard';
        } else {
            return $this->redirectPath();
        }
    }
}
