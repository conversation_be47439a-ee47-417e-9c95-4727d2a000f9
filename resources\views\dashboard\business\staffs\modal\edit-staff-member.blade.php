<!-- Modal -->
<div class="modal fade" id="editStaffModal" tabindex="-1" aria-labelledby="editStaffModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold m-0">Edit Staff Member</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editStaffForm" method="POST" enctype="multipart/form-data" class="form-add-services add-card-details">
                @csrf
                @method('PUT')
                <input type="hidden" id="edit-staff-id" name="staff_id">
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-md-12">
                            <label for="description" class="form-label form-input-labels">Thumbnail
                                Image</label>
                            <div class="position-relative ">
                                <div class="image-input image-input-empty" data-kt-image-input="true">
                                    <div class="image-input-wrapper"></div>
                                    <label
                                        class="image-label  flex-column gap-3 align-items-center justify-content-center btn-active-color-primary shadow"
                                        data-kt-image-input-action="change" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Change avatar">
                                        <i class="bi bi-upload upload-icon"></i>
                                        <span>Upload Image</span>
                                        <span>50x50 px</span>

                                        <!--begin::Inputs-->
                                        <input type="file" name="avatar" accept=".png, .jpg, .jpeg" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>

                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="cancel" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Cancel avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Cancel button-->

                                    <!--begin::Remove button-->
                                    <span
                                        class="btn btn-icon btn-circle btn-color-muted btn-active-color-primary w-25px h-25px bg-body shadow"
                                        data-kt-image-input-action="remove" data-bs-toggle="tooltip"
                                        data-bs-dismiss="click" title="Remove avatar">
                                        <i class="ki-outline ki-cross fs-3"></i>
                                    </span>
                                    <!--end::Remove button-->
                                </div>
                                <!--end::Image input-->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="full-name" class="form-label form-input-labels">Full Name</label>
                            <input type="text" class="form-control form-inputs-field" placeholder="Enter full-name"
                                id="edit-name" name="name">
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label form-input-labels">Email Address</label>
                            <input type="email" class="form-control form-inputs-field"
                                placeholder="Enter email address" id="edit-email" name="email">
                        </div>
                        <div class="col-md-6">
                            <label for="category-secondary" class="form-label form-input-labels">Category</label>
                            <select class="form-select form-select-field" data-control="select2"
                                data-dropdown-css-class="w-619px" data-close-on-select="true"
                                data-placeholder="Select an option"
                                data-allow-clear="true" id="edit-category_id" name="category_id">
                                <option></option>
                                @foreach ($categories as $category)
                                    <option value="{{$category->id}}">{{$category->name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="subcategory-secondary" class="form-label form-input-labels">Sub
                                Category</label>
                            <select class="form-select form-select-field" data-control="select2"
                                data-dropdown-css-class="w-619px" data-close-on-select="true"
                                data-placeholder="Select a category first" data-allow-clear="true" id="edit-subcategory_id"
                                name="subcategory_id" disabled>
                                <option></option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label form-input-label">Phone Number</label>
                            <div class="phone-call">
                                <input id="edit-phone" type="tel" placeholder="Phone Number" name="phone" />
                                <input type="hidden" name="country_code" id="edit-country_code">
                                <small class="text-muted">Country code will be automatically included</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <label for="socal-icon-fb" class="form-label form-input-labels">Facebook</label>
                            <input type="text" class="form-control form-inputs-field"
                                placeholder="Enter facebook link" id="edit-facebook" name="facebook">
                        </div>
                        <div class="col-md-6">
                            <label for="socal-icon-insta" class="form-label form-input-labels">Instagram</label>
                            <input type="text" class="form-control form-inputs-field"
                                placeholder="Enter instagram link" id="edit-instagram" name="instagram">
                        </div>
                        <div class="col-md-6">
                            <label for="socal-icon-yt" class="form-label form-input-labels">Youtube</label>
                            <input type="text" class="form-control form-inputs-field"
                                placeholder="Enter youtube link" id="edit-youtube" name="youtube">
                        </div>
                        <div class="col-md-6">
                            <label for="socal-icon-tiktok" class="form-label form-input-labels">Tiktok</label>
                            <input type="text" class="form-control form-inputs-field"
                                placeholder="Enter tiktok link" id="edit-tiktok" name="tiktok">
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="cancel-btn trans-button " data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="add-btn py-2">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

