{{--
                    ///////////////////////
                        SUBCATEGORIES
                    ///////////////////////
--}}
@if($subcategories && $subcategories->count() > 0 && $selectedSubcategory)
<div>
    <ul class="nav nav-pills row-gap-5 pt-5  service-subcategorymb-10" id="pills-tab" role="tablist">

        @foreach ($subcategories as $subcategory)
            <li class="nav-item" role="presentation">
                <button
                    class="nav-link {{ $selectedSubcategory && $selectedSubcategory->slug == $subcategory->slug ? 'active' : '' }} service-tab subcategory-tab-btn"
                    id="pills-subcategory-{{ $subcategory->slug }}-tab" data-bs-toggle="pill"
                    data-bs-target="#pills-fitness" type="button" role="tab"
                    data-category-name="{{ $subcategory->category->slug }}"
                    data-subcategory-name="{{ $subcategory->slug }}" aria-controls="pills-fitness"
                    aria-selected="{{ $selectedSubcategory && $selectedSubcategory->slug == $subcategory->slug ? 'true' : 'false' }}">
                    {{ $subcategory->name }}
                </button>
            </li>
        @endforeach
    </ul>
</div>
@endif

{{-- /////////////////////// SERVICES /////////////////////// --}}

<div class="tab-content mt-10" id="pills-tabContent  ">
    <div class="tab-pane fade show active" id="pills-home" role="tabpanel" aria-labelledby="pills-home-tab"
        tabindex="0">
        <div class="tab-content mt-10" id="pills-tabContent  ">
            <div class="tab-pane fade active show" id="pills-fitness" role="tabpanel"
                aria-labelledby="pills-fitness-tab" tabindex="0">
                <div class="row row-gap-8">
                    @forelse ($services as $service)
                        <div class="col-xl-3 col-lg-4 col-md-6 col-sm-6 col-12">
                            <div class="card top-rated-card services-card h-100">
                                <div class="card-header border-0 p-0 ">
                                    <img src="{{ asset('website' . '/' . $service->image) }}"
                                        onerror="this.src='{{ asset('website/assets/images/default.png') }}'"
                                        class=" top-rated-image" alt="card-image">

                                         @if ($service->is_featured == 1)
                                            <div class="rated-div position-absolute">
                                                <p class="fs-12 sora semi_bold m-0">
                                                    @include('svg.rated') FEATURED
                                                </p>
                                            </div>
                                        @endif
                                </div>
                                <div class="card-body p-5 bg-white">
                                    <p class="fs-16 semi_bold black m-0 ">{{ $service->name }}</p>
                                    <div class="d-flex gap-2 align-items-center">
                                        <img src="{{ asset('website') . '/' . ($service?->user?->profile?->pic ?? '') }}"
                                            onerror="this.src='{{ asset('website/assets/images/default.png') }}'"
                                            class="rounded-pill w-25px h-25px" alt="card-image">
                                        <div>
                                            <p class="fs-11 semi_bold black m-0">
                                                {{ $service->user->profile->company_name }}
                                            </p>
                                            <p class="fs-10px sora semi_bold m-0 light-black"><i
                                                    class="fa-solid fa-star"
                                                    style="color: #000000; font-size: 10px;"></i>{{ $service->averageRating > 0 ? $service->averageRating : 'No rating' }}
                                                <span
                                                    class="normal deep-blue ms-1">({{ $service->totalReviews }})</span>
                                                <span class="light-black opacity-6 ms-1">
                                                    @include('svg.dot')
                                                    {{ $service->physical_location ?? '' }}</span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer border-0 d-flex justify-content-between p-5">
                                    <div>
                                        <p class="m-0 fs-16 black bold">${{ $service->price }}</p>
                                        <p class="m-0 fs-14 regular "><i class="fa-regular fa-clock"></i>
                                            {{ $service->duration }} mins</p>
                                    </div>
                                    @auth
                                        @if (!auth()->user()->hasAnyRole(['admin', 'super admin']) && auth()->user()->hasRole('customer'))
                                            <button type="button" class="blue-button add-to-cart-btn"
                                                data-id="{{ $service->ids }}" data-is-edit="false">
                                                Book Now
                                            </button>
                                        @endif
                                    @else
                                        <button onclick="window.location.href='{{ route('register') }}'"
                                            class="blue-button add-to-cart-btn px-10">Login to book</button>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="col-12">
                            <p class="fs-14 sora light-black normal service-details border-0">No Services Found</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

</div>

@push('js')
    <script>
        $(document).ready(function() {
            $('.daterange').daterangepicker();
            $('input[name="loc"]').on('change', function() {
                var selectedValue = $('input[name="loc"]:checked').val();

                if (selectedValue === "Providers location") {
                    $('#providers-loc').show();
                    $('#home-loc').hide();
                } else if (selectedValue === "Your Home/Office") {
                    $('#providers-loc').hide();
                    $('#home-loc').show();
                }
            });
        });
    </script>
@endpush
