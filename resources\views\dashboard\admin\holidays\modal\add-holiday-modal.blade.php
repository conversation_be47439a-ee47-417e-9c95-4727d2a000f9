<div class="modal fade card-details holiday-form" id="add-holiday" tabindex="-1" aria-labelledby="add-holiday" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <h5 class="fs-15 semi_bold sora black">
                    Add a Holiday</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="holidayForm" action="{{ route('holidays.store') }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="row row-gap-5">
                        <div class="col-lg-12">
                            <label for="event-name" class="form-label form-input-labels">Name</label>
                            <input type="text" class="form-control form-inputs-field d-block" placeholder="Enter name"
                                name="name" value="{{ old('name') }}">
                        </div>
                        <div class="col-md-12">
                            <label for="startdate" class="form-label form-input-labels">Date</label>
                            <div class="flatpickr input-group form-control form-inputs-field" data-wrap="true">
                                <input type="text" id="date" placeholder="Select start date"
                                    data-input name="date" value="{{ old('date') }}">
                                <button class="input-button calender-button" type="button" title="toggle" data-toggle>
                                    <i class="fa-regular fa-calendar"></i>
                                </button>
                            </div>
                            <label id="date-error" class="error" for="date"></label>
                        </div>
                        <div class="col-lg-12">
                            <label for="country-name" class="form-label form-input-labels">Country</label>
                            <select class="form-select form-select-field" id="country_name" name="country_name"
                                data-control="select2" data-placeholder="Select Country">
                                <option selected disabled>Select Country</option>
                                @foreach ($countries as $country)
                                    <option value="{{ $country->country_name }}"
                                        {{ old('country_name') == $country->country_name ? 'selected' : '' }}>
                                        {{ $country->country_name }}
                                    </option>
                                @endforeach
                            </select>
                            <label id="country_name-error" class="error" for="country_name"> </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-0 ">
                    <button type="button" class="cancel-btn" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="save-btn">Add</button>
                </div>
            </form>
        </div>
    </div>
</div>
