<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\ResetsPasswords;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;

    /**
     * Where to redirect users after resetting their password.
     * Override to provide role-based redirects.
     */
    protected function redirectPath()
    {
        // Get the user who just reset their password
        $user = auth()->user();

        if ($user) {
            if ($user->hasRole('developer')) {
                return '/home';
            } elseif ($user->hasRole('customer')) {
                return '/'; // Customer goes to home page
            } elseif ($user->hasAnyRole(['admin', 'business', 'individual', 'professional', 'super admin'])) {
                return '/dashboard'; // All other roles go to dashboard
            }
        }

        // Default fallback
        return '/';
    }
}
