@if ($approved_users->count() > 0)
    @foreach ($approved_users as $user)
        <tr>
            <td class="">
                <div class="d-flex align-items-center gap-5">
                    <div class="card-header p-0 border-0 align-items-start">
                        <img src="{{ asset('website').'/'.$user->profile->pic }}"
                              alt="card-image" />
                    </div>
                    <div class="card-body p-0 ">
                        <p class="fs-16 regular black m-0 pb-2">{{ $user->name }}</p>
                        {{-- <p class="fs-14 semi_bold sora black m-0"><i class="fa-solid fa-star"></i> 5.0 </p> --}}
                    </div>
                    </div>
                </div>
            </td>
            <td data-label="EMAIL ADDRESS">{{ $user->email }}</td>
            <td data-label="SUBSCRIPTION">
                {{ ucfirst($user->activeSubscription->name ?? 'None') }} </td>
            @if ($user->status == 1)
                <td data-label="STATUS" class="professional-status status paid-status">
                 <span class="status-text">   Active </span>
                </td>
            @else
                <td data-label="STATUS" class="professional-status status unpaid-status">
                   <span class="status-text"> Inactive </span>
                </td>
            @endif
            <td data-label="JOINED DATE">
                {{ $user->created_at->format('M d, Y') }}</td>
            <td data-label="TOTAL BOOKINGS">
                0 </td>
            <td data-label="action" class="{{ $user->is_featured ? 'featured-ribbon' : '' }}">
                <div class="dropdown">
                    <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu " aria-labelledby="dropdownMenuButton">
                        <li>
                            <a class="dropdown-item complete fs-14 regular "
                                href="{{ route('professional.show', $user->ids) }}">
                                <i class="bi bi-check-circle complete-icon"></i>
                                View Profile
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item complete fs-14 regular "
                                href="{{ route('professional.change_status', $user->ids) }}">
                                <i class="bi bi-check-circle complete-icon"></i>
                                Change Status
                            </a>
                        </li>
                        <li>
                            <a class="dropdown-item view-icon fs-14 regular "
                                href="{{ route('professional.featured', $user->ids) }}">
                                <i class="bi bi-{{ $user->is_featured ? 'x-circle' : 'check-circle' }} view-icon"></i>
                                {{ $user->is_featured ? 'Remove Featured' : 'Mark as Featured' }}
                            </a>
                        </li>
                        <li>
                            <form action="{{ route('professional.delete', $user->ids) }}" method="POST" class="delete-form">
                                @csrf
                                @method('DELETE')
                                <button class="dropdown-item cancel fs-14 regular" type="button"
                                    onclick="showDeleteConfirmation(this)"> <i class="fa-solid fa-xmark cancel-icon"></i>
                                Delete
                            </button>
                        </form>
                    </li>
                    </ul>
                </div>
            </td>
        </tr>
    @endforeach
@else
    <tr>
        <td colspan="7" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                <h5 class="text-muted mb-2">No approved professionals found</h5>
                <p class="text-muted">Try adjusting your search criteria or filters</p>
            </div>
        </td>
    </tr>
@endif
