<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SocialPlatform extends Model
{
    use HasFactory;
    protected $fillable = [
        'image',
        'name',
        'status',
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    public function userSocials()
    {
        return $this->hasMany(UserSocial::class);
    }
    
}
