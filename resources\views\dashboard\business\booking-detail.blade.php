@extends('dashboard.layout.master')


@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid">
        <div id="kt_app_content_container" class="app-container container padding-block">

            @if (isset($error))
                <div class="alert alert-danger">
                    <h5>Error</h5>
                    <p>{{ $error }}</p>
                    <a href="{{ route('booking') }}" class="btn btn-primary">Back to Bookings sumaiya</a>
                </div>
            @elseif(isset($booking))
                <!-- Header -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h4 class="mb-1">Booking Details</h4>
                                <p class="text-muted mb-0">Booking ID: {{ $booking->booking_number }}</p>
                            </div>
                            <a href="{{ route('booking') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-1"></i> Back to Bookings
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Service and Status Overview -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body p-4">
                                <div class="row align-items-center">
                                    <div class="col-md-8">
                                        <h4 class="mb-2 text-dark">{{ $booking->service->name ?? 'N/A' }}</h4>
                                        <div class="d-flex align-items-center mb-2">
                                            <span class="me-3 text-muted">Status:</span>
                                            @if ($booking->status == 0)
                                                @php
                                                    // Create booking datetime and compare with current datetime
                                                    $bookingDateTime = \Carbon\Carbon::parse(
                                                        $booking->booking_date . ' ' . $booking->booking_time,
                                                    );
                                                    $now = \Carbon\Carbon::now();
                                                    $isFuture = $bookingDateTime->gt($now);
                                                @endphp
                                                @if ($isFuture)
                                                    <span class="badge bg-warning text-dark px-3 py-2 text-white">Upcoming</span>
                                                @else
                                                    <span class="badge bg-info px-3 py-2 text-white">Ongoing</span>
                                                @endif
                                            @elseif($booking->status == 1)
                                                <span class="badge bg-success px-3 py-2 text-white">Completed</span>
                                            @elseif($booking->status == 2)
                                                <span class="badge bg-danger px-3 py-2 text-white">Cancelled</span>
                                            @elseif($booking->status == 3)
                                                <span class="badge bg-success px-3 py-2 text-white">Refunded</span>
                                            @else
                                                <span class="badge bg-danger px-3 py-2 text-white">Denied Refund</span>
                                            @endif
                                        </div>
                                        @if ($booking->status == 2 && $booking->cancel_reason)
                                            <div class="d-flex align-items-center">
                                                <span class="me-3 text-muted">Reason:</span>
                                                <span class="text-danger">{{ $booking->cancel_reason }}</span>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-md-4 text-md-end">
                                        <h2 class="text-success mb-0 fw-bold">
                                            ${{ $booking->price ?? ($booking->total_amount ?? '0') }}</h2>
                                        <small class="text-muted">Total Amount</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Booking Information -->
                <div class="row">
                    <!-- Customer Information -->
                    <div class="col-lg-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-light border-0">
                                <h5 class="mb-0 text-dark">
                                    <i class="bi bi-person-circle me-2 text-dark"></i>
                                    Customer Information
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Name:</strong></div>
                                    <div class="col-8">
                                        {{ $booking->user_id == null ? $booking->client_name : $booking->customer->name ?? 'N/A' }}
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Email:</strong></div>
                                    <div class="col-8">
                                        {{ $booking->user_id == null ? $booking->client_email : $booking->customer->email ?? 'N/A' }}
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-4"><strong class="text-muted">Phone:</strong></div>
                                    <div class="col-8">
                                        {{ $booking->user_id == null ? $booking->client_phone_number : $booking->customer->profile->phone ?? 'N/A' }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Service Information -->
                    <div class="col-lg-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-light border-0">
                                <h5 class="mb-0 text-dark">
                                    <i class="bi bi-gear me-2 text-dark"></i>
                                    Service Details
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Service:</strong></div>
                                    <div class="col-8">{{ $booking->service->name ?? 'N/A' }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Category:</strong></div>
                                    <div class="col-8">{{ $booking->service->category->name ?? 'N/A' }}</div>
                                </div>
                                <div class="row">
                                    <div class="col-4"><strong class="text-muted">Duration:</strong></div>
                                    <div class="col-8">{{ $booking->duration ?? 'N/A' }} minutes</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- Schedule Information -->
                    <div class="col-lg-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-light border-0">
                                <h5 class="mb-0 text-dark">
                                    <i class="bi bi-calendar-event me-2 text-dark"></i>
                                    Schedule
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Date:</strong></div>
                                    <div class="col-8">
                                        {{ \Carbon\Carbon::parse($booking->booking_date)->format('l, d M Y') }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Time:</strong></div>
                                    <div class="col-8">
                                        {{ \Carbon\Carbon::parse($booking->booking_time)->format('H:i') }}</div>
                                </div>
                                <div class="row">
                                    <div class="col-4"><strong class="text-muted">Created:</strong></div>
                                    <div class="col-8">{{ $booking->created_at->format('d M Y') }}</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Provider Information -->
                    <div class="col-lg-6 mb-4">
                        <div class="card border-0 shadow-sm h-100">
                            <div class="card-header bg-light border-0">
                                <h5 class="mb-0 text-dark">
                                    <i class="bi bi-person-badge me-2 text-dark"></i>
                                    Provider Information
                                </h5>
                            </div>
                            <div class="card-body p-4">
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Provider:</strong></div>
                                    <div class="col-8">{{ $booking->service->user->name ?? 'N/A' }}</div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-4"><strong class="text-muted">Email:</strong></div>
                                    <div class="col-8">{{ $booking->service->user->email ?? 'N/A' }}</div>
                                </div>
                                <div class="row">
                                    <div class="col-4"><strong class="text-muted">Business:</strong></div>
                                    <div class="col-8">{{ $booking->service->user->profile->phone ?? 'N/A' }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                @if ($booking->comments || $booking->address)
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card border-0 shadow-sm">
                                <div class="card-header bg-light border-0">
                                    <h5 class="mb-0 text-dark">
                                        <i class="bi bi-info-circle me-2 text-dark"></i>
                                        Additional Information
                                    </h5>
                                </div>
                                <div class="card-body p-4">
                                    @if ($booking->address)
                                        <div class="mb-3">
                                            <div class="row">
                                                <div class="col-3"><strong class="text-muted">Location:</strong></div>
                                                <div class="col-9">{{ $booking->address }}</div>
                                            </div>
                                        </div>
                                    @endif

                                    @if ($booking->comments)
                                        <div class="mb-0">
                                            <div class="row">
                                                <div class="col-3"><strong class="text-muted">Comments:</strong></div>
                                                <div class="col-9">
                                                    <div class="bg-light p-3 rounded border">
                                                        {{ $booking->comments }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
                @if (!auth()->user()->hasAnyRole(['admin', 'super admin']))
                    <!-- Action Buttons -->
                    @if ($booking->status == 0)
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4 text-center">
                                        <h5 class="mb-4 text-dark">Booking Actions</h5>
                                        <div class="d-flex justify-content-center gap-3">
                                            @if ($booking->hasTimePassed())
                                                <button class="btn btn-success btn-lg px-4 booking-action"
                                                    data-booking-id="{{ $booking->id }}" data-action="complete">
                                                    <i class="bi bi-check-circle me-2"></i>
                                                    Mark as Complete
                                                </button>
                                            @endif
                                            @if ($bookingDateTime->isFuture())
                                                <a href="" data-bs-target="#cancelBookingModalLabel"
                                                    data-bs-toggle="modal" data-booking-id="{{ $booking->ids }}"
                                                    class="btn btn-danger btn-lg px-4" type="button">
                                                    <i class="bi bi-x-circle me-2"></i>
                                                    Cancel & Refund Booking
                                                </a>
                                            @endif
                                            {{-- <button class="btn btn-danger btn-lg px-4 booking-action"
                                            data-booking-id="{{ $booking->id }}" data-action="cancel">
                                            <i class="bi bi-x-circle me-2"></i>
                                            Cancel Booking
                                        </button> --}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                @endif
            @else
                <div class="alert alert-warning">
                    <h5>No Data</h5>
                    <p>No booking information available.</p>
                    <a href="{{ route('booking') }}" class="btn btn-primary">Back to Bookings</a>
                </div>
            @endif

        </div>
    </div>

    @include('dashboard.templates.modal.cancel-booking-modal')


@endsection

@push('js')
    <script>
        $(document).on('click', '[data-bs-target="#cancelBookingModalLabel"]', function() {
            const bookingId = $(this).data('booking-id');
            $('#booking_id').val(bookingId);
            $('#cancelReason').val('');
        });
        $(document).ready(function() {
            // Handle booking action buttons
            $('.booking-action').on('click', function(e) {
                e.preventDefault();
                const bookingId = $(this).data('booking-id');
                const action = $(this).data('action');

                const actionText = action === 'complete' ? 'complete' : 'cancel';
                const actionColor = action === 'complete' ? '#10B981' : '#EF4444';
                const actionIcon = action === 'complete' ? 'success' : 'warning';

                Swal.fire({
                    title: `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Booking?`,
                    text: `Are you sure you want to ${actionText} this booking?`,
                    icon: actionIcon,
                    showCancelButton: true,
                    confirmButtonColor: actionColor,
                    cancelButtonColor: '#6B7280',
                    confirmButtonText: `Yes, ${actionText} it!`,
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        updateBookingStatus(bookingId, action);
                    }
                });
            });

            function updateBookingStatus(bookingId, action) {
                Swal.fire({
                    title: 'Processing...',
                    text: 'Updating booking status',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                $.ajax({
                    url: '{{ route('booking.update-status') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        booking_id: bookingId,
                        action: action
                    },
                    success: function(response) {
                        if (response.success) {
                            const actionText = action === 'complete' ? 'completed' : 'cancelled';
                            Swal.fire({
                                title: 'Success!',
                                text: `Booking ${actionText} successfully!`,
                                icon: 'success',
                                confirmButtonColor: '#006AA0',
                                timer: 2000,
                                timerProgressBar: true
                            }).then(() => {
                                location.reload();
                            });
                        } else {
                            Swal.fire({
                                title: 'Error!',
                                text: 'Failed to update booking status',
                                icon: 'error',
                                confirmButtonColor: '#EF4444'
                            });
                        }
                    },
                    error: function(response) {
                        Swal.fire({
                            title: 'Error!',
                            text: response.message || 'Failed to update booking status',
                            icon: 'error',
                            confirmButtonColor: '#EF4444'
                        });
                    }
                });
            }
        });
    </script>
@endpush
