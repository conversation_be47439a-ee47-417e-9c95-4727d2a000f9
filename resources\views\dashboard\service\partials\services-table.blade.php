@forelse ($individual_services as $individual_service)
    <tr>
        <td data-label="" class="">
            <div class="card  flex-row shadow-none p-0 gap-3">
                <div class="card-header p-0 border-0 align-items-start">
                    <img src="{{ asset('website') . '/' . $individual_service->image }}"
                          alt="card-image"
                        onerror="this.src='{{ asset('website/assets/images/default.png') }}'" />
                </div>
                <div class="card-body p-0  d-flex flex-column justify-content-center">
                    <p class="fs-16 regular black mb-0">
                        {{ $individual_service->name }}</p>
                    <p class="light-black opacity-6 mb-0 fs-14 normal">
                        {{ $individual_service->description }}
                    </p>
                </div>
            </div>
        </td>
        <td data-label="Category">
            {{ $individual_service->category->name ?? '-' }}
        </td>
        <td data-label="Duration">
            {{ $individual_service->duration . ' min' }}</td>
        <td data-label="Price">${{ $individual_service->price }}</td>
        @if (auth()->user()->hasRole('business'))
            <td data-label="Assigned Staff">
                @if ($individual_service->staff && $individual_service->staff->count() > 0)
                    @foreach ($individual_service->staff as $staff)
                        <span class="badge bg-primary me-1 text-white">{{ $staff->name }}</span>
                    @endforeach
                @else
                    <span class="text-muted">No staff assigned</span>
                @endif
            </td>
        @endif
        <td data-label="Status">
            <div class="toggle-container">
                <label class="switch">
                    <!-- Dynamically set checked based on subcategory status -->
                    <input type="checkbox" class="toggle-input service-toggle"
                        data-service-id="{{ $individual_service->id }}" {{ $individual_service->status == 1 ? 'checked' : '' }}>
                    <span class="slider"></span>
                </label>
                <span class="toggle-label">{{ $individual_service->status == 1 ? 'Active' : 'Deactive' }}</span>
            </div>
        </td>
        <td data-label="action" class=  '{{ $individual_service->is_featured ? 'featured-ribbon' : '' }} text-white'>
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                    aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    <li>
                        <a href="{{ route('services.edit', ['service' => $individual_service->ids, 'type' => $individual_service->type]) }}"
                            class="dropdown-item complete fs-14 regular ">
                            <i class="bi bi-check-circle complete-icon"></i>
                            Edit
                        </a>
                    </li>

                    <li>
                        <a href="{{ route('services.show', $individual_service->ids) }}"
                            class="dropdown-item complete fs-14 regular view-icon">
                            <i class="bi bi-eye  complete-icon"></i>
                            View
                        </a>
                    </li>
                    @if (auth()->user()->hasAnyRole(['admin', 'super admin']))
                        <li>
                            <a class="dropdown-item view-icon fs-14 regular "
                                href="{{ route('services.featured', $individual_service->ids) }}">
                                <i
                                    class="bi bi-{{ $individual_service->is_featured ? 'x-circle' : 'check-circle' }} view-icon"></i>
                                {{ $individual_service->is_featured ? 'Remove Featured' : 'Mark as Featured' }}
                            </a>
                        </li>
                    @endif

                     <li>
                        <form action="{{ route('services.destroy', $individual_service->ids) }}" method="POST"
                            class="delete-form">
                            @csrf
                            @method('DELETE')
                            <button class="dropdown-item cancel fs-14 regular red" type="button"
                                onclick="showDeleteConfirmation(this)">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Delete
                            </button>
                        </form>
                    </li>
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="7" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Services Found</h5>
            </div>
        </td>
    </tr>
@endforelse
