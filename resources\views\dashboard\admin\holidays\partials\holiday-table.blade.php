@forelse ($holidays as $holiday)
    <tr>
        <td data-label="EVENT TITLE">{{ $holiday->name ?? '' }}</td>
        <td data-label="DATE">{{ $holiday->date ? $holiday->date->format('M d, Y') : '' }}</td>
        <td data-label="COUNTRY">{{ $holiday->country_name ?? '' }}</td>
        <td data-label="">
            <div class="dropdown d-flex justify-content-center">
                <button class="drop-btn" type="button" id="dropdownMenuButton"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                    @can('holidays-edit')
                    <li>
                        <button class="dropdown-item complete fs-14 regular edit-holiday "
                            type="button" data-id="{{ $holiday->ids }}">
                            <i class="bi bi-check-circle complete-icon"></i>
                            Edit
                        </button>
                    </li>
                    @endcan
                    @can('holidays-delete')
                    <li>
                        <form action="{{ route('holidays.destroy', $holiday->ids) }}"
                            method="POST" class="delete-form">
                            @csrf
                            @method('DELETE')
                            <button class="dropdown-item cancel fs-14 regular"
                                type="button" onclick="showDeleteConfirmation(this)">
                                <i class="fa-solid fa-xmark cancel-icon"></i>
                                Delete
                            </button>
                        </form>
                    </li>
                    @endcan
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="4" class="text-center">
            <div class="d-flex flex-column align-items-center justify-content-center py-5">
                <i class="fas fa-calendar-alt text-muted mb-3" style="font-size: 3rem; opacity: 0.3;"></i>
                <h6 class="text-muted mb-2">No holidays found</h6>
                <p class="text-muted small mb-0">Try adjusting your search criteria</p>
            </div>
        </td>
    </tr>
@endforelse
