<!DOCTYPE html>
<html lang="en">

<head>
    <base href="" />
    <title>{{ setting()->title ?? '' }}</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="asset-url" content="{{ asset('website') }}" />
    <meta name="csrf-token" content="{{ csrf_token() }}" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="{{ setting()->title ?? '' }}" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="{{ asset('website') . '/' . setting()->favicon ?? '' }}" />

    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />

    <link href="{{ asset('website') }}/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('website') }}/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <!-- Link Swiper's CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/18.2.1/css/intlTelInput.min.css" /> -->
    <!-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css"/> -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/css/intlTelInput.min.css" rel="stylesheet"> -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/css/intlTelInput.min.css">
    <!-- data-tables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/jquery.dataTables.css" />
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.dataTables.min.css" />
    <link href="{{ asset('website') }}/assets/css/dashboard.css" rel="stylesheet" type="text/css" />
    <link href="{{ asset('website') }}/assets/css/dashboard-responsive.css" rel="stylesheet" type="text/css" />
    @stack('css')
</head>

<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="false"
    data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true"
    data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true"
    class="app-default @if (auth()->check() && auth()->user()->hasRole('customer')) customer_dashboard @endif">

    <div id="secLoader">
        <div class="logo-loader">
            <div class="logo-container">
                <div class="circle">
                    <img src="{{ asset('website') . '/' . setting()->loader_logo }}" class="h-100 w-100 object-fit-contain"
                        alt="logo">
                </div>
                {{-- <img src="{{ asset('website') }}/assets/images/v1_logo.svg" class="h-50 w-50 object-fit-contain"
                    alt="logo"> --}}
            </div>
        </div>
    </div>
    <div class="d-flex flex-column flex-root app-root" id="kt_app_root">
        <div class="app-page flex-column flex-column-fluid" id="kt_app_page">
            <div id="kt_app_header"
                class="app-header
                {{ auth()->check() && auth()->user()->hasRole('customer') && request()->is('cart') ? 'app-header-height' : '' }}
                {{ auth()->check() && !auth()->user()->hasRole('developer') ? 'left-unset' : '' }}
                {{ auth()->check() && auth()->user()->hasAnyRole(['admin', 'super admin']) ? 'admin-sidebar-changes' : '' }}"
                data-kt-sticky="false" data-kt-sticky-activate="{default: false, lg: false}"
                data-kt-sticky-name="app-header-minimize" data-kt-sticky-offset="{default: '0', lg: '0'}"
                data-kt-sticky-animation="false">
                <div class="app-container container-fluid d-flex align-items-stretch justify-content-between"
                    id="kt_app_header_container">
                    <div class="d-flex align-items-center d-lg-none ms-n3 me-1 me-md-2" title="Show sidebar menu">
                        <div class="btn btn-icon w-35px h-25px"
                            id="kt_app_sidebar_mobile_toggle">
                           <i class="fas fa-bars"></i>
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                        </div>
                    </div>
                    <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                        <a href="{{ url('/') }}" class="d-lg-none">
                            <img alt="Logo" src="{{ asset('') }}{{ setting()->favicon ?? '' }}"
                                class="h-30px" />
                        </a>
                    </div>
                    <div class="d-flex align-items-stretch justify-content-start flex-lg-grow-1
                        @if (Auth::check() && Auth::user()->hasAnyRole(['admin', 'super admin'])) admin-sidebar2 @endif
                        {{ auth()->check() && !auth()->user()->hasRole('developer') ? 'flex-column' : '' }}"
                        id="kt_app_header_wrapper">
                        @if (auth()->check() && auth()->user()->hasRole('developer'))
                            @include('dashboard.layout.navbar')
                            @include('dashboard.layout.right_sidebar')
                        @elseif (auth()->check() && !Auth::user()->hasAnyRole(['customer', 'developer']))
                            @include('dashboard.layout.header')
                            {{-- @elseif(auth()->check() && auth()->user()->hasRole('customer') && !request()->is('cart'))
                            @include('website.template.header') --}}
                        @endif
                    </div>
                </div>
            </div>
            <div class="app-wrapper flex-column flex-row-fluid @if (Auth::check() && Auth::user()->hasAnyRole(['admin', 'super admin'])) admin-padding @endif
             {{ auth()->check() && !auth()->user()->hasRole('developer') ? 'margin-left-unset' : '' }} {{ auth()->check() && auth()->user()->hasRole('customer') && request()->is('cart') ? 'app-header-height' : '' }}"
                id="kt_app_wrapper">
                @if (auth()->check() &&
                        auth()->user()->hasanyRole(['developer', 'admin', 'super admin']))
                    @include('dashboard.layout.sidebar')
                @endif
                <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
                    <div class="d-flex flex-column flex-column-fluid">
                        @yield('breadcrumb')
                        @yield('content')
                    </div>
                    @if (auth()->check() && auth()->user()->hasRole('developer'))
                        <div id="kt_app_footer" class="app-footer">
                            <div
                                class="app-container container-fluid d-flex flex-column flex-md-row flex-center flex-md-stack py-3">
                                <div class="text-dark order-2 order-md-1">
                                    <span
                                        class="text-muted fw-semibold me-1">{{ setting()->footer_text ?? '' }}&copy;</span>
                                    {{-- <a href="https://keenthemes.com" target="_blank"
                                        class="text-gray-800 text-hover-primary">Admin</a> --}}
                                </div>
                            </div>
                        </div>
                        {{-- @elseif(auth()->check() && auth()->user()->hasRole('customer')) --}}
                    @elseif(auth()->check() &&
                            auth()->user()->hasRole('customer') &&
                            !request()->is('add-friends') &&
                            !request()->is('customer_transactions') &&
                            !request()->is('profile_setting') &&
                            !request()->is('cart'))
                        @include('website.template.footer')
                    @elseif (auth()->check() &&
                            Auth::user()->hasAnyRole(['individual', 'business', 'admin']) &&
                            !request()->is('add_coupon') &&
                            !request()->is('business_analytics') &&
                            !request()->is('add_staff_member') &&
                            !request()->is('add_services'))
                        @include('dashboard.layout.footer')
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div id="kt_scrolltop" class="scrolltop" data-kt-scrolltop="true">
        <i class="fa-solid fa-chevron-up ">
            <span class="path1"></span>
            <span class="path2"></span>
        </i>
    </div>
    {{-- @include('theme.layout.modal') --}}
    <script>
        var hostUrl = "{{ asset('website') }}/assets/";
    </script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="{{ asset('website') }}/assets/plugins/global/plugins.bundle.js"></script>
    <script src="{{ asset('website') }}/assets/js/scripts.bundle.js"></script>
    <!-- Include Flatpickr JS -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <!-- sweetalert JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <!-- chartjs - using UMD version to avoid module import issues -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <!-- <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/intlTelInput.min.js"></script> -->

    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>
    <script src="{{ asset('website') }}/assets/plugins/custom/formrepeater/formrepeater.bundle.js"></script>
    <script>
        {!! strip_tags(html_entity_decode(setting()->code_snippet ?? '')) !!}
    </script>



    <!-- Phone number -->
    <script>
        const input = document.querySelector("#phone");

        const iti = window.intlTelInput(input, {
            initialCountry: "gb",
            separateDialCode: true,
            preferredCountries: ["gb", "us", "in", "pk"],
            utilsScript:
                "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js",
        });

        // jQuery Validate hook (optional)
        if (window.jQuery && $.validator) {
            $.validator.addMethod(
                "phoneValidation",
                function (value, element) {
                    if (this.optional(element)) return true;
                    return iti.isValidNumber();
                },
                "Please enter a valid phone number"
            );
        }

        // Keep last valid (not-too-long) value to revert on overflow/paste.
        let lastOk = "";

        function digitsOnly(s) {
            return (s || "").replace(/\D/g, "");
        }

        // Compute a conservative max national length using an E.164 example.
        function setMaxLengthFromExample() {
            const u = window.intlTelInputUtils;
            if (!u) return; // utils not ready yet
            const { iso2, dialCode } = iti.getSelectedCountryData();

            // Example in E.164, e.g. "+923001234567"
            const exampleE164 = u.getExampleNumber(
                iso2,
                false,
                u.numberFormat.E164
            );
            const e164Digits = digitsOnly(exampleE164); // e.g. "923001234567"
            const nationalMax =
                e164Digits && dialCode ? e164Digits.length - String(dialCode).length : 15;

            // Backstop: set input's maxLength to this computed cap
            input.maxLength = Math.max(1, nationalMax);
        }

        function clampIfTooLong() {
            const raw = input.value;
            const digits = digitsOnly(raw);

            // Always keep only digits in the visible input
            input.value = digits;

            const u = window.intlTelInputUtils;
            if (!u) {
                // utils.js not ready; remember current sanitized value
                lastOk = input.value;
                return;
            }

            const { dialCode } = iti.getSelectedCountryData();

            // Build an E.164-like candidate and ask libphonenumber if it's TOO_LONG
            const e164Candidate = (dialCode ? "+" + dialCode : "") + digits;
            const reason = u.isPossibleNumberWithReason(e164Candidate);
            const TOO_LONG = u.validationError.TOO_LONG;

            if (reason === TOO_LONG) {
                // Revert to the last value that wasn't too long
                input.value = lastOk;
                return;
            }

            // Accept and remember this value
            lastOk = input.value;
        }

        // Enforce on every change (typing, paste, drag-drop, autofill)
        input.addEventListener("input", clampIfTooLong);

        // When the country changes, reset and recompute caps
        input.addEventListener("countrychange", () => {
            input.value = "";
            lastOk = "";
            setMaxLengthFromExample();

            const countryData = iti.getSelectedCountryData();
            const hidden = document.getElementById("country_code");
            if (hidden) hidden.value = countryData.dialCode;
        });

        // Initial cap (after utils loads it will be refined on the next input/countrychange)
        setMaxLengthFromExample();

        // In case utils loads a bit later, try to set max once more after a tick
        window.addEventListener("load", () => {
            setTimeout(setMaxLengthFromExample, 200);
        });
    </script>










   <!-- <script>
        const input = document.querySelector("#phone");
        if (input) {
            const iti = window.intlTelInput(input, {
                separateDialCode: true,
                initialCountry: "auto",
                utilsScript: "https://cdn.jsdelivr.net/npm/intl-tel-input@25.3.1/build/js/utils.js",
                geoIpLookup: function(success, failure) {
                    fetch("https://ipinfo.io/json?token=YOUR_TOKEN")
                        .then(res => res.json())
                        .then(data => success(data.country))
                        .catch(() => success("us"));
                },
            });
        }
        const phoneElement = document.getElementById('phone');
        if (phoneElement) {
            phoneElement.addEventListener('input', function(e) {
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        }
    </script> -->
    <script type="text/javascript">
        @if (session()->has('message'))
            Swal.fire({
                title: "{{ session()->get('title') ?? 'success!' }}",
                html: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 5000,
                buttons: false,
            });
        @endif
        @if (session()->has('flash_message'))
            Swal.fire({
                title: "{{ @ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message'))) }}",
                icon: "{{ session()->get('type') ?? 'success' }}",
                timer: 5000,
                buttons: false,
            });
        @endif
        @if (session()->has('status'))
            Swal.fire({
                title: "Password Reset Successful!",
                text: "{{ session('status') }}",
                icon: "success",
                confirmButtonText: "OK"
            });
        @endif
        @if (session()->has('status'))
            Swal.fire({
                title: "Password Reset Successful!",
                text: "{{ session('status') }}",
                icon: "success",
                timer: 5000,
                buttons: false,
            });
        @endif
        //delete button confirm swal dynamic.
        function showDeleteConfirmation(button) {
            Swal.fire({
                title: 'Confirm Delete',
                text: 'Are you sure you want to delete this item?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Delete',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    button.closest('.delete-form').submit();
                }
            });
        }

        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>

    <!-- custom-dropown-items -->
    <script>
        // Loop through all dropdown buttons
        document.querySelectorAll('.status-dropdown-button').forEach(button => {
            const dropdown = button.closest('.dropdown');
            const items = dropdown.querySelectorAll('.dropdown-status');

            items.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const label = this.getAttribute('data-label');
                    const color = this.getAttribute('data-color');

                    // Update the button content
                    button.innerHTML = `
                    <span>
                        <span class="dot" style="width:8px; height:8px; border-radius:50%; background:${color}; display:inline-block;"></span> ${label}
                    </span>
                `;
                });
            });
        });

        // data tables-script
        $(document).ready(function() {
            var table = $(".responsiveTable").DataTable({
                dom: "rtip",
                responsive: true,
                scrollX: false,
                paging: true,
                pageLength: 8,
                lengthChange: false,
                initComplete: function() {
                    // Set data-label attributes from header text
                    this.api()
                        .columns()
                        .header()
                        .each(function(header) {
                            var title = $(header).text();
                            $(header).attr("data-label", title);
                        });
                },
            });

            // Update data-labels when table changes
            table.on("draw", function() {
                $("th").each(function() {
                    var title = $(this).text();
                    $(this).attr("data-label", title);
                });
            });

            $("#customSearchInput").on("keyup", function() {
                table.search(this.value).draw();
            });
        });


        // favourite functionality
        $(document).ready(function() {
            $('.fav-icon i').on('click', function() {
                $(this).toggleClass('fa-solid fa-regular text-danger');
            });
        });
    </script>

    <!-- // calender-dropdown-range -->
    <script>
        $(function() {
            var start = moment().subtract(29, 'days');
            var end = moment();

            function cb(start, end) {
                var rangeText = start.format('MMM D, YYYY') + ' - ' + end.format('MMM D, YYYY');
                // Only set values for date pickers that are not specifically excluded
                $('.datePicker').not('#customersDatePicker, #approvedDatePicker, #pendingDatePicker, #holidayDatePicker').val(
                rangeText);
                $('.datePicker').not('#customersDatePicker, #approvedDatePicker, #pendingDatePicker, #holidayDatePicker').attr(
                    'data-range', rangeText);
            }

            // Initialize date pickers except for specific ones that have their own initialization
            $('.datePicker').not('#customersDatePicker, #approvedDatePicker, #pendingDatePicker, #holidayDatePicker, #businessBookingDatePicker').daterangepicker({
                startDate: start,
                endDate: end,
                opens: 'center',
                locale: {
                    format: 'MMM D, YYYY'
                },
                ranges: {
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last Week': [moment().subtract(6, 'days'), moment()],
                    'Last Month': [moment().subtract(29, 'days'), moment()],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1,
                        'month').endOf('month')]
                }
            }, cb);

            // Only call the callback for non-customers date pickers
            cb(start, end);

            $('.down-arrow').on('click', function() {
                var input = $(this).siblings('input.datePicker');
                if (input.data('daterangepicker')) {
                    input.data('daterangepicker').show();
                }
            });

        });
    </script>

    <script>
        // header-swiper
        var swiper = new Swiper(".header-swiper", {
            slidesPerView: 'auto',
            spaceBetween: 5,
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
            // autoplay: {
            //     delay: 3000,
            //     disableOnInteraction: false,
            // }
        });
    </script>

    <!-- toggle-input -->
    <script>
        document.querySelectorAll('.toggle-input').forEach(function(toggleInput) {
            const container = toggleInput.closest('.toggle-container');
            const toggleLabel = container.querySelector('.toggle-label');

            toggleInput.addEventListener('change', function() {
                toggleLabel.textContent = toggleInput.checked ? 'Active' : 'Deactive';
            });
        });
    </script>


    <!-- flat-picker -->
    <script>
        flatpickr(".flatpickr", {
            dateFormat: "Y-m-d",
            allowInput: true,
            wrap: true
        });
    </script>

    <!-- calender -->
    <script>
        $(function() {
            $('#serviceTime').daterangepicker({
                singleDatePicker: true, // We use only the start date
                showDropdowns: false,
                timePicker: true,
                timePicker24Hour: false,
                timePickerIncrement: 15,
                autoUpdateInput: false, // prevent auto-fill
                locale: {
                    format: 'h:mm A'
                }
            }, function(start, end, label) {
                // Extract weekday from selected start date
                const day = start.format('dddd');
                const timeStart = start.format('h:mm A');
                const timeEnd = end.format('h:mm A');

                // Final display text
                const display = `${day}, ${timeStart} - ${timeEnd}`;
                $('#serviceTime').val(display);
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            function toggleDivs() {
                var selectedValue = $('input[name="role"]:checked').val();

                if (selectedValue === "13+ Age") {
                    $('#div-13plus').show();
                    $('#div-under13').hide();
                } else if (selectedValue === "Under 13") {
                    $('#div-under13').show();
                    $('#div-13plus').hide();
                }
            }

            // Initial toggle on page load
            toggleDivs();

            // Toggle on radio button change
            $('input[name="role"]').on('change', function() {
                toggleDivs();
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            function toggleLocationDivs() {
                if ($('#onsite-secondary').is(':checked')) {
                    $('#onsite-location-div').show();
                } else {
                    $('#onsite-location-div').hide();
                }

                if ($('#customer-location-secondary').is(':checked')) {
                    $('#customer-location-div').show();
                } else {
                    $('#customer-location-div').hide();
                }
            }

            // Initial check on page load
            toggleLocationDivs();

            // Update on checkbox change
            $('#onsite-secondary, #customer-location-secondary').on('change', function() {
                toggleLocationDivs();
            });
        });
    </script>
    <!-- add individual services checkbox -->
    <script>
        $(document).ready(function() {
            // Initially check the checkbox states and adjust visibility
            toggleFields();

            // When "On-site" checkbox is toggled
            $('#onsite-secondary').change(function() {
                toggleFields();
            });

            // When "Customer Location" checkbox is toggled
            $('#customer-location-secondary').change(function() {
                toggleFields();
            });

            function toggleFields() {
                // If both "On-site" and "Customer Location" are checked
                if ($('#onsite-secondary').is(':checked') && $('#customer-location-secondary').is(':checked')) {
                    $('#physical-location-field').show();
                    $('#radius-field').show();
                    $('#traveltime-field').show();
                    $('#servicecharges-field').show();
                    $('#thumbnail-secondary-field').show();
                }
                // If only "Customer Location" is checked
                else if ($('#customer-location-secondary').is(':checked')) {
                    $('#radius-field').show();
                    $('#traveltime-field').show();
                    $('#servicecharges-field').show();
                    $('#physical-location-field').hide();
                    $('#thumbnail-secondary-field').hide();
                }
                // If only "On-site" is checked
                else if ($('#onsite-secondary').is(':checked')) {
                    $('#physical-location-field').show();
                    $('#thumbnail-secondary-field').show();
                    $('#radius-field').hide();
                    $('#traveltime-field').hide();
                    $('#servicecharges-field').hide();
                }
                // If neither checkbox is checked
                else {
                    $('#physical-location-field').hide();
                    $('#radius-field').hide();
                    $('#traveltime-field').hide();
                    $('#servicecharges-field').hide();
                    $('#thumbnail-secondary-field').hide();
                }
            }
        });
    </script>

    <script>
        $(document).ready(function() {
            toggleFields();

            $('#onsite-radio').change(function() {
                toggleFields();
            });

            $('#outside-location-radio').change(function() {
                toggleFields();
            });

            function toggleFields() {
                if ($('#onsite-radio').is(':checked')) {
                    $('#physical-location-seconday-field').show();
                    $('#outside-location-field').hide();
                } else if ($('#outside-location-radio').is(':checked')) {
                    $('#outside-location-field').show();
                    $('#physical-location-seconday-field').hide();
                } else {
                    // If no radio button is selected, hide both fields
                    $('#physical-location-seconday-field').hide();
                    $('#outside-location-field').hide();
                }
            }
        });

        // loader
        var loader = document.getElementById("secLoader");
        window.addEventListener("load", function() {
            loader.style.display = "none"
        });

        // repeater for add-booking on booking page
        $('#kt_docs_repeater_basic').repeater({
            initEmpty: false,

            defaultValues: {
                'text-input': 'foo'
            },

            show: function() {
                $(this).slideDown();
            },

            hide: function(deleteElement) {
                $(this).slideUp(deleteElement);
            }
        });
    </script>
    <!-- calender -->
    <script>
        // orginal code
        $(document).ready(function() {

            var isDragging = false;
            var startCell, endCell;

            var titles = ["Mon", "Tues", "Wed", "Thur", "Fri", "Sat", "Sun"];

            function generateCalendar(start_time, end_time) {

                var currentDate = new Date();
                var calendar = '<table><thead><tr><th> </th>';

                // Create column headers for each day
                // Get “today” for later comparison
                var today = new Date();
                var todayString = today.toDateString();

                // Compute the date of this week’s Monday
                var headerDate = new Date();
                headerDate.setDate(headerDate.getDate() - headerDate.getDay() + 1);

                // Open the row
                function renderHeaderRow(startDate) {
                    // var titles = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
                    var calendar = '<thead><tr><th> </th>';
                    var todayString = new Date().toDateString();
                    var headerDate = new Date(startDate);

                    for (var k = 0; k < titles.length; k++) {
                        var d = new Date(headerDate);
                        var dayNum = d.getDate();
                        var isWeekend = (d.getDay() === 6 || d.getDay() === 0);
                        var isToday = (d.toDateString() === todayString);

                        var className = '';
                        if (isWeekend) className += ' weekend-class';
                        if (isToday) className += ' today-class';

                        calendar +=
                            '<th data-day="' + titles[k] + '" class="' + className.trim() + '">' +
                            '<div class="title">' +
                            '<p>' + titles[k] + '</p>' +
                            '<h4>' + dayNum + '</h4>' +
                            '</div>' +
                            '</th>';

                        headerDate.setDate(headerDate.getDate() + 1);
                    }

                    calendar += '</tr></thead>';
                    return calendar;
                }

                // You can now use this inside generateCalendar:
                var currentWeekStart = new Date();
                currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay() + 1);

                var calendar = '<table>';
                calendar += renderHeaderRow(currentWeekStart); // 👈 inject header here
                calendar += '<tbody>';
                calendar += '</tr></thead><tbody>';


                start_time = parseInt(start_time);
                end_time = parseInt(end_time);


                // Prepare today’s string once
                var today = new Date();
                var todayString = today.toDateString();

                // Set the initial date for tempDate to the first day of the week (Monday)
                var tempDateBase = new Date();
                tempDateBase.setDate(tempDateBase.getDate() - tempDateBase.getDay() + 1);

                for (var i = start_time; i <= end_time; i++) {
                    var timeSlot = i * 60; // e.g. 7 * 60 = 420 minutes
                    var formattedTime = formatTime(timeSlot); // e.g. "07:00 AM"

                    calendar += '<tr>';
                    calendar += '<td class="time-col"><strong>' + formattedTime + '</strong></td>';

                    // Clone the week‐start date for this row
                    var tempDate = new Date(tempDateBase);

                    // Loop over days (columns)
                    for (var k = 0; k < titles.length; k++) {
                        var dateString = tempDate.toDateString(); // e.g. "Tue May 13 2025"
                        var court = titles[k] + ' 0' + (k + 1);

                        // weekend?
                        var weekendClass = (tempDate.getDay() === 6 || tempDate.getDay() === 0) ?
                            ' weekend-class' :
                            '';

                        // today?
                        var todayClass = (dateString === todayString) ?
                            ' today-class' :
                            '';

                        calendar +=
                            '<td class="day' + weekendClass + todayClass +
                            '" data-title="' + court +
                            '" data-time="' + formattedTime +
                            '" data-date="' + dateString +
                            '"></td>';

                        // move to next day
                        tempDate.setDate(tempDate.getDate() + 1);
                    }

                    calendar += '</tr>';
                }





                calendar += '</tbody></table>';
                $('#calendar').html(calendar);

                $('#calendar td:not(.title-col)').on({
                    mousedown: function(event) {
                        isDragging = true;
                        startCell = $(this);
                        endCell = null;
                        $('#calendar td').removeClass('start-time end-time in-range');
                        startCell.addClass('start-time');

                        $('.popup').remove();
                    },
                    mouseover: function() {
                        if (isDragging && startCell) {
                            var hoveredCell = $(this);
                            var currentRow = hoveredCell.closest('tr');
                            if (currentRow.is(startCell.closest('tr'))) {
                                $('#calendar td').removeClass('in-range');
                                startCell.nextUntil(hoveredCell).addBack().addClass('in-range');
                                endCell = hoveredCell;
                            }
                        }
                    },
                    mouseup: function(event) {
                        if (isDragging) {
                            if (!endCell) {
                                endCell = startCell;
                            }
                            endCell.addClass('end-time');
                            updateSelectedRange();
                            addDummyEvents();
                            isDragging = false;
                        }
                    }
                });

            }

            function formatTime(minutes) {
                var hours = Math.floor(minutes / 60);
                var remainder = minutes % 60;
                var ampm = hours >= 12 ? 'PM' : 'AM';
                hours = hours % 12;
                hours = hours ? hours : 12;
                var formattedTime = (hours < 10 ? '0' : '') + hours + ':' + (remainder === 0 ? '00' : remainder) +
                    ' ' + ampm;
                return formattedTime;
            }



            function updateSelectedRange() {
                var currentDate = new Date();
                var formattedCurrentDate = currentDate.toISOString().split('T')[0];
                var startTime = startCell ? startCell.data('time') : '';
                var endTime = endCell ? endCell.data('time') : '';
                var startDate = new Date(formattedCurrentDate + ' ' + startTime);
                var endDate = new Date(formattedCurrentDate + ' ' + endTime);
                startDate.setMinutes(startDate.getMinutes() + 15);
                endDate.setMinutes(endDate.getMinutes() + 15);
                var timeDiff = Math.abs(endDate - startDate);
                var totalMinutes = Math.floor(timeDiff / 60000) + 15;
                var hours = Math.floor(totalMinutes / 60);
                var remainingMinutes = totalMinutes % 60;
                var displayText = hours > 0 ? hours + 'h' + (remainingMinutes > 0 ? remainingMinutes + 'm' : '') :
                    remainingMinutes + 'm';
                var plus_15 = new Date('2000-01-01 ' + endTime).setMinutes(new Date('2000-01-01 ' + endTime)
                    .getMinutes() + 15);
                plus_15 = new Date(plus_15).toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                if (endTime.includes(":00") || endTime.includes(":15") || endTime.includes(":30") || endTime
                    .includes(":45")) {
                    endTime = plus_15;
                }
                // var numTimeSlots = totalMinutes / 15;
                // var width = numTimeSlots * 167 + 'px';
                // let popup = '<div class="reservation" style="width:' + width + ';">' +
                var numTimeSlots = totalMinutes / 15;
                var widthClass = 'width-' + numTimeSlots + '-slot';
                let popup = '<div class="reservation ' + widthClass + '">' +
                    '<h4 class="fs-12 semi-bold"> Service Booking </h4>' +
                    '<ul>' +
                    '<li class="profile">  <img src="{{ asset('website') }}/assets/media/avatars/avatar.svg" class="w-15px h-15px rounded-pill"/> Amir Hayun </li>' +
                    '<li> <i class="fa-regular fa-clock"></i> ' + startTime + ' - ' + endTime + ' </li>' +
                    '<li class="status"> <i class="fa-solid fa-user"></i> 2/2 </li>' +
                    '</ul>' +
                    '</div>';
                if ($(endCell).hasClass('end-time')) {
                    $(startCell).html(popup);
                }
            }

            function openPopup(cell) {
                var startTime = cell.data('time');
                var endTime = new Date('2000-01-01 ' + startTime);
                endTime.setMinutes(endTime.getMinutes() + 15);
                endTime = endTime.toLocaleTimeString([], {
                    hour: '2-digit',
                    minute: '2-digit'
                });
                cell.html(popup);
            }


            generateCalendar(7, 14);

        });
    </script>
    <script>
        const now = new Date();
        const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

        let selectedRange = [];

        const calendarInstance = flatpickr("#inline-calendar", {
            inline: true,
            mode: "range",
            dateFormat: "M d, Y",
            onChange: function(selectedDates, dateStr, instance) {
                const [start, end] = selectedDates;

                if (start && (start < firstDay || start > lastDay)) {
                    instance.clear();
                    alert("Please select dates only within this month.");
                } else if (end && (end < firstDay || end > lastDay)) {
                    instance.clear();
                    alert("Please select dates only within this month.");
                } else {
                    selectedRange = selectedDates;
                }
            }
        });

        const cancelBtn = document.getElementById("cancel-btn");
        if (cancelBtn) {
            cancelBtn.addEventListener("click", function() {
                calendarInstance.clear();
                selectedRange = [];
            });
        }

        const applyBtn = document.getElementById("apply-btn");
        if (applyBtn) {
            applyBtn.addEventListener("click", function() {
                if (selectedRange.length === 2) {
                    const [start, end] = selectedRange;
                    alert(`You selected:\nStart: ${start.toDateString()}\nEnd: ${end.toDateString()}`);
                    // Yahan aap selected dates ko backend bhej sakte hain ya use kar sakte hain
                } else {
                    alert("Please select a date range first.");
                }
            });
        }
    </script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            let currentStartDate = new Date(2025, 3, 7); // Start date: 07 April 2025
            const dateRangeElement = document.getElementById("date-range");
            const prevWeekButton = document.getElementById("prev-week");
            const nextWeekButton = document.getElementById("next-week");

            function formatDate(date) {
                const options = {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                return date.toLocaleDateString('en-GB', options);
            }

            function updateDateRange() {
                if (dateRangeElement) {
                    const startDate = formatDate(currentStartDate);
                    const endDate = formatDate(new Date(currentStartDate.getTime() + 6 * 24 * 60 * 60 * 1000));
                    dateRangeElement.innerText = `${startDate} - ${endDate}`;
                }
            }

            // Previous week button event
            if (prevWeekButton) {
                prevWeekButton.addEventListener("click", function() {
                    currentStartDate.setDate(currentStartDate.getDate() - 7);
                    updateDateRange();
                    // Only call renderCalendar if it exists
                    if (typeof renderCalendar === 'function') {
                        renderCalendar(); // Re-render the calendar with new range
                    }
                });
            }

            // Next week button event
            if (nextWeekButton) {
                nextWeekButton.addEventListener("click", function() {
                    currentStartDate.setDate(currentStartDate.getDate() + 7);
                    updateDateRange();
                    // Only call renderCalendar if it exists
                    if (typeof renderCalendar === 'function') {
                        renderCalendar(); // Re-render the calendar with new range
                    }
                });
            }

            // Initial rendering
            updateDateRange();
            // Only call renderCalendar if it exists
            if (typeof renderCalendar === 'function') {
                renderCalendar(); // Initial render of the calendar
            }
        });
    </script>
    <!-- add category button hide show   -->
    <script>
        $(document).ready(function() {
            function toggleAddButtons() {
                const activeTabId = $('#pills-tab .nav-link.active').attr('id');

                if (activeTabId === 'category-tab') {
                    $('#add-category-button').show();
                    $('#add-subcategory-button').hide();
                } else if (activeTabId === 'subcategory-tab') {
                    $('#add-category-button').hide();
                    $('#add-subcategory-button').show();
                } else {
                    // If some other tab is active
                    $('#add-category-button, #add-subcategory-button').hide();
                }
            }

            // Initial check
            toggleAddButtons();

            // On tab change
            $('button[data-bs-toggle="pill"]').on('shown.bs.tab', function() {
                toggleAddButtons();
            });
        });
    </script>
    <!-- analytics -->
    <!-- donut-chart-booking -->
    {{-- <script>
        var ctx4 = document.getElementById('bookingChart');
        var myChart4; // Declare chart variable

        if (ctx4) {
            var blue = '#62B2FD';
            var green = '#9BDFC4';
            var pink = '#F99BAB';
            var lightGray = '#F3F4F6';

            const data4 = {
                labels: ['10:00 - 12:00 PM', '12:00 - 2:00 PM', '2:00 - 4:00 PM'],
                datasets: [{
                    label: 'Booking Analytics',
                    data: [180, 90, 90],
                    backgroundColor: [blue, pink, green],
                    borderColor: '#fff',
                    hoverOffset: 0,
                    borderWidth: 3
                }]
            };

            const config4 = {
                type: 'doughnut',
                data: data4,
                options: {
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    const value = context.raw.toLocaleString();
                                    if (context.dataIndex === 3) {
                                        return '';
                                    }
                                    return `${value} Bookings`;
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '75%',
                }
            };

            myChart4 = new Chart(ctx4, config4);
        }

        // Custom Legend (skip light grey entry)
        function createCustomLegend(chart) {
            const legendContainer = document.getElementById('chartLegend');
            if (legendContainer) {
                const data = chart.data;
                let legendHTML = '';

                data.labels.forEach((label, index) => {
                    if (label !== '') {
                        const color = data.datasets[0].backgroundColor[index];
                        const bookings = data.datasets[0].data[index];

                        legendHTML += `
                                        <div style="display: flex; align-items: center; margin-bottom: 10px; gap: 20px;">
                                            <span style="width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; display: inline-block;"></span> <!-- Increased circle size -->
                                            <div>
                                                <div style="font-size: 14px; font-weight: 500; opacity:0.6; color: #363636;">${label}</div>
                                                <div style="font-size: 16px; font-weight: 500; color: #2A2E33;">${bookings} Bookings</div>
                                            </div>
                                        </div>
                                    `;
                    }
                });

                legendContainer.innerHTML = legendHTML;
            }
        }
        // Only create legend if chart exists
        if (typeof myChart4 !== 'undefined') {
            createCustomLegend(myChart4);
        }
    </script> --}}

    <!-- Pusher for Global Delivery Notifications -->
    @auth
    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <script>
        // Initialize Pusher globally for delivery notifications
        if (typeof Pusher !== 'undefined') {
            window.Pusher = Pusher;
            window.pusher = new Pusher('61adaf56059734aefbeb', {
                cluster: 'ap2',
                encrypted: true,
                auth: {
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    }
                },
                authEndpoint: '/broadcasting/auth'
            });

            // Subscribe to user-specific public channel for notifications
            var currentUserId = '{{ auth()->id() }}';
            if (currentUserId) {
                var userChannel = window.pusher.subscribe('user.' + currentUserId);

                // Listen for new notifications
                userChannel.bind('notification.sent', function(data) {
                    console.log('New notification received:', data);

                    // Increment notification counter immediately
                    incrementNotificationCounter();

                    // Add notification to dropdown
                    addNotificationToDropdown(data);

                    // Show toast notification
                    showNotificationToast(data);
                });

                // Listen for account status changes (deactivation/deletion)
                userChannel.bind('account.status_changed', function(data) {
                    console.log('Account status change received:', data);

                    const isDeleted = data.action === 'deleted';
                    const title = isDeleted ? 'Account Deleted' : 'Account Deactivated';
                    const icon = isDeleted ? 'error' : 'warning';

                    // Show SweetAlert notification
                    Swal.fire({
                        title: title,
                        text: data.message || (isDeleted ? 'Your account has been deleted' : 'Your account has been deactivated'),
                        icon: icon,
                        showConfirmButton: false,
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        timer: 1500,
                        timerProgressBar: true
                    });

                    // Auto logout after 1.5 seconds
                    setTimeout(() => {
                        // Create a form and submit it to logout
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = '/logout';

                        const csrfToken = document.createElement('input');
                        csrfToken.type = 'hidden';
                        csrfToken.name = '_token';
                        csrfToken.value = $('meta[name="csrf-token"]').attr('content');

                        form.appendChild(csrfToken);
                        document.body.appendChild(form);
                        form.submit();
                    }, 1500);
                });
            }
        }
    </script>
    @endauth

    <!-- Global Message Delivery Status Handler -->
    <script>
        // Global delivery status handler - works on any page
        $(document).ready(function() {
            // Only initialize if user is authenticated
            @auth
            // Set up Pusher for global delivery notifications
            if (typeof window.pusher !== 'undefined') {
                const currentUserId = '{{ auth()->id() }}';

                try {
                    // Subscribe to user-specific public channel for delivery notifications
                    const globalUserChannel = window.pusher.subscribe(`user.${currentUserId}`);

                    globalUserChannel.bind('message.received', function(data) {
                        console.log('🔔 FRONTEND: Global message.received event:', data);
                        // Only handle delivery status for messages not from current user
                        if (data.message.sender.id != currentUserId) {
                            console.log(`🔔 FRONTEND: Marking message ${data.message.id} as delivered`);
                            // Mark as delivered via AJAX
                            $.ajax({
                                url: `/chats/messages/${data.message.id}/delivered`,
                                method: 'POST',
                                headers: {
                                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                                },
                                success: function(response) {
                                    console.log(`🔔 FRONTEND: Successfully marked message ${data.message.id} as delivered`);
                                },
                                error: function(xhr, status, error) {
                                    console.error(`🔔 FRONTEND: Error marking message ${data.message.id} as delivered:`, error);
                                }
                            });

                            // Check if this conversation is currently open (only on chat page)
                            if (window.location.pathname.includes('/chats')) {
                                const urlParams = new URLSearchParams(window.location.search);
                                const urlConversationId = urlParams.get('conversation_id');
                                const isConversationOpen = (urlConversationId === data.conversation_ids);

                                if (isConversationOpen) {
                                    $.ajax({
                                        url: `/chats/mark-read/${data.message.conversation_id}`,
                                        method: 'POST',
                                        data: {
                                            _token: $('meta[name="csrf-token"]').attr('content')
                                        },
                                        success: function() {
                                            // Update envelope counter when messages are marked as read
                                            if (typeof ChatApp !== 'undefined' && ChatApp.updateEnvelopeCounterDirect) {
                                                ChatApp.updateEnvelopeCounterDirect();
                                            } else {
                                                updateEnvelopeCounter();
                                            }
                                        }
                                    });
                                }
                            }

                            // Update envelope counter directly
                            if (typeof ChatApp !== 'undefined' && ChatApp.updateEnvelopeCounterDirect) {
                                ChatApp.updateEnvelopeCounterDirect();
                            } else {
                                // Fallback for non-chat pages
                                updateEnvelopeCounter();
                            }

                            // Update sidebar if on chat page
                            if (window.location.pathname.includes('/chats') && typeof ChatApp !== 'undefined') {
                                ChatApp.loadConversations();
                            }
                        }
                    });

                } catch (error) {
                    // Silent error handling
                }
            }
            @endauth

            // Envelope counter functions
            function updateEnvelopeCounter() {
                $.ajax({
                    url: '/chats/unread-count',
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            const count = response.count;
                            const counter = $('#envelope-counter');

                            if (count > 0) {
                                counter.text(count > 99 ? '99+' : count).show();
                            } else {
                                counter.hide();
                            }
                        }
                    }
                });
            }

            // Notification counter functions
            function updateNotificationCounter() {
                $.ajax({
                    url: '/notifications/unread-count',
                    method: 'GET',
                    success: function(response) {
                        if (response.success) {
                            const count = response.count;
                            const counter = $('#notification-counter');

                            if (count > 0) {
                                counter.text(count > 99 ? '99+' : count).show();
                            } else {
                                counter.hide();
                            }
                        }
                    }
                });
            }

            // Increment notification counter
            function incrementNotificationCounter() {
                const counter = $('#notification-counter');
                let currentCount = parseInt(counter.text()) || 0;
                currentCount++;

                if (currentCount > 0) {
                    counter.text(currentCount > 99 ? '99+' : currentCount).show();
                }
            }

            // Decrement notification counter
            function decrementNotificationCounter() {
                const counter = $('#notification-counter');
                let currentCount = parseInt(counter.text()) || 0;
                currentCount = Math.max(0, currentCount - 1);

                if (currentCount > 0) {
                    counter.text(currentCount > 99 ? '99+' : currentCount).show();
                } else {
                    counter.hide();
                }
            }

            // Add notification to dropdown
            function addNotificationToDropdown(data) {
                const notificationList = $('#notification-list');
                const imageUrl = data.sender_image ?
                    '{{ asset("website") }}/' + data.sender_image :
                    '{{ asset("website/no_avatar.jpg") }}';

                const notificationHtml = `
                    <div class="d-flex align-items-center gap-3 justify-content-center border-bottom mb-5 pb-3"
                         data-notification-id="${data.id}"
                         style="cursor: pointer;">
                        <img src="${imageUrl}" alt="Logo" class="img-fluid" style="height: 50px; width: 50px; object-fit: cover; border-radius: 50%;">
                        <div>
                            <p class="fs-12 mb-0">${data.title} - ${data.message.substring(0, 50)}${data.message.length > 50 ? '...' : ''}</p>
                            <p class="fs-12 mb-0">${data.created_at}</p>
                        </div>
                    </div>
                `;

                // Add to top of list
                notificationList.prepend(notificationHtml);

                // Remove "No notifications yet" message if it exists
                notificationList.find('.text-center').remove();

                // Keep only latest 4 notifications in dropdown
                const notifications = notificationList.children('.d-flex');
                if (notifications.length > 4) {
                    notifications.last().remove();
                }
            }

            // Show toast notification
            function showNotificationToast(data) {
                // You can customize this toast notification
                if (typeof toastr !== 'undefined') {
                    toastr.info(data.message, data.title);
                } else {
                    // Fallback to browser notification if toastr is not available
                    if (Notification.permission === 'granted') {
                        new Notification(data.title, {
                            body: data.message,
                            icon: data.sender_image ?
                                '{{ asset("website") }}/' + data.sender_image :
                                '{{ asset("website/no_avatar.jpg") }}'
                        });
                    }
                }
            }

            // Initialize counters on page load
            $(document).ready(function() {
                @auth
                updateEnvelopeCounter();
                updateNotificationCounter();

                // Request notification permission
                if ('Notification' in window && Notification.permission === 'default') {
                    Notification.requestPermission();
                }

                // Handle notification dropdown clicks to mark as read
                $(document).on('click', '#notification-list .d-flex', function() {
                    const notificationElement = $(this);
                    const notificationId = notificationElement.data('notification-id');

                    if (notificationId) {
                        $.ajax({
                            url: '/notifications/' + notificationId + '/mark-read',
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                if (response.success) {
                                    decrementNotificationCounter();
                                    notificationElement.addClass('opacity-50'); // Visual feedback
                                }
                            }
                        });
                    }
                });
                @endauth
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            console.log('ready');
            if ($('td.status').length > 0) {
                $('td.status').each(function() {
                    const $td = $(this);
                    const text = $td.clone().children().remove().end().text().trim();

                    if (text) {
                        $td.contents().filter(function() {
                            return this.nodeType === 3 && this.textContent.trim() !== '';
                        }).remove();

                        $td.prepend('<span class="status-text">' + text + '</span>');
                    }
                });
            }
        });
    </script>

    
    @stack('js')
    
</body>

</html>
