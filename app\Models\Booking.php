<?php

namespace App\Models;

use App\Traits\HasUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;
class Booking extends Model
{
    use HasFactory, HasUuid;

    protected $fillable = [
        'booking_id',
        'booking_number',
        'user_id',
        'service_id',
        'provider_id',
        'booking_date',
        'booking_time',
        'duration',
        'service_price',
        'additional_cost',
        'vat_amount',
        'discount_amount',
        'total_amount',
        'location_type',
        'address',
        'latitude',
        'longitude',
        'comments',
        'required_items',
        'coupon_id',
        'coupon_code',
        'discount_coupon_id',
        'payment_method',
        'payment_status',
        'stripe_payment_intent_id',
        'stripe_session_id',
        'outlook_event_id',
        'google_event_id',
        'status',
        'confirmed_at',
    ];

    /**
     * Get the service that owns the booking.
     */
    public function service()
    {
        return $this->belongsTo(Service::class);
    }

    /**
     * Get the customer that owns the booking.
     */
    public function customer()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the user that owns the booking (alias for customer).
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the provider that owns the booking.
     */
    public function provider()
    {
        return $this->belongsTo(User::class, 'provider_id');
    }

    /**
     * Get the discount coupon that was applied to this booking.
     */
    public function discountCoupon()
    {
        return $this->belongsTo(DiscountCoupon::class);
    }

    /**
     * Get the booking services (professionals assigned to this booking).
     */
    public function bookingServices()
    {
        return $this->hasMany(BookingService::class);
    }

    /**
     * Get the assigned professionals through the pivot table.
     */
    public function assignedProfessionals()
    {
        return $this->hasManyThrough(
            Staff::class,
            BookingService::class,
            'booking_id', // Foreign key on booking_service table
            'id', // Foreign key on staffs table
            'id', // Local key on bookings table
            'staff_id' // Local key on booking_service table
        );
    }

    /**
     * Generate a unique booking ID
     */
    public static function generateBookingId()
    {
        do {
            $bookingId = 'BK' . strtoupper(uniqid());
        } while (self::where('booking_id', $bookingId)->exists());

        return $bookingId;
    }

    /**
     * Get formatted booking date
     */
    public function getFormattedBookingDateAttribute()
    {
        return $this->booking_date ? date('M d, Y', strtotime($this->booking_date)) : null;
    }

    /**
     * Get formatted booking time
     */
    public function getFormattedBookingTimeAttribute()
    {
        return $this->booking_time ? date('h:i A', strtotime($this->booking_time)) : null;
    }

    /**
     * Check if the booking time has passed
     */
    public function hasTimePassed()
    {
        if (!$this->booking_date || !$this->booking_time) {
            return false;
        }

        try {
            // Create proper Carbon datetime from booking_date and booking_time
            $bookingDateTime = \Carbon\Carbon::parse($this->booking_date . ' ' . $this->booking_time);
            $now = \Carbon\Carbon::now();

            return $now->gt($bookingDateTime);
        } catch (\Exception $e) {
            // If parsing fails, assume time has passed for safety
            return true;
        }
    }

    /**
     * Get the selected professionals for this booking
     */
    public function getSelectedProfessionalsAttribute()
    {
        return $this->bookingServices()->with(['staff', 'user'])->get()->map(function ($bookingService) {
            return [
                'id' => $bookingService->staff_id,
                'name' => $bookingService->staff->name,
                'email' => $bookingService->staff->email,
                'phone' => $bookingService->staff->phone,
                'image' => $bookingService->staff->image,
                'user_id' => $bookingService->user_id,
                'user' => $bookingService->user
            ];
        });
    }
    public function setClientNameAttribute($value)
    {
        $this->attributes['client_name'] = $value ? Crypt::encryptString($value) : null;
    }

    public function setClientEmailAttribute($value)
    {
        $this->attributes['client_email'] = $value ? Crypt::encryptString($value) : null;
    }

    public function setClientPhoneNumberAttribute($value)
    {
        $this->attributes['client_phone_number'] = $value ? Crypt::encryptString($value) : null;
    }
    public function getClientNameAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function getClientEmailAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    public function getClientPhoneNumberAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Get the user name for the booking
     * If booking doesn't have stripe_session_id, use client_name, otherwise use user relationship
     */
    public function getUserNameAttribute()
    {
        if (!$this->stripe_session_id) {
            return $this->client_name;
        }

        return $this->user ? $this->user->name : null;
    }

    /**
     * Get the user email for the booking
     * If booking doesn't have stripe_session_id, use client_email, otherwise use user relationship
     */
    public function getUserEmailAttribute()
    {
        if (!$this->stripe_session_id) {
            return $this->client_email;
        }

        return $this->user ? $this->user->email : null;
    }

    /**
     * Get the status text for the booking
     */
    public function getStatusTextAttribute()
    {
        // Handle status 0 with time-based logic
        if ($this->status == 0) {
            try {
                // Parse booking date and time
                $date = $this->booking_date instanceof \Carbon\Carbon ? $this->booking_date : \Carbon\Carbon::parse($this->booking_date);
                $time = $this->booking_time instanceof \Carbon\Carbon ? $this->booking_time : \Carbon\Carbon::parse($this->booking_time);

                // If time is a full datetime, extract just the time part
                if ($time->format('Y-m-d') !== '1970-01-01') {
                    $time = \Carbon\Carbon::createFromTime($time->hour, $time->minute, $time->second);
                }

                $bookingDateTime = $date->copy()->setTime($time->hour, $time->minute, $time->second);
                $now = \Carbon\Carbon::now();

                // If booking time is in the future, it's upcoming
                if ($bookingDateTime->gt($now)) {
                    return 'Upcoming';
                } else {
                    // If booking time is now or in the past, it's ongoing
                    return 'Ongoing';
                }
            } catch (\Exception $e) {
                // If parsing fails, return default for status 0
                return 'Pending';
            }
        }

        // Handle other statuses
        return match($this->status) {
            1 => 'Completed',
            2 => 'Cancelled',
            3 => 'Refunded',
            4 => 'Denied Refund',
            default => '-'
        };
    }
}
