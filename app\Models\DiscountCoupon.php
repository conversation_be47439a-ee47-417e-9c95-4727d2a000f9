<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\HasUuid;
use App\Models\Subscription;

class DiscountCoupon extends Model
{
    use HasFactory, HasUuid;
    protected $table = 'discount_coupons';
    protected $fillable = [
        'user_id',
        'stripe_coupon_id',
        'discount_type',
        'name',
        'coupon_code',
        'type',
        'discount',
        'percentage',
        'user_limit',
        'usage_count',
        'applies_to',
        'duration',
        'start_date',
        'end_date',
        'status'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'status' => 'boolean',
        'discount' => 'integer',
        'percentage' => 'integer',
        'user_limit' => 'integer',
        'usage_count' => 'integer',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class, 'coupon_category', 'coupon_id', 'category_id');
    }

    public function services()
    {
        return $this->belongsToMany(Service::class, 'coupon_service', 'coupon_id', 'service_id');
    }

    public function subscriptions()
    {
        return $this->belongsToMany(Subscription::class, 'coupon_subscription', 'coupon_id', 'subscription_id');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
