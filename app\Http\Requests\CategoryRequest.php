<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name' => 'required|string|max:100',
            'slug' => 'sometimes|string|max:100',
            'status' => 'sometimes|integer|in:0,1',
            'description' => 'required|string',
            'image' => 'sometimes|string|max:255',
            'alt_tag' => 'sometimes|string|max:100',
            'image_description' => 'sometimes|string|max:255',
        ];
    }
}
