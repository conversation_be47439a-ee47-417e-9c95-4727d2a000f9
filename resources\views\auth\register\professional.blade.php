@extends('layouts.app')

@section('head')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        /* Registration Loader Styles */
        #registrationLoader .logo-loader {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        #registrationLoader .logo-container {
            text-align: center;
        }

        #registrationLoader .circle {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #f8f9fa;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: pulse 2s infinite;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .radio-box {
            border: 2px solid transparent;
            transition: border 0.3s ease;
        }

        .radio-box {
            border: 2px solid transparent;
            border-radius: 12px;
            transition: border 0.2s ease;
            background-color: #fff;
            min-height: 120px;
            position: relative;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .radio-box.active {
            border: 2px solid #020C87 !important;
            box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
        }

        .selected-checkmark {
            font-size: 16px;
            color: #020C87;
        }

        .sub_categories {
            margin-top: 20px;
        }

        .custom-checkbox-group {
            padding: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .custom-checkbox-group .custom-checkbox {
            background-color: #f9f9f9;
            border: 1px solid #ccc;
            border-radius: 50px;
            padding: 6px 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }

        .custom-checkbox-group .custom-checkbox input[type="checkbox"] {
            accent-color: #020C87;
        }

        .custom-checkbox-group .custom-checkbox:hover {
            background-color: #eef1ff;
            border-color: #020C87;
        }

        .custom-checkbox-group .custom-checkbox input:checked+.checkbox-label {
            font-weight: 600;
        }

        .checkbox-label {
            margin-left: 8px;
            font-size: 14px;
            white-space: nowrap;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }

            100% {
                transform: scale(1);
            }
        }

        /* Hide steps initially to prevent flash */
        .step {
            display: none;
        }

        .step:first-child {
            display: block;
        }

        /* Phone Input Error Styles */
        .error-input {
            border-color: #dc3545 !important;
            outline: none !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }

        .iti input[type="tel"].error-input {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.25) !important;
        }

        /* Phone Input Styles */
        .iti {
            width: 100%;
        }

        .iti__country-list {
            z-index: 9999;
        }

        .iti__selected-flag {
            padding: 12px 16px;
        }

        .iti input[type="tel"] {
            padding: 12px 16px;
            padding-left: 60px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            font-size: 14px;
            width: 100%;
        }

        .iti input[type="tel"]:focus {
            outline: none;
            border-color: #020C87;
            box-shadow: 0 0 0 2px rgba(2, 12, 135, 0.1);
        }
    </style>
@endsection
@section('content')
    <!-- Registration Loader -->
    <div id="registrationLoader"
        style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(255,255,255,0.9); z-index: 9999; display: flex; justify-content: center; align-items: center;">
        <div class="logo-loader">
            <div class="logo-container">
                <div class="circle">
                    <img src="{{ asset('website') . '/' . setting()->loader_logo }}" class="h-100 w-100 object-fit-contain"
                        alt="logo">
                </div>
            </div>
        </div>
    </div>
    <div class="container professional-acc-form">
        <div class="row justify-content-center">
            <div class="col-md-12 stepper-navigation">
                <ul id="progressbar">
                    <li id="account" class="active"></li>
                    <li id="personal"></li>
                    <li id="payment"></li>
                    <li id="confirm"></li>
                    <li id="confirm"></li>
                </ul>

                <div class="pro-stepper-header d-flex justify-content-between align-items-center">
                    <div>
                        <!-- Home Logo -->
                        <a href="{{ url('/') }}" class="home-logo">
                            <img src="{{ asset('website') . '/' . setting()->logo }}" alt="Home" class="img-fluid"
                                style="height: 60px;">
                        </a>

                        <!-- Previous Button -->
                        <i name="previous" value="ll"
                            class="fas fa-chevron-left previous action-button-previous opacity-0"></i>
                    </div>

                    <!-- Continue Button Container -->
                    <div class="continue-btn-container">
                        <div class="d-flex flex-column">
                            <a class=" blue-border-btn mb-3" href="#" id="logoutBtnSubmit">Logout</a>
                            <input type="button" name="next" class=" next action-button" value="Continue" />
                        </div>
                    </div>

                    <!-- Submit Button Container (for final step) -->
                    <div class="submit-btn-container" style="display: none;">
                        <div class="d-flex flex-column">
                            <a class=" blue-border-btn mb-3" href="#" id="logoutBtnSubmit">Logout</a>
                            <input type="button" class="submit action-button" value="Continue" />
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-8 mb-2 pt-20 mt-10">
                <div class=" px-0 pt-4 pb-0 mt-10 mb-3">
                    <form id="acc-form" method="POST" enctype="multipart/form-data">
                        @csrf
                        <fieldset data-step="1" class="step" id="step-1">
                            @include('dashboard.templates.professional-acc-stepper.step1')
                        </fieldset>

                        <fieldset data-step="2" class="step" id="step-2">
                            @include('dashboard.templates.professional-acc-stepper.step2')
                        </fieldset>

                        <fieldset data-step="3" class="step" id="step-3">
                            @include('dashboard.templates.professional-acc-stepper.step3')
                        </fieldset>

                        <fieldset data-step="4" class="step" id="step-4">
                            @include('dashboard.templates.professional-acc-stepper.step4')
                        </fieldset>

                        <fieldset data-step="5" class="step" id="step-5">
                            @include('dashboard.templates.professional-acc-stepper.step5')
                        </fieldset>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://ajax.microsoft.com/ajax/jquery.validate/1.7/additional-methods.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/intl-tel-input@17.0.19/build/js/intlTelInput.min.js"></script>
    <script
        src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.api_key') }}&libraries=places&v=weekly"
        async defer></script>
    <script>
        let holidayIndex = {{ $holidays->count() }};
        // Initialize certIndex: if there are existing certs, use that count, otherwise 0
        let certIndex = {{ auth()->user()->certificates->count() > 0 ? auth()->user()->certificates->count() - 1 : 0 }};
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/css/intlTelInput.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/intlTelInput.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js"></script>
    <script>
        const input = document.querySelector("#phone");
        let professionalIti = null;

        // Helper function to clear phone errors
        function clearPhoneErrors() {
            const phoneErrors = document.querySelectorAll('#phone-error, [id^="phone-error"]');
            phoneErrors.forEach(error => {
                error.style.display = 'none';
                error.textContent = '';
            });
            if (input) {
                input.classList.remove('error-input');
            }
        }

        // Helper function to show phone error
        function showPhoneError(message) {
            clearPhoneErrors();
            const errorElement = document.getElementById('phone-error');
            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                input.classList.add('error-input');
            }
        }

        if (input) {
            // Check if there's a saved country code from the database
            const savedCountryCode = document.getElementById('country_code').value;

            // Helper function to find country ISO from dial code using API
            async function getCountryIsoFromDialCode(dialCode) {
                try {
                    // Use restcountries API to get country by calling code
                    const response = await fetch(`https://restcountries.com/v3.1/all`);
                    const countries = await response.json();
                    
                    // Find country by dial code
                    const country = countries.find(c => {
                        const callingCodes = c.idd?.root ? 
                            (c.idd.suffixes || ['']).map(suffix => (c.idd.root + suffix).replace('+', '')) : 
                            [];
                        return callingCodes.includes(dialCode);
                    });
                    
                    return country ? country.cca2.toLowerCase() : null;
                } catch (error) {
                    console.error('Error fetching country data:', error);
                    return null;
                }
            }

            // Initialize with auto-detection by default
            professionalIti = window.intlTelInput(input, {
                initialCountry: "auto",
                geoIpLookup: function (callback) {
                    fetch("https://ipinfo.io/json?token=1e240fc8539ff6")
                        .then((resp) => resp.json())
                        .then((resp) => {
                            const countryCode = resp && resp.country ? resp.country : "gb";
                            callback(countryCode);

                            // Set initial country code after initialization (only if no saved code)
                            if (!savedCountryCode) {
                                setTimeout(() => {
                                    const initialCountryData = professionalIti.getSelectedCountryData();
                                    document.getElementById('country_code').value = initialCountryData
                                        .dialCode;
                                }, 100);
                            }
                        })
                        .catch(() => {
                            callback("gb");

                            // Set fallback country code (only if no saved code)
                            if (!savedCountryCode) {
                                setTimeout(() => {
                                    const initialCountryData = professionalIti.getSelectedCountryData();
                                    document.getElementById('country_code').value = initialCountryData
                                        .dialCode;
                                }, 100);
                            }
                        });
                },
                separateDialCode: true,
                preferredCountries: ["gb", "us", "in", "pk"],
                utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.19/js/utils.js"
            });

            // If there's a saved country code, set it after initialization
            if (savedCountryCode) {
                getCountryIsoFromDialCode(savedCountryCode).then(countryIso => {
                    if (countryIso && professionalIti) {
                        professionalIti.setCountry(countryIso);
                    }
                });
            }

            // Allow only numbers and enforce country-specific max length
            input.addEventListener("input", function () {
                this.value = this.value.replace(/\D/g, "");

                // Prevent starting with 0 (not needed with country codes)
                if (this.value.startsWith('0')) {
                    this.value = this.value.substring(1);
                }

                // Get country-specific max length
                const countryData = professionalIti.getSelectedCountryData();
                const maxLength = getPhoneMaxLength(countryData.iso2);

                // Enforce max length
                if (this.value.length > maxLength) {
                    this.value = this.value.substring(0, maxLength);
                }

                // Clear errors on input
                clearPhoneErrors();

                // Real-time validation
                if (this.value.trim()) {
                    if (professionalIti.isValidNumber()) {
                        clearPhoneErrors();
                    } else if (this.value.length > 3) { // Only show error after user has typed a few digits
                        showPhoneError("Please enter a valid phone number");
                    }
                }
            });

            // Handle country change
            input.addEventListener('countrychange', function () {
                clearPhoneErrors();
                const countryData = professionalIti.getSelectedCountryData();
                document.getElementById('country_code').value = countryData.dialCode;

                // Enforce new max length for the selected country
                const maxLength = getPhoneMaxLength(countryData.iso2);
                if (input.value.length > maxLength) {
                    input.value = input.value.substring(0, maxLength);
                }

                // Re-validate if there's a number
                if (input.value.trim()) {
                    setTimeout(() => {
                        if (!professionalIti.isValidNumber()) {
                            showPhoneError("Please enter a valid phone number");
                        }
                    }, 100);
                }
            });

            // Final validation on blur
            input.addEventListener("blur", function () {
                if (this.value.trim()) {
                    if (!professionalIti.isValidNumber()) {
                        showPhoneError("Please enter a valid phone number");
                    }
                }
            });
        }

        // Function to restore country selection from saved data
        function restorePhoneCountry() {
            if (professionalIti && document.getElementById('country_code').value) {
                const savedCountryCode = document.getElementById('country_code').value;
                // Find country by dial code and set it
                const countryData = professionalIti.getSelectedCountryData();
                if (countryData.dialCode !== savedCountryCode) {
                    // Try to set the country by dial code
                    const allCountries = window.intlTelInputGlobals.getCountryData();
                    const targetCountry = allCountries.find(country => country.dialCode === savedCountryCode);
                    if (targetCountry) {
                        professionalIti.setCountry(targetCountry.iso2);
                    }
                }
            }
        }

        // Call restore function after a short delay to ensure ITI is fully initialized
        setTimeout(restorePhoneCountry, 500);

        // Function to get max phone length based on country (international format, no leading 0)
        function getPhoneMaxLength(countryCode) {
            const phoneLengths = {
                'us': 10,
                'ca': 10, // North America (no leading 0)
                'gb': 10,
                'ie': 9, // UK & Ireland (without leading 0)
                'de': 11,
                'fr': 9,
                'it': 10,
                'es': 9,
                'nl': 9, // Western Europe (without leading 0)
                'au': 9,
                'nz': 9, // Oceania (without leading 0)
                'in': 10,
                'pk': 10,
                'bd': 10, // South Asia (without leading 0)
                'cn': 11,
                'jp': 10,
                'kr': 10, // East Asia (without leading 0)
                'br': 11,
                'ar': 10,
                'mx': 10, // Latin America
                'za': 9,
                'ng': 10,
                'eg': 10, // Africa (without leading 0)
                'ae': 9,
                'sa': 9,
                'tr': 10, // Middle East (without leading 0)
                'ru': 10,
                'pl': 9,
                'se': 9,
                'no': 8,
                'dk': 8,
                'fi': 9, // Northern/Eastern Europe (without leading 0)
                'ch': 9,
                'at': 10,
                'be': 9,
                'pt': 9, // Central Europe (without leading 0)
                'sg': 8,
                'my': 9,
                'th': 9,
                'ph': 10,
                'id': 11,
                'vn': 9, // Southeast Asia (without leading 0)
            };

            return phoneLengths[countryCode?.toLowerCase()] || 14; // Default to 14 if country not found
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const allCategories = document.querySelectorAll('.service_category');

            // Initially hide all subcategories
            document.querySelectorAll('.sub_category').forEach(sub => sub.style.display = 'none');

            allCategories.forEach(category => {
                const radioBox = category.querySelector('.radio-box');
                const categoryId = category.getAttribute('data-ctg');
                const subCategory = document.querySelector(`#sub_category_${categoryId}`);
                const checkboxes = subCategory?.querySelectorAll('input[type="checkbox"]') || [];

                // Show only this subcategory when clicked
                radioBox.addEventListener('click', () => {
                    document.querySelectorAll('.sub_category').forEach(sc => sc.style.display =
                        'none');
                    if (subCategory) subCategory.style.display = 'block';
                });

                // Only update this category’s border
                const updateBorder = () => {
                    const isSelected = [...checkboxes].some(cb => cb.checked);
                    radioBox.style.border = isSelected ? '2px solid #020C87' : '2px solid transparent';
                };

                checkboxes.forEach(cb => {
                    cb.addEventListener('change', function () {
                        const checkedCount = [...checkboxes].filter(c => c.checked).length;
                        updateBorder();
                    });
                });

                // Initial state on load
                updateBorder();
            });
        });
    </script>
    <script>
        $.validator.addMethod("phoneValidation", function (value, element) {
            if (this.optional(element)) {
                return true;
            }

            if (professionalIti) {
                if (professionalIti.isValidNumber()) {
                    return true;
                } else {
                    // Get the error code from intl-tel-input
                    let errorCode = professionalIti.getValidationError();
                    let msg = "Invalid phone number";

                    // Friendly error messages for all countries
                    switch (errorCode) {
                        case intlTelInputUtils.validationError.TOO_SHORT:
                            msg = "This number is too short.";
                            break;
                        case intlTelInputUtils.validationError.TOO_LONG:
                            msg = "This number is too long.";
                            break;
                        case intlTelInputUtils.validationError.INVALID_LENGTH:
                            msg = "This number has an invalid length.";
                            break;
                        case intlTelInputUtils.validationError.INVALID_COUNTRY_CODE:
                            msg = "This number has an invalid country code.";
                            break;
                        case intlTelInputUtils.validationError.INVALID_NUMBER:
                            msg = "This number is not valid for the selected country.";
                            break;
                    }

                    // Set the message dynamically
                    $.validator.messages.phoneValidation = msg;
                    return false;
                }
            }

            return false;
        }, "Please enter a valid phone number");

        // Add custom validation method for file size
        $.validator.addMethod("filesize", function (value, element, param) {
            if (element.files && element.files.length > 0) {
                return element.files[0].size <= param;
            }
            return true; // If no file selected, let required rule handle it
        }, "File size must be less than {0} bytes");

        // Initialize jQuery validation
        function initializeValidation() {
            $("#acc-form").validate({
                ignore: ':hidden:not(select)',
                success: function (label) {
                    label.remove()
                },
                errorClass: "error",
                errorElement: "label",
                errorPlacement: function (error, element) {
                    // Remove any existing error for this element first
                    element.siblings('label.error').remove();

                    error.insertAfter(element);
                    error.css({
                        'color': 'red',
                        'font-weight': 'bold',
                        'display': 'block',
                        // 'margin-top': '-20px',
                        'margin-bottom': '5px'
                    });
                },
                rules: {
                    name: {
                        required: true,
                        maxlength: 50
                    },
                    company_name: {
                        required: true,
                        maxlength: 100
                    },
                    email: {
                        required: true,
                        email: true
                    },
                    phone: {
                        required: true,
                        phoneValidation: true
                    },
                    website: {
                        required: true,
                        url: true
                    },
                    facebook: {
                        required: true,
                        url: true
                    },
                    instagram: {
                        required: true,
                        url: true
                    },
                    tiktok: {
                        required: true,
                        url: true
                    },
                    location: {
                        required: true
                    },
                    location_service: {
                        required: true,
                        number: true,
                        min: 1
                    },
                    country: {
                        required: true
                    },
                    company_id: {
                        required: true
                    },
                    vat_number: {
                        required: true,
                        number: true
                    },
                    avatar: {
                        required: function() {
                            // Check if there's an existing image using the hidden input
                            var hasExistingImage = $('#has_existing_image').val() === '1';
                            return !hasExistingImage; // Required only if no existing image
                        },
                        filesize: 5242880 // 5MB in bytes (5 * 1024 * 1024)
                    }
                },
                messages: {
                    name: {
                        required: "Name is required",
                        maxlength: "Name cannot exceed 50 characters"
                    },
                    company_name: {
                        required: "Company name is required",
                        maxlength: "Company name cannot exceed 100 characters"
                    },
                    email: {
                        required: "Email is required",
                        email: "Please enter a valid email address"
                    },
                    phone: {
                        required: "Phone number is required"
                    },
                    website: {
                        required: "Website is required",
                        url: "Please enter a valid website URL"
                    },
                    facebook: {
                        required: "Facebook URL is required",
                        url: "Please enter a valid Facebook URL"
                    },
                    instagram: {
                        required: "Instagram URL is required",
                        url: "Please enter a valid Instagram URL"
                    },
                    tiktok: {
                        required: "TikTok URL is required",
                        url: "Please enter a valid TikTok URL"
                    },
                    location: {
                        required: "Location is required"
                    },
                    location_service: {
                        required: "Service radius is required",
                        number: "Please enter a valid number",
                        min: "Service radius must be at least 1 km"
                    },
                    country: {
                        required: "Country is required"
                    },
                    company_id: {
                        required: "Company ID is required"
                    },
                    vat_number: {
                        required: "VAT number is required",
                        number: "Please enter a valid VAT number"
                    },
                    avatar: {
                        required: "Profile picture is required",
                        filesize: "Profile picture must be less than 5MB"
                    }
                }
            });
        }
    </script>
    <script>
        const fileInput = document.querySelector('input[type="file"]');
        const wrapper = document.querySelector('.image-input-wrapper');
        const removeBtn = document.querySelector('[data-kt-image-input-action="remove"]');
        const cancelBtn = document.querySelector('[data-kt-image-input-action="cancel"]');
        const imageInput = document.querySelector('.image-input');

        fileInput.addEventListener('change', function (e) {
            const file = e.target.files[0];

            if (file) {
                const reader = new FileReader();

                reader.onload = function (event) {
                    wrapper.style.backgroundImage = `url('${event.target.result}')`;
                    imageInput.classList.remove('image-input-empty');
                };

                reader.readAsDataURL(file);

                // Trigger validation for the avatar field
                $(fileInput).valid();
            }
        });

        // Remove action
        removeBtn.addEventListener('click', function () {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
            // Set hidden input for backend if needed
            document.querySelector('input[name="avatar_remove"]').value = '1';
        });

        // Optional: Cancel action (if you need to reset back to default image, add that logic)
        cancelBtn.addEventListener('click', function () {
            wrapper.style.backgroundImage = '';
            fileInput.value = '';
            imageInput.classList.add('image-input-empty');
        });
    </script>

    <script>
        $(document).ready(function () {
            var current_fs, next_fs, previous_fs;
            var opacity;
            var current = 1;
            var steps = $("fieldset").length;

            // Initialize jQuery validation
            initializeValidation();

            // Validate current step using jQuery validation (only for step 1)
            function validateCurrentStep($fieldset) {
                $fieldset.find('label.error').remove();
                $fieldset.find('.field-error').remove();
                $fieldset.find('.invalid-feedback').remove();
                $fieldset.find('.error').removeClass('error');
                var stepId = $fieldset.attr('id');

                if (stepId === 'step-1') {
                    // Clear all previous errors
                    $fieldset.find('.error').removeClass('error');
                    $fieldset.find('label.error').remove(); // Remove jQuery validation errors
                    $fieldset.find('.field-error').remove(); // Remove any custom error spans

                    var isValid = true;
                    var fieldsToValidate = ['name', 'company_name', 'email', 'phone', 'website', 'facebook',
                        'instagram',
                        'tiktok', 'location', 'location_service', 'country', 'company_id', 'vat_number'
                    ];
                    fieldsToValidate.forEach(function (fieldName) {
                        var field = $fieldset.find('[name="' + fieldName + '"]');
                        if (field.length && !field.valid()) {
                            isValid = false;
                        }
                    });
                    // Avatar validation - check both required and file size
                    var avatarField = $fieldset.find('[name="avatar"]');
                    if (avatarField.length) {
                        var file = avatarField[0].files[0];
                        var avatarSpan = $fieldset.find('span:contains("Upload Profile Picture")');
                        
                        // Check if there's an existing image using the hidden input
                        var hasExistingImage = $('#has_existing_image').val() === '1';

                        if (!file && !hasExistingImage) {
                            // No file selected and no existing image - show required error
                            if (avatarSpan.length) {
                                var errorLabel = $('<label class="error">Profile picture is required</label>');
                                errorLabel.css({
                                    'color': 'red',
                                    'font-weight': 'bold',
                                    'display': 'block',
                                    'margin-top': '5px'
                                });
                                errorLabel.insertAfter(avatarSpan);
                            }
                            isValid = false;
                        } else if (file && file.size > 5242880) {
                            // File too large - show size error
                            if (avatarSpan.length) {
                                var errorLabel = $(
                                    '<label class="error">Profile picture must be less than 5MB</label>');
                                errorLabel.css({
                                    'color': 'red',
                                    'font-weight': 'bold',
                                    'display': 'block',
                                    'margin-top': '5px'
                                });
                                errorLabel.insertAfter(avatarSpan);
                            }
                            isValid = false;
                        }
                    }
                    return isValid;
                } else {
                    // For steps 2, 3, 4, and 5, use the validateFields function
                    if (typeof validateFields === 'function') {
                        var hasError = validateFields($fieldset);
                        return !hasError; // Return true if no errors (validation passes)
                    }
                    return true;
                }
            }

            // Load saved progress on page load
            loadSavedProgress();

            setProgressBar(current);
            toggleSubmitButton(current); // Initial check

            $(".next").click(function () {
                current_fs = $("fieldset:visible");

                // Validate current step before proceeding using jQuery validation
                if (!validateCurrentStep(current_fs)) {
                    return false; // Stop if validation fails
                }

                // Disable continue button for step 5 to prevent multiple clicks
                if (current_fs.data('step') == 5) {
                    $(this).prop('disabled', true).val('Processing...');
                }

                // Save current step data before proceeding
                saveStepData(current_fs.data('step'), function (success, redirectUrl) {
                    if (success) {
                        // Clear localStorage when step 1 is successfully saved
                        if (current_fs.data('step') === 1) {
                            clearFormDataFromStorage();
                        }
                        proceedToNextStep();
                    } else {
                        // alert('Failed to save step data. Please try again.');
                    }
                });
            });

            function proceedToNextStep() {
                current_fs = $("fieldset:visible");
                next_fs = current_fs.next("fieldset");
                if (next_fs.length === 0) return;

                $("#progressbar li").eq($("fieldset").index(next_fs)).addClass("active");

                next_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        next_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });

                if ($('.action-button-previous').hasClass('opacity-0')) {
                    $('.action-button-previous').removeClass('opacity-0');
                }

                setProgressBar(++current);
                toggleSubmitButton(current);

                // Initialize category selection if we're on step 2
                if (next_fs.attr('id') === 'step-2') {
                    setTimeout(function () {
                        if (typeof initializeCategorySelection === 'function') {
                            initializeCategorySelection();
                        }
                    }, 600); // Wait for animation to complete
                }
            }

            function saveStepData(stepNumber, callback) {
                // Capture country code before serializing if we're on step 1 and have phone input
                if (stepNumber === 1 && professionalIti) {
                    const countryData = professionalIti.getSelectedCountryData();
                    $('#country_code').val(countryData.dialCode);
                }

                const formArray = $('#acc-form').serializeArray();
                const formData = new FormData();

                // Group array fields properly
                const groupedData = {};
                formArray.forEach(function (field) {
                    if (field.name.endsWith('[]')) {
                        const baseName = field.name.slice(0, -2);
                        if (!groupedData[baseName]) {
                            groupedData[baseName] = [];
                        }
                        groupedData[baseName].push(field.value);
                    } else {
                        formData.append(field.name, field.value);
                    }
                });

                // Append grouped array data
                Object.keys(groupedData).forEach(function (key) {
                    groupedData[key].forEach(function (value) {
                        formData.append(key + '[]', value);
                    });
                });

                // Handle file inputs separately (serializeArray doesn't capture files)
                const fileInputs = $('#acc-form input[type="file"]');

                fileInputs.each(function (index, input) {
                    const files = input.files;

                    if (files && files.length > 0) {
                        for (let i = 0; i < files.length; i++) {
                            formData.append(input.name, files[i]);
                        }
                    }
                });

                // Add step number and CSRF token
                formData.append('step', stepNumber);
                formData.append('_token', '{{ csrf_token() }}');

                $.ajax({
                    url: '{{ route('register.professional.save_step') }}',
                    type: 'POST',
                    data: formData,
                    processData: false, // Important for file uploads
                    contentType: false, // Important for file uploads
                    success: function (response) {
                        if (response.success) {
                            // Check if this is step 5 completion (logout scenario)
                            if (response.logout) {
                                Swal.fire({
                                    title: response.title || "Success",
                                    text: response.message,
                                    icon: "success",
                                    confirmButtonText: "OK"
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        window.location.href = response.redirect || '/';
                                    }
                                });
                                callback(false); // Don't proceed to next step
                            } else {
                                callback(true, response.redirect);
                            }
                        } else {
                            callback(false);
                            Swal.fire({
                                title: "Error",
                                text: response.message,
                                icon: "error"
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('AJAX Error:', error);
                        console.error('Response:', xhr.responseText);
                        callback(false);
                    }
                });
            }

            function loadSavedProgress() {
                $.ajax({
                    url: '{{ route('register.professional.get_progress') }}',
                    type: 'GET',
                    success: function (response) {
                        if (response.success && response.current_step) {
                            current = parseInt(response.current_step);

                            // Hide all steps
                            $("fieldset").hide();

                            // Show current step
                            $(`#step-${current}`).show();

                            // Update progress bar
                            for (let i = 1; i <= current; i++) {
                                $("#progressbar li").eq(i - 1).addClass("active");
                            }

                            setProgressBar(current);
                            toggleSubmitButton(current);

                            // Show previous button if not on first step
                            if (current > 1) {
                                $('.action-button-previous').removeClass('opacity-0');
                            }


                        }

                        // Hide loader after progress is loaded
                        $('#registrationLoader').fadeOut(300);
                    },
                    error: function () {
                        console.log('Could not load saved progress');
                        // Hide loader even on error
                        $('#registrationLoader').fadeOut(300);
                    }
                });
            }



            // Handle calendar connection buttons - save form data before navigating
            $(document).on('click', '#googleCalendarBtn, #outlookCalendarBtn', function (e) {
                e.preventDefault();
                const targetUrl = $(this).attr('href');

                // Save current form data to localStorage
                saveFormDataToStorage();

                // Navigate to calendar connection
                window.location.href = targetUrl;
            });

            function saveFormDataToStorage() {
                const formData = {};

                // Capture all form fields from step 1
                $('#step-1 input, #step-1 select, #step-1 textarea').each(function () {
                    const field = $(this);
                    const name = field.attr('name');
                    const type = field.attr('type');

                    if (name && type !== 'file') {
                        if (type === 'checkbox' || type === 'radio') {
                            formData[name] = field.is(':checked');
                        } else {
                            formData[name] = field.val();
                        }
                    }
                });

                // Also capture country code from phone input
                if (professionalIti) {
                    const countryData = professionalIti.getSelectedCountryData();
                    formData['country_code'] = countryData.dialCode;
                }

                // Store in localStorage
                localStorage.setItem('professional_step1_data', JSON.stringify(formData));
                console.log('Saved form data to localStorage:', formData);
            }

            function restoreFormDataFromStorage() {
                const savedData = localStorage.getItem('professional_step1_data');
                if (savedData) {
                    try {
                        const formData = JSON.parse(savedData);
                        console.log('Restoring form data from localStorage:', formData);

                        // Restore form fields
                        Object.keys(formData).forEach(function (name) {
                            const field = $(`[name="${name}"]`);
                            const value = formData[name];

                            if (field.length) {
                                const type = field.attr('type');

                                if (type === 'checkbox' || type === 'radio') {
                                    field.prop('checked', value);
                                } else {
                                    field.val(value);
                                }
                            }
                        });

                        // Restore phone country selection
                        if (formData.country_code && professionalIti) {
                            setTimeout(() => {
                                restorePhoneCountry();
                            }, 300);
                        }

                        // Trigger change events for select2 dropdowns
                        $('#step-1 select').trigger('change');

                        // Reinitialize map if location data was restored
                        if (formData.location && formData.lat && formData.lng) {
                            setTimeout(() => {
                                if (typeof initIndividualServiceMap === 'function') {
                                    initIndividualServiceMap();
                                }
                            }, 500);
                        }

                    } catch (e) {
                        console.error('Error restoring form data:', e);
                    }
                }
            }

            function clearFormDataFromStorage() {
                localStorage.removeItem('professional_step1_data');
                console.log('Cleared form data from localStorage');
            }

            // Restore form data on page load
            setTimeout(() => {
                restoreFormDataFromStorage();
            }, 1000); // Wait for all initializations to complete

            $(".previous").click(function () {
                // Prevent going back if we're already on step 1
                if (current <= 1) {
                    return false;
                }

                current_fs = $("fieldset:visible");
                previous_fs = current_fs.prev("fieldset");

                if (previous_fs.length === 0) return;

                // Check if we're going to step 1 and hide the previous button after transition
                var targetStepNum = previous_fs.attr('data-step');
                if (targetStepNum == '1') {
                    setTimeout(function () {
                        $('.action-button-previous').addClass('opacity-0');
                    }, 500); // Wait for animation to complete
                }

                $("#progressbar li").eq($("fieldset").index(current_fs)).removeClass("active");

                previous_fs.show();
                current_fs.animate({
                    opacity: 0
                }, {
                    step: function (now) {
                        opacity = 1 - now;
                        current_fs.css({
                            'display': 'none',
                            'position': 'relative'
                        });
                        previous_fs.css({
                            'opacity': opacity
                        });
                    },
                    duration: 500
                });

                setProgressBar(--current);
                toggleSubmitButton(current);

                // Initialize category selection if we're going back to step 2
                if (previous_fs.attr('id') === 'step-2') {
                    setTimeout(function () {
                        if (typeof initializeCategorySelection === 'function') {
                            initializeCategorySelection();
                        }
                    }, 600); // Wait for animation to complete
                }

                // Restore phone country if we're going back to step 1
                if (previous_fs.attr('id') === 'step-1') {
                    setTimeout(function () {
                        restorePhoneCountry();
                    }, 600); // Wait for animation to complete
                }
            });

            function setProgressBar(curStep) {
                var percent = parseFloat(100 / steps) * curStep;
                percent = percent.toFixed();
                $(".progress-bar").css("width", percent + "%");
            }

            function toggleSubmitButton(currentStep) {
                console.log('toggleSubmitButton called with step:', currentStep, 'total steps:', steps);
                if (currentStep === steps) {
                    console.log('Showing submit button, hiding continue button');
                    $('.submit-btn-container').show();
                    $('.continue-btn-container').hide();
                } else {
                    console.log('Showing continue button, hiding submit button');
                    $('.submit-btn-container').hide();
                    $('.continue-btn-container').show();
                }
            }

            $(".submit").click(function (e) {
                e.preventDefault();

                // Validate current step (step 5) before submission
                current_fs = $("fieldset:visible");
                if (!validateCurrentStep(current_fs)) {
                    return false; // Stop if validation fails
                }

                // Save step 5 data and handle completion
                saveStepData(current_fs.data('step'), function (success, redirectUrl) {
                    if (success) {
                        // Clear localStorage when registration is completed
                        clearFormDataFromStorage();

                        // Step 5 data saved successfully
                        if (redirectUrl) {
                            window.location.href = redirectUrl;
                        } else {
                            window.location.href = '{{ route('dashboard') }}';
                        }
                    } else {
                        // The saveStepData function will handle the SweetAlert for step 5 completion
                        // No additional action needed here
                    }
                });
            });

            // Helper function to convert 12-hour format to 24-hour format for comparison
            function convertTo24Hour(time12h) {
                const [time, modifier] = time12h.split(' ');
                let [hours, minutes] = time.split(':');
                if (hours === '12') {
                    hours = '00';
                }
                if (modifier === 'PM') {
                    hours = parseInt(hours, 10) + 12;
                }
                return `${hours}:${minutes}`;
            }

            function validateFields($fieldset) {
                var hasError = false;

                $fieldset.find(
                    'input:not(.no_validate,[type="hidden"], [type="checkbox"], .select2-search__field, .next, .previous), textarea:not(.no_validate)'
                ).each(function () {
                    var $field = $(this);
                    var fieldValue = $field.val();
                    var fieldName = $field.attr('name');

                    // Check if field is empty
                    if (!fieldValue) {
                        $field.addClass("valid_error");
                        $field.css('border', '2px solid red');
                        hasError = true;
                    }
                    // Special validation for phone field using ipinfo API
                    else if (fieldName === 'phone') {
                        if (professionalIti && fieldValue && !professionalIti.isValidNumber()) {
                            $field.addClass("valid_error");
                            $field.css('border', '2px solid red');
                            hasError = true;

                            // Show error message if not already shown
                            if (!document.getElementById('professional-phone-error')) {
                                const errorElement = document.createElement('span');
                                errorElement.id = 'professional-phone-error';
                                errorElement.className = 'invalid-feedback';
                                errorElement.style.display = 'block';
                                errorElement.style.color = 'red';
                                errorElement.style.fontWeight = 'bold';
                                errorElement.innerHTML =
                                    '<strong>Please enter a valid phone number</strong>';
                                $field[0].parentNode.appendChild(errorElement);
                            }
                        } else if (fieldValue) {
                            $field.removeClass("valid_error");
                            $field.css('border', '0.5px solid #9b9b9b');

                            // Remove error message if exists
                            const phoneError = document.getElementById('professional-phone-error');
                            if (phoneError) {
                                phoneError.remove();
                            }
                        }
                    } else {
                        $field.removeClass("valid_error");
                        $field.css('border', '0.5px solid #9b9b9b');
                    }
                });

                // Special validation for step 2 (subcategories)
                if ($fieldset.attr('id') === 'step-2') {
                    // Check selected subcategories
                    var selectedSubcategories = $fieldset.find('input[name="subcategories[]"]:checked').length;

                    if (selectedSubcategories < 1) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least 1 subcategory",
                            icon: "error"
                        });
                        hasError = true;
                    }

                    // Check that no more than 3 categories are selected
                    var selectedCategories = $fieldset.find('.service_category.active').length;
                    if (selectedCategories > 3) {
                        Swal.fire({
                            title: "Error",
                            text: "You can select maximum 3 categories",
                            icon: "error"
                        });
                        hasError = true;
                    }
                }

                // Special validation for step 3 (certifications)
                if ($fieldset.attr('id') === 'step-3') {
                    // Product certifications are always required
                    var selectedProductCerts = $fieldset.find('input[name="product_certifications[]"]:checked')
                        .length;

                    if (selectedProductCerts < 1) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least one product certification",
                            icon: "error"
                        });
                        hasError = true;
                    }

                    // Check if any certificate fields are filled (including image and exception)
                    var hasCertificateData = false;
                    var certificateBlocks = $fieldset.find('.gray-card');

                    certificateBlocks.each(function () {
                        var $block = $(this);
                        var title = $block.find(`input[name*="[title]"]`).val();
                        var issuedBy = $block.find(`input[name*="[issued_by]"]`).val();
                        var issuedDate = $block.find(`input[name*="[issued_date]"]`).val();
                        var endDate = $block.find(`input[name*="[end_date]"]`).val();
                        var hasImage = $block.find(`input[name*="[image]"]`)[0]?.files?.length > 0 ||
                            $block.find(`input[name*="[old_image]"]`).length > 0;
                        var hasException = $block.find(`input[name*="[exception]"]`).is(':checked');

                        if (title || issuedBy || issuedDate || endDate || hasImage || hasException) {
                            hasCertificateData = true;
                            return false; // Break the loop
                        }
                    });

                    // Validate certificates only if there's certificate data
                    if (hasCertificateData) {
                        var certificateErrors = {};

                        var activeCertNumber = 0; // Track only certificates with data
                        certificateBlocks.each(function (index) {
                            var $block = $(this);

                            // Check if any field is filled for this certificate (including image and exception)
                            var title = $block.find(`input[name*="[title]"]`).val();
                            var issuedBy = $block.find(`input[name*="[issued_by]"]`).val();
                            var issuedDate = $block.find(`input[name*="[issued_date]"]`).val();
                            var endDate = $block.find(`input[name*="[end_date]"]`).val();
                            var hasImage = $block.find(`input[name*="[image]"]`)[0]?.files?.length > 0 ||
                                $block.find(`input[name*="[old_image]"]`).length > 0;
                            var hasException = $block.find(`input[name*="[exception]"]`).is(':checked');
                            var exceptionReason = $block.find(`textarea[name*="[exception_reason]"]`).val();

                            var hasAnyField = title || issuedBy || issuedDate || endDate || hasImage ||
                                hasException;

                            if (hasAnyField) {
                                activeCertNumber++; // Only increment for certificates with data
                                var certNumber = activeCertNumber;
                                var certErrors = [];

                                // Core certificate fields are always required when any field is filled
                                if (!title) {
                                    certErrors.push(`Certificate title is required`);
                                }
                                if (!issuedBy) {
                                    certErrors.push(`Issued by is required`);
                                }
                                if (!issuedDate) {
                                    certErrors.push(`Issued date is required`);
                                }
                                if (!endDate) {
                                    certErrors.push(`Expiry date is required`);
                                }

                                // Check if expiry date is after issued date
                                if (issuedDate && endDate && new Date(endDate) <= new Date(issuedDate)) {
                                    certErrors.push(`Expiry date must be after issued date`);
                                }

                                // Image or exception logic
                                if (hasException) {
                                    // If exception is checked, reason is required, image is optional
                                    if (!exceptionReason) {
                                        certErrors.push(
                                            `Reason for exception is required when certificate exception is checked`
                                        );
                                    }
                                } else {
                                    // If no exception, image is required
                                    if (!hasImage) {
                                        certErrors.push(
                                            `Certificate image is required or check certificate exception`
                                        );
                                    }
                                }

                                // If this certificate has errors, add them to the main errors object
                                if (certErrors.length > 0) {
                                    certificateErrors[certNumber] = certErrors;
                                }
                            }
                        });

                        // Show all errors grouped by certificate if any exist
                        if (Object.keys(certificateErrors).length > 0) {
                            var errorMessage = '';

                            Object.keys(certificateErrors).forEach(function (certNumber) {
                                errorMessage += `<strong>Certificate #${certNumber}:</strong><br>`;
                                certificateErrors[certNumber].forEach(function (error) {
                                    errorMessage += `• ${error}<br>`;
                                });
                                errorMessage += '<br>';
                            });

                            Swal.fire({
                                title: "Validation Errors",
                                html: errorMessage,
                                icon: "error"
                            });
                            hasError = true;
                        }
                    }
                }

                // Special validation for step 4 (availability)
                if ($fieldset.attr('id') === 'step-4') {
                    var anyDayChecked = $fieldset.find('input[name^="availability"][type="checkbox"]:checked')
                        .length > 0;
                    if (!anyDayChecked) {
                        Swal.fire({
                            title: "Error",
                            text: "Please select at least one day for availability",
                            icon: "error"
                        });
                        hasError = true;
                    } else {
                        // Check if time is provided for each selected day and validate time ranges
                        var timeErrors = [];

                        $fieldset.find('input[name^="availability"][type="checkbox"]:checked').each(function () {
                            var checkboxName = $(this).attr('name');
                            // Extract the index from the checkbox name (e.g., availability[0][day] -> 0)
                            var indexMatch = checkboxName.match(/availability\[(\d+)\]\[day\]/);
                            if (indexMatch) {
                                var index = indexMatch[1];
                                var dayName = $(this).val();
                                var startTime = $fieldset.find(
                                    `input[name="availability[${index}][start]"]`).val();
                                var endTime = $fieldset.find(`input[name="availability[${index}][end]"]`)
                                    .val();

                                if (!startTime || !endTime) {
                                    timeErrors.push(
                                        `Please provide both start and end time for ${dayName}`);
                                } else {
                                    // Convert 12-hour format to 24-hour for comparison
                                    var startTime24 = startTime;
                                    var endTime24 = endTime;

                                    if (startTime24 >= endTime24) {
                                        timeErrors.push(`End time must be after start time for ${dayName}`);
                                    }
                                }
                            }
                        });

                        // Validate holiday times
                        $fieldset.find('.time-picker-calendar input[type="checkbox"]:checked').each(function () {
                            var $holidayContainer = $(this).closest('.time-picker-calendar');
                            var $timeContainer = $holidayContainer.find('.start-time');
                            var holidayName = $(this).siblings('.checkmark').text().trim();

                            if ($timeContainer.is(':visible')) {
                                var startTimeInput = $timeContainer.find('input[name*="[start_time]"]');
                                var endTimeInput = $timeContainer.find('input[name*="[end_time]"]');
                                var startTime = startTimeInput.val();
                                var endTime = endTimeInput.val();

                                // If holiday is checked and time container is visible, both times are required
                                if (!startTime || !endTime) {
                                    timeErrors.push(
                                        `Please provide both start and end time for ${holidayName}`);
                                } else {
                                    // If both times are provided, validate the range
                                    var startTime24 = startTime;
                                    var endTime24 = endTime;

                                    if (startTime24 >= endTime24) {
                                        timeErrors.push(
                                            `End time must be after start time for ${holidayName}`);
                                    }
                                }
                            }
                        });

                        if (timeErrors.length > 0) {
                            var errorMessage = timeErrors.join('<br>');
                            Swal.fire({
                                title: "Time Validation Errors",
                                html: errorMessage,
                                icon: "error"
                            });
                            hasError = true;
                        }
                    }
                }

                // Special validation for step 5 (multiple banner images)
                if ($fieldset.attr('id') === 'step-5') {
                    var bannerImagesCount = $('#banner_images_inputs input[name="banner_images[]"]').length;

                    if (bannerImagesCount < 3) {
                        Swal.fire({
                            title: "Error",
                            text: "Please upload at least 3 banner images",
                            icon: "error"
                        });
                        hasError = true;
                    }
                }
                return hasError; // Returns true if there is an error, false otherwise
            }

            // Handle logout with confirmation for both buttons
            $(document).on('click', '#logoutBtn, #logoutBtnSubmit', function (e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Registration Not Complete',
                    text: 'Your registration is not complete yet. You can continue from here anytime. Are you sure you want to logout?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Yes, Logout',
                    cancelButtonText: 'Continue Registration'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = "{{ url('logout') }}";
                    }
                });
            });
        });
    </script>

    <script>
        // form submit
        $(document).on("click", ".continue-btn", function () {
            let form_data = $("#acc-form").serialize();
            console.log(form_data);

        })
        // form submit end

        function initFileUpload($group) {
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];
            const $dropArea = $group.find(".upload-box");
            const $fileInput = $group.find("input[type='file']");
            const $previewArea = $group.find(".preview-container");

            $dropArea.off();
            $fileInput.off();

            $dropArea.on("click", function () {
                $fileInput.click();
            });

            $fileInput.on("change", function (e) {
                const file = e.target.files[0];
                if (file) handleFile(file);
            });

            $dropArea.on("dragover", function (e) {
                e.preventDefault();
                $dropArea.css("background", "#eee");
            });

            $dropArea.on("dragleave", function (e) {
                e.preventDefault();
                $dropArea.css("background", "");
            });

            $dropArea.on("drop", function (e) {
                e.preventDefault();
                $dropArea.css("background", "");
                const file = e.originalEvent.dataTransfer.files[0];
                if (file) handleFile(file);
            });

            function handleFile(file) {
                if (!allowedImages.includes(file.type)) {
                    swal.fire({
                        title: "Error",
                        text: "Only JPG and PNG images are allowed.",
                        icon: "error"
                    });
                    return;
                }

                if (file.size > 2 * 1024 * 1024) {
                    swal.fire({
                        title: "Error",
                        text: "File is too large. Max size: 2MB.",
                        icon: "error"
                    });
                    return;
                }

                $previewArea.empty();
                // Don't clear the file input value - we need it for form submission
                // $fileInput.val("");

                const reader = new FileReader();
                reader.onload = function (e) {
                    const $preview = $(`
                            <div class="preview-box" style="position: relative; display: inline-block; margin: 10px;">
                                <img src="${e.target.result}" >
                                <button class="remove-preview" style="position: absolute; top: 2px; right: 2px; background: red; color: white; border: none; border-radius: 50%; width: 20px; height: 20px;">
                                    <span class="position-absolute" style="top: 0; right: 5.5px;" > × <span>
                                </button>
                            </div>
                        `);

                    $preview.find(".remove-preview").on("click", function () {
                        $preview.remove();
                        $fileInput.val("");
                    });

                    $previewArea.append($preview);
                };
                reader.readAsDataURL(file);
            }
        }

        function reindexRows() {
            // Reindex ALL certificate blocks (both initial and dynamically added)
            let allCerts = [];
            
            // Get initial certificate if it exists
            const $initialCert = $('.initial-cert');
            if ($initialCert.length) {
                allCerts.push($initialCert);
            }
            
            // Get all certificates in the wrapper
            $('#certifications-wrapper .file-upload-group').each(function() {
                allCerts.push($(this));
            });

            // Now reindex all certificates sequentially
            allCerts.forEach(function($group, index) {
                $group.find('[name]').each(function() {
                    let name = $(this).attr('name');
                    if (name) {
                        name = name.replace(/certificates\[\d+\]/, `certificates[${index}]`);
                        $(this).attr('name', name);
                    }
                });

                $group.find('textarea[id^="w3review_"]').attr('id', `w3review_${index}`);
                $group.find('label[for^="w3review_"]').attr('for', `w3review_${index}`);
                $group.find('input.issued-date').attr('data-index', index);
                $group.find('input.end-date').attr('data-index', index);
                
                // Update the certificate title (works for both h1 and h4)
                const $title = $group.find('h1, h4, .cert-title');
                if ($title.length) {
                    $title.text(`Certificate #${index + 1}`);
                }

                // Ensure delete button exists for each certificate block
                let deleteBtn = $group.find('.delete-block');
                if (deleteBtn.length === 0) {
                    $group.append(`
                        <div class="mt-3 d-flex justify-content-between">
                            <button type="button" class="delete-block">Delete This Block</button>
                        </div>
                    `);
                }
            });
            
            // Update certIndex to the total count
            certIndex = allCerts.length - 1;
        }

        function addCertificationBlock() {
            certIndex++;
            const wrapper = document.getElementById('certifications-wrapper');
            const totalCerts = $('#certifications-wrapper .file-upload-group').length + 1;

            const newBlock = `
                                        <div class="gray-card my-5 file-upload-group">
                                            <h4 class="mb-8 text-center cert-title">Certificate #${certIndex + 1}</h4>
                                           

                                            <div class="row"> 
                                                 <div class="col-md-12 field-spacing-cust">
                                                <label class="fieldlabels">Certification Title*</label>
                                                <input class="no_validate" type="text" name="certificates[${certIndex}][title]" placeholder="Enter certification title"/>
                                                
                                            </div>

                                                <div class="col-md-12 field-spacing-cust">
                                                 <label class="fieldlabels">Issued by*</label>
                                                    <input class="no_validate" type="text" name="certificates[${certIndex}][issued_by]" placeholder="Enter name"/>
                                                </div>

                                                <div class="col-md-6 field-spacing-cust">
                                                    <label class="fieldlabels">Issued Date*</label>
                                                    <input class="no_validate issued-date" type="date" name="certificates[${certIndex}][issued_date]" placeholder="Enter issued date" data-index="${certIndex}"/>
                                                </div>

                                                <div class="col-md-6 field-spacing-cust">
                                                    <label class="fieldlabels">End Date*</label>
                                                    <input class="no_validate end-date" type="date" name="certificates[${certIndex}][end_date]" placeholder="Enter end date" data-index="${certIndex}" disabled style="background-color: #e9ecef; color: #6c757d; cursor: not-allowed;"/>
                                                    <label class="error text-danger" style="display: none;"></label>
                                                </div>

                                                <div class="col-md-12 form-border">
                                                    <p class="manrope fw-600 light-black">Share Certificates</p>
                                                    <div>
                                                        <label class="upload-box fs-12 normal fw-300 Plus-Jakarta-Sans" style="cursor:pointer;">
                                                            <img src="{{ asset('website/assets/images/upload.svg') }}" alt="Upload Icon">
                                                            <p>Upload Certificate</p>
                                                            <p class="mb-0">Maximum file size: 2 MB</p>
                                                            <p>Supported format: JPG and PNG</p>
                                                            <span class="add-file">
                                                                <p class="upload-cert-btn fs-14 fw-600"> Upload </p>
                                                            </span>
                                                            <input class="no_validate" name="certificates[${certIndex}][image]" type="file" hidden>
                                                        </label>
                                                    </div>
                                                    <div class="preview-container"></div>

                                                    <div class="exception-checkbox">
                                                        <label class="cert-excep">
                                                            <input class="no_validate" type="checkbox" id="exceptionToggle" name="certificates[${certIndex}][exception]">
                                                            <span class="checkmark">Certificate Exception</span>
                                                        </label>

                                                        <div class="exception-textarea">
                                                            <label class="mb-2" for="w3review_${certIndex}">Reason for Exception</label>
                                                            <textarea class="mb-0 no_validate" id="w3review_${certIndex}" name="certificates[${certIndex}][exception_reason]" rows="4" cols="50"
                                                            placeholder="Write reason for exception"></textarea>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="mt-3 d-flex justify-content-between">
                                                    <button type="button" class=" delete-block">Delete This Block</button>
                                                </div>
                                                </div>


                                            </div>
                                        `;

            // Insert after the last block
            wrapper.insertAdjacentHTML('beforeend', newBlock);

            // Re-initialize upload and bind delete/add buttons for the new block
            const $newGroup = $('.file-upload-group').last();
            initFileUpload($newGroup);

            // Initialize date functionality for the new block
            const $newIssuedDate = $newGroup.find('.issued-date');
            if ($newIssuedDate.length && typeof handleIssuedDateChangeRegister === 'function') {
                handleIssuedDateChangeRegister($newIssuedDate[0]);
            }

            // Delete handler
            $newGroup.find('.delete-block').on('click', function () {
                $newGroup.remove();
                reindexRows();
                updateDeleteButtonVisibility();
            });

            // Add More handler inside block
            $newGroup.find('.add-more-inside').on('click', function () {
                addCertificationBlock();
            });

            // Update delete button visibility after adding new block
            updateDeleteButtonVisibility();
        }

        // On top Add More button click
        document.getElementById('addMoreBtn').addEventListener('click', addCertificationBlock);

        $(document).ready(function () {
            $('.file-upload-group').each(function () {
                initFileUpload($(this));
            });

            // Initialize delete buttons for existing certificates
            $(document).on('click', '.delete-block', function () {
                const $group = $(this).closest('.file-upload-group');
                const $grayCard = $(this).closest('.gray-card');

                // Remove the certificate block
                if ($group.length > 0) {
                    $group.remove();
                } else if ($grayCard.length > 0) {
                    $grayCard.remove();
                }

                reindexRows();
                updateDeleteButtonVisibility();
            });

            function updateDeleteButtonVisibility() {
                // Count all certificate blocks
                const initialCertCount = $('.initial-cert').length;
                const wrapperCertCount = $('#certifications-wrapper .file-upload-group').length;
                const totalAllCerts = initialCertCount + wrapperCertCount;

                // Update delete button visibility for all certificate blocks
                $('.delete-block').each(function () {
                    if (totalAllCerts > 1) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            }

            // Initial reindexing to set up delete buttons properly
            reindexRows();
            updateDeleteButtonVisibility();

            $(document).on("dragover drop", function (e) {
                e.preventDefault();
            });
        });
    </script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            // Initialize all flatpickr time inputs
            flatpickr(".flatpickr-time", {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                time_24hr: true
            });

            // Add real-time validation for availability time inputs
            $(document).on('change', 'input[name^="availability"][name$="[end]"]', function () {
                validateAvailabilityTime($(this));
            });

            // Function to validate availability time
            function validateAvailabilityTime($endInput) {
                // Get the corresponding start input
                var endInputName = $endInput.attr('name');
                var startInputName = endInputName.replace('[end]', '[start]');
                var $startInput = $(`input[name="${startInputName}"]`);

                // Get the day name from the checkbox
                var dayInputName = endInputName.replace('[end]', '[day]');
                var $dayCheckbox = $(`input[name="${dayInputName}"]`);
                var dayName = $dayCheckbox.val();

                var startTime = $startInput.val();
                var endTime = $endInput.val();

                if (startTime && endTime) {
                    // Convert times to minutes for comparison
                    var startMinutes = timeToMinutes(startTime);
                    var endMinutes = timeToMinutes(endTime);

                    if (endMinutes <= startMinutes) {
                        // Show SweetAlert error
                        Swal.fire({
                            title: 'Invalid Time Range',
                            text: `End time must be after start time for ${dayName}`,
                            icon: 'error',
                            confirmButtonText: 'Got it'
                        });

                        // Clear the invalid end time
                        $endInput.val('');
                        if ($endInput[0]._flatpickr) {
                            $endInput[0]._flatpickr.clear();
                        }
                    }
                }
            }

            // Helper function to convert time string to minutes
            function timeToMinutes(timeStr) {
                if (!timeStr) return 0;
                var parts = timeStr.split(':');
                return parseInt(parts[0]) * 60 + parseInt(parts[1]);
            }

            // Ensure already visible time inputs (for checked holidays) are properly initialized
            document.querySelectorAll('.time-picker-calendar').forEach(function (calendar) {
                const checkbox = calendar.querySelector('.day-checkbox');
                const timeContainer = calendar.querySelector('.start-time');

                if (checkbox && checkbox.checked && timeContainer) {
                    // Make sure the time container is visible
                    timeContainer.style.display = 'flex';

                    // Re-initialize flatpickr for these inputs to ensure they work properly
                    timeContainer.querySelectorAll('.flatpickr-time').forEach(input => {
                        if (!input._flatpickr) {
                            flatpickr(input, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        }
                    });
                }
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Get the Select All checkbox
            const selectAllCheckbox = document.querySelector('.select-all');
            // Get all individual day checkboxes
            const dayCheckboxes = document.querySelectorAll('.day-checkbox');

            // Handle availability checkboxes (time-picker-calendar2)
            $(document).on('change', '.time-picker-calendar2 input[type="checkbox"]', function () {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();

                    // Initialize flatpickr for time inputs
                    $checkedTime.find('.flatpickr-time').each(function () {
                        if (!this._flatpickr) {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        }
                    });
                } else {
                    $checkedTime.hide();
                    $closedTime.show();

                    // Clear time values
                    $checkedTime.find('.flatpickr-time').val('');
                }
            });

            // Initialize existing state for availability checkboxes
            $('.time-picker-calendar2 input[type="checkbox"]').each(function () {
                const $timeRange = $(this).closest('.time-picker-calendar2').find('.time-picker-range2');
                const $checkedTime = $timeRange.find('.checked-time');
                const $closedTime = $timeRange.find('.closed-time');

                if ($(this).is(':checked')) {
                    $checkedTime.show();
                    $closedTime.hide();
                } else {
                    $checkedTime.hide();
                    $closedTime.show();
                }
            });

            // Function to toggle time picker visibility and Flatpickr
            function toggleTimePicker(checkbox, isChecked) {
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector('.start-time');
                const timeInputs = timePickerContainer.querySelectorAll('.flatpickr-time');

                if (isChecked) {
                    timePickerContainer.style.display = 'flex';
                    timeInputs.forEach(input => {
                        flatpickr(input, {
                            enableTime: true,
                            noCalendar: true,
                            dateFormat: "H:i",
                            time_24hr: true
                        });
                    });
                } else {
                    timePickerContainer.style.display = 'none';
                    timeInputs.forEach(input => {
                        if (input._flatpickr) {
                            input._flatpickr.destroy();
                        }
                    });
                }
            }

            // Event listener for Select All checkbox
            selectAllCheckbox.addEventListener('change', function () {
                const isChecked = this.checked;

                // Toggle all day checkboxes and their time pickers
                dayCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked; // Check or uncheck all
                    toggleTimePicker(checkbox, isChecked); // Show or hide time picker
                });
            });

            // Existing logic for individual checkboxes
            dayCheckboxes.forEach(checkbox => {
                const timePickerContainer = checkbox.closest('.time-picker-calendar').querySelector(
                    '.start-time');

                // Only hide time picker if checkbox is not checked or if it doesn't have time values
                if (!checkbox.checked) {
                    timePickerContainer.style.display = 'none';
                } else {
                    // If checkbox is checked, check if there are time values
                    const timeInputs = timePickerContainer.querySelectorAll('.flatpickr-time');
                    const hasTimeValues = Array.from(timeInputs).some(input => input.value.trim() !== '');

                    if (hasTimeValues) {
                        // Keep it visible and ensure flatpickr is initialized
                        timePickerContainer.style.display = 'flex';
                        timeInputs.forEach(input => {
                            if (!input._flatpickr) {
                                flatpickr(input, {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "H:i",
                                    time_24hr: true
                                });
                            }
                        });
                    } else {
                        timePickerContainer.style.display = 'none';
                    }
                }

                // Add event listener for individual checkbox change
                checkbox.addEventListener('change', function () {
                    toggleTimePicker(this, this.checked);
                });
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            const allowedDocs = [
                "application/pdf", "application/msword",
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "application/ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "application/vnd.ms-powerpoint",
                "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                "application/zip"
            ];
            const allowedImages = ["image/png", "image/jpeg", "image/jpg"];

            // Generic handler for multiple dropzones
            $("[data-type]").each(function () {
                const dropArea = $(this);
                const fileInput = dropArea.find(".file-input");
                const previewContainer = dropArea.find(".preview-container");
                const type = dropArea.data("type");

                dropArea.on("click", function () {
                    fileInput.click();
                });

                fileInput.on("change", function (e) {
                    handleFiles(e.target.files, type, previewContainer);
                });

                dropArea.on("dragover", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#ddd");
                });

                dropArea.on("dragleave", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#f9f9f9");
                });

                dropArea.on("drop", function (e) {
                    e.preventDefault();
                    dropArea.css("background", "#f9f9f9");
                    handleFiles(e.originalEvent.dataTransfer.files, type, previewContainer);
                });
            });

            function handleFiles(files, type, previewContainer) {
                $.each(files, function (index, file) {
                    const fileType = file.type;
                    const fileReader = new FileReader();

                    if (type === "certificate") {
                        if (!allowedImages.includes(fileType)) {
                            swal.fire({
                                title: "Error",
                                text: "Only image files (PNG, JPG, JPEG) are allowed.",
                                icon: "error"
                            });
                            return;
                        }

                        fileReader.onload = function (e) {
                            const previewElement = $("<div class='preview-box'></div>");
                            previewElement.append(`<img src="${e.target.result}" alt="Image Preview">`);
                            const removeBtn = $("<button class='remove-image'>X</button>");
                            removeBtn.on("click", function () {
                                previewElement.remove();
                            });
                            previewElement.append(removeBtn);
                            previewContainer.append(previewElement);
                        };

                    } else if (type === "portfolio") {
                        if (!allowedDocs.includes(fileType)) {
                            swal.fire({
                                title: "Error",
                                text: "Only document files (PDF, DOCX, ZIP, etc.) are allowed.",
                                icon: "error"
                            });
                            return;
                        }

                        fileReader.onload = function () {
                            const fileName = file.name;
                            const fileDate = new Date().toLocaleDateString();
                            const previewElement = $("<div class='preview-box-file'></div>");

                            let icon = "<i class='fa-solid fa-file' style='color: black;'></i>";
                            if (fileType === "application/pdf") icon =
                                "<i class='fa-solid fa-file-pdf' style='color: #d70e0ef2;'></i>";
                            if (fileType.includes("word")) icon =
                                "<i class='fa-solid fa-file-word' style='color: #1d517f;'></i>";
                            if (fileType.includes("excel")) icon =
                                "<i class='fa-solid fa-file-excel' style='color: #137b13;'></i>";
                            if (fileType.includes("powerpoint")) icon =
                                "<i class='fa-solid fa-file-powerpoint' style='color: orange;'></i>";
                            if (fileType === "application/zip") icon =
                                "<i class='fa-solid fa-file-zipper' style='color: brown;'></i>";

                            previewElement.append(icon);
                            previewElement.append(
                                `<div class="file-name d-flex flex-column">
                                                                        <p class="dark-charcoal manrope fs-14 fw-500">${fileName}</p>
                                                                        <p class="dark-charcoal manrope fs-14 normal">${fileDate}</p>
                                                                     </div>`
                            );
                            const removeBtn = $("<button class='remove-file'>X</button>");
                            removeBtn.on("click", function () {
                                previewElement.remove();
                            });
                            previewElement.append(removeBtn);
                            previewContainer.append(previewElement);
                        };
                    }

                    fileReader.readAsDataURL(file);
                });
            }
        });
    </script>

    <script>
        // Prevent double init in SPAs
        if (!window._bannerMultipleInit) {
            window._bannerMultipleInit = true;

            let bannerImages = [];
            let currentImageIndex = 0;

            (function initMultipleBannerUpload() {
                // File input handler
                const fileInput = document.createElement('input');
                fileInput.type = 'file';
                fileInput.multiple = true;
                fileInput.accept = '.png,.jpg,.jpeg';
                fileInput.style.display = 'none';
                document.body.appendChild(fileInput);

                // Click handler for upload area
                document.getElementById('profileImg').addEventListener('click', function () {
                    fileInput.click();
                });

                // File selection handler
                fileInput.addEventListener('change', function (e) {
                    const files = Array.from(e.target.files);
                    processSelectedFiles(files);
                    fileInput.value = ''; // Reset input
                });

                // Process selected files
                function processSelectedFiles(files) {
                    for (let file of files) {
                        // Validate file type
                        if (!file.type || !file.type.match(/image.*/)) {
                            Swal.fire({
                                title: "Error",
                                text: "Only image files are allowed.",
                                icon: "error"
                            });
                            continue;
                        }

                        // Validate file size (2MB = 2 * 1024 * 1024 bytes)
                        if (file.size > 2 * 1024 * 1024) {
                            Swal.fire({
                                title: "Error",
                                text: `File "${file.name}" is too large. Maximum size is 2MB.`,
                                icon: "error"
                            });
                            continue;
                        }

                        // Process valid file
                        processImageFile(file);
                    }
                }

                // Process individual image file
                function processImageFile(file) {
                    const reader = new FileReader();
                    reader.onload = function (e) {
                        // Create image for cropping
                        const img = new Image();
                        img.onload = function () {
                            openCropModal(img, file, e.target.result);
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }

                // Open crop modal for image
                function openCropModal(img, file, originalDataUrl) {
                    const modal = document.getElementById("cropmodal");
                    const templateFrag = document.getElementById("modaltpl").content.cloneNode(true);

                    const modalFooter = templateFrag.querySelector(".modal-footer");
                    const imgContainer = templateFrag.querySelector(".img-container");

                    // Insert the image to crop
                    const image = new Image();
                    image.src = originalDataUrl;
                    imgContainer.innerHTML = "";
                    imgContainer.appendChild(image);

                    // Buttons: Crop + Cancel
                    const confirmBtn = document.createElement("button");
                    confirmBtn.type = "button";
                    confirmBtn.className = "w-100px blue-btn px-4 py-2";
                    confirmBtn.textContent = "Crop";

                    const cancelBtn = document.createElement("button");
                    cancelBtn.type = "button";
                    cancelBtn.className = "btn btn-light px-4 py-2 ms-2";
                    cancelBtn.textContent = "Cancel";

                    modalFooter.appendChild(cancelBtn);
                    modalFooter.appendChild(confirmBtn);

                    // Render modal content
                    modal.innerHTML = "";
                    modal.appendChild(templateFrag);

                    // Bootstrap 5 modal
                    const bsModal = new bootstrap.Modal(modal, {
                        backdrop: "static"
                    });

                    let cropper = null;

                    // One-time handlers avoid stacking
                    modal.addEventListener("shown.bs.modal", onShown, {
                        once: true
                    });
                    modal.addEventListener("hidden.bs.modal", onHidden, {
                        once: true
                    });

                    cancelBtn.addEventListener("click", () => {
                        bsModal.hide();
                    });

                    confirmBtn.addEventListener("click", async () => {
                        if (!cropper) return;

                        const canvas = cropper.getCroppedCanvas({
                            width: 2520,
                            height: 1080
                        });
                        if (!canvas) return;

                        canvas.toBlob(async (blob) => {
                            if (!blob) return;

                            // Convert to base64 and add to banner images
                            const reader = new FileReader();
                            reader.onloadend = function () {
                                addBannerImage(reader.result, file.name);
                                bsModal.hide();
                            };
                            reader.readAsDataURL(blob);
                        }, "image/jpeg", 0.95);
                    });

                    // Show modal
                    bsModal.show();

                    // ====== Handlers ======
                    function onShown() {
                        cropper = new Cropper(image, {
                            aspectRatio: 21 / 9,
                            viewMode: 2,
                            autoCropArea: 1,
                            responsive: true,
                            background: false
                        });
                    }

                    function onHidden() {
                        // Cleanup
                        if (cropper) {
                            cropper.destroy();
                            cropper = null;
                        }
                        modal.innerHTML = "";
                    }
                }

                // Add banner image to the collection
                function addBannerImage(base64Data, fileName) {
                    const imageIndex = currentImageIndex++;

                    // Add to bannerImages array
                    bannerImages.push({
                        index: imageIndex,
                        data: base64Data,
                        name: fileName
                    });

                    // Create hidden input
                    const hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.name = 'banner_images[]';
                    hiddenInput.value = base64Data;
                    hiddenInput.id = `banner_image_${imageIndex}`;
                    document.getElementById('banner_images_inputs').appendChild(hiddenInput);

                    // Create preview
                    createImagePreview(imageIndex, base64Data, fileName);

                    // Update validation display
                    updateValidationDisplay();
                }

                // Create image preview
                function createImagePreview(imageIndex, base64Data, fileName) {
                    const template = document.getElementById('banner_image_template');
                    const clone = template.content.cloneNode(true);

                    const container = clone.querySelector('.banner-image-item');
                    container.setAttribute('data-index', imageIndex);

                    const img = clone.querySelector('.banner-preview-img');
                    img.src = base64Data;

                    const nameElement = clone.querySelector('.banner-image-name');
                    nameElement.textContent = fileName;

                    const removeBtn = clone.querySelector('.remove-banner-btn');
                    removeBtn.addEventListener('click', () => removeBannerImage(imageIndex));

                    document.getElementById('banner_images_container').appendChild(clone);
                }

                // Remove banner image
                function removeBannerImage(imageIndex) {
                    // Remove from array
                    bannerImages = bannerImages.filter(img => img.index !== imageIndex);

                    // Remove hidden input
                    const hiddenInput = document.getElementById(`banner_image_${imageIndex}`);
                    if (hiddenInput) {
                        hiddenInput.remove();
                    }

                    // Remove preview
                    const previewElement = document.querySelector(`[data-index="${imageIndex}"]`);
                    if (previewElement) {
                        previewElement.remove();
                    }

                    // Update validation display
                    updateValidationDisplay();
                }

                // Update validation display
                function updateValidationDisplay() {
                    const errorDiv = document.getElementById('banner_validation_error');
                    const count = bannerImages.length;

                    if (count < 3) {
                        errorDiv.textContent = `Please upload at least 3 images. Currently uploaded: ${count}`;
                        errorDiv.style.display = 'block';
                    } else {
                        errorDiv.style.display = 'none';
                    }
                }
            })();
        }
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            let holidayIndex = 0; // Initialize holidayIndex

            // ===== Reindex holidays =====
            function reindexHolidays() {
                // Reindex regular holidays (non-custom)
                const regularHolidayContainers = $('.time-picker-calendar').not('.custom-holiday-div');
                regularHolidayContainers.each(function (index) {
                    $(this).find('input, select, textarea').each(function () {
                        const name = $(this).attr('name');
                        if (name && name.includes('holidays[')) {
                            const newName = name.replace(/holidays\[\d+\]/, 'holidays[' + index + ']');
                            $(this).attr('name', newName);
                        }
                    });
                });

                // Reindex custom holidays separately
                const customHolidayContainers = $('.custom-holiday-div');
                customHolidayContainers.each(function (index) {
                    $(this).find('input, select, textarea').each(function () {
                        const name = $(this).attr('name');
                        if (name && name.includes('custom_holidays[')) {
                            const newName = name.replace(/custom_holidays\[\d+\]/, 'custom_holidays[' + index + ']');
                            $(this).attr('name', newName);
                        }
                    });
                });

                // Update index count
                holidayIndex = customHolidayContainers.length;
            }

            // ===== Checkbox & Select All Handling =====
            $(document).on('change', function (e) {
                if ($(e.target).hasClass('day-checkbox')) {
                    const checkbox = $(e.target);
                    const timePickerContainer = checkbox.closest('.time-picker-calendar').find('.start-time');

                    if (checkbox.prop('checked')) {
                        timePickerContainer.show();
                        timePickerContainer.find('.flatpickr-time').each(function () {
                            flatpickr(this, {
                                enableTime: true,
                                noCalendar: true,
                                dateFormat: "H:i",
                                time_24hr: true
                            });
                        });
                    } else {
                        timePickerContainer.hide();
                        timePickerContainer.find('.flatpickr-time').each(function () {
                            if (this._flatpickr) this._flatpickr.destroy();
                        });
                    }
                }

                if ($(e.target).hasClass('select-all')) {
                    const isChecked = $(e.target).prop('checked');
                    $('.day-checkbox').prop('checked', isChecked).trigger('change');
                }
            });

            // ===== Bootstrap Modal Initialization =====
            const modalEl = document.getElementById('customHolidayModal');
            const customHolidayModal = new bootstrap.Modal(modalEl, {
                backdrop: true,
                keyboard: true
            });

            // ===== Open Modal =====
            $(document).on('click', '.add-custom-holiday-btn', function () {
                customHolidayModal.show();
            });

            // ===== Save Custom Holiday =====
            $('#saveCustomHoliday').on('click', function () {
                const name = $('#customHolidayName').val().trim();
                const date = $('#customHolidayDate').val().trim();

                if (name && date) {
                    const wrapper = $(`
                    <div class="time-picker-calendar custom-holiday-div">
                        <div class="d-flex justify-content-between align-items-center">
                            <label class="days">
                                <input type="checkbox" class="day-checkbox" name="custom_holidays[${holidayIndex}]">
                                <span class="checkmark">${name}</span>
                                <input type="hidden" name="custom_holidays[${holidayIndex}][name]" value="${name}">
                                <input type="hidden" name="custom_holidays[${holidayIndex}][date]" value="${date}">
                            </label>
                            <p>${date}</p>
                        </div>

                        <div class="start-time" style="display:none;">
                            <div class="d-flex gap-5">
                                <input type="text" class="flatpickr-time no_validate" placeholder="Select Time" name="custom_holidays[${holidayIndex}][start_time]">
                                <p class="mt-4"> - </p>
                                <input type="text" class="flatpickr-time no_validate" placeholder="Select Time" name="custom_holidays[${holidayIndex}][end_time]">
                            </div>
                        </div>

                        <button class="delete-holiday delete-block2 my-4">Delete</button>
                    </div>
                `);

                    $('.add-custom-holiday-btn').before(wrapper);
                    reindexHolidays();

                    const checkbox = wrapper.find('.day-checkbox');
                    const timePickerContainer = wrapper.find('.start-time');
                    const $hiddenInputs = wrapper.find('input[type="hidden"]');
                    const $timeInputs = wrapper.find('.flatpickr-time');

                    // Handle checkbox toggle
                    checkbox.on('change', function () {
                        if ($(this).prop('checked')) {
                            $hiddenInputs.prop('disabled', false);
                            $timeInputs.prop('disabled', false);
                            timePickerContainer.show();
                            timePickerContainer.find('.flatpickr-time').each(function () {
                                flatpickr(this, {
                                    enableTime: true,
                                    noCalendar: true,
                                    dateFormat: "H:i",
                                    time_24hr: true
                                });
                            });
                        } else {
                            $hiddenInputs.prop('disabled', true);
                            $timeInputs.prop('disabled', true).val('');
                            timePickerContainer.hide();
                            timePickerContainer.find('.flatpickr-time').each(function () {
                                if (this._flatpickr) this._flatpickr.destroy();
                            });
                        }
                    });

                    // Default checked
                    checkbox.prop('checked', true).trigger('change');

                    // Reset modal inputs and close properly
                    $('#customHolidayName').val('');
                    $('#customHolidayDate').val('');
                    customHolidayModal.hide();
                } else {
                    showToast('Please fill in both holiday name and date.', 'danger');
                }
            });

            // ===== Delete Custom Holiday =====
            $(document).on('click', '.delete-holiday', function () {
                $(this).closest('.custom-holiday-div').remove();
                reindexHolidays();
            });

            // ===== Clear Inputs When Modal Closes =====
            $('#customHolidayModal').on('hidden.bs.modal', function () {
                $('#customHolidayName').val('');
                $('#customHolidayDate').val('');
            });

            // ===== Flatpickr Initialization =====
            flatpickr('#customHolidayDate', { 
                dateFormat: "Y-m-d",
                minDate: new Date().fp_incr(1)
            });

            // ===== Auto-check first custom holiday =====
            const firstCustomCheckbox = document.querySelector('.custom-holiday-div .day-checkbox');
            if (firstCustomCheckbox) {
                firstCustomCheckbox.checked = true;
                firstCustomCheckbox.dispatchEvent(new Event('change'));
            }

            // ===== Set initial index =====
            holidayIndex = document.querySelectorAll('.custom-holiday-div').length;
        });
    </script>




    <script>
        $(document).ready(function () {
            // Category click handler - only shows subcategories, doesn't select category
            $('.service_category').click(function () {
                var categoryId = $(this).data('ctg');
                var $category = $(this);

                // Hide all subcategories first
                $('.sub_category').hide();

                // Show subcategories for this category
                $('#sub_category_' + categoryId).show();
            });

            // Subcategory click handler - validate before allowing selection
            $(document).on('click', 'input[name="subcategories[]"]', function (e) {
                var $checkbox = $(this);
                var categoryId = $checkbox.closest('.sub_category').attr('id').replace('sub_category_', '');
                var $category = $('.service_category[data-ctg="' + categoryId + '"]');
                var $categoryCheckboxes = $('#sub_category_' + categoryId +
                    ' input[name="subcategories[]"]');

                // If trying to check, validate limits first
                if (!$checkbox.is(':checked')) {
                    // Check if this would create a new active category and exceed category limit
                    var currentActiveCategories = $('.service_category.active').length;
                    var categoryWasActive = $category.hasClass('active');

                    if (!categoryWasActive && currentActiveCategories >= 3) {
                        e.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Maximum Categories Reached',
                            text: 'You can select maximum 3 categories',
                            confirmButtonText: 'Got it',
                            confirmButtonColor: '#020C87',
                        });
                        return false;
                    }
                }
            });

            // Subcategory change handler - auto-selects parent category
            $(document).on('change', 'input[name="subcategories[]"]', function () {
                var $checkbox = $(this);
                var categoryId = $checkbox.closest('.sub_category').attr('id').replace('sub_category_', '');
                var $category = $('.service_category[data-ctg="' + categoryId + '"]');
                var $categoryCheckboxes = $('#sub_category_' + categoryId +
                    ' input[name="subcategories[]"]');

                // Count checked subcategories for this category
                var checkedCount = $categoryCheckboxes.filter(':checked').length;

                // Auto-select/deselect category based on subcategories
                if (checkedCount > 0) {
                    // Auto-select category, show checkmark, and add blue border
                    $category.addClass('active');
                    $category.find('.selected-checkmark').removeClass('d-none');
                    $category.find('.radio-box').css('border', '2px solid #020C87');
                } else {
                    // Deselect category, hide checkmark, and remove blue border style completely
                    $category.removeClass('active');
                    $category.find('.selected-checkmark').addClass('d-none');
                    $category.find('.radio-box').removeAttr('style');
                }

                // Update hidden inputs for subcategories only
                updateSubcategoryInputs();
            });

            // Function to update hidden subcategory inputs
            function updateSubcategoryInputs() {
                var $container = $('#selected-subcategories-inputs');
                $container.empty();

                $('input[name="subcategories[]"]:checked').each(function () {
                    var subcategoryId = $(this).val();
                    $container.append('<input type="hidden" name="subcategories[]" value="' +
                        subcategoryId + '">');
                });
            }

            // Initialize category selection state when returning to step 2
            function initializeCategorySelection() {
                // Reset all categories visual state first
                $('.service_category').removeClass('active');
                $('.selected-checkmark').addClass('d-none');
                $('.radio-box').removeAttr('style'); // Remove any inline border styles

                // Check all pre-selected subcategories and auto-select their parent categories
                $('input[name="subcategories[]"]:checked').each(function () {
                    var $checkbox = $(this);
                    var categoryId = $checkbox.closest('.sub_category').attr('id').replace('sub_category_',
                        '');
                    var $category = $('.service_category[data-ctg="' + categoryId + '"]');

                    // Auto-select category, show checkmark, and add blue border
                    $category.addClass('active');
                    $category.find('.selected-checkmark').removeClass('d-none');
                    $category.find('.radio-box').css('border', '2px solid #020C87');
                });

                updateSubcategoryInputs();
            }

            // Initialize on page load
            initializeCategorySelection();

            // Re-initialize when returning to step 2
            $(document).on('click', '.previous', function () {
                setTimeout(function () {
                    if ($('#step-2').is(':visible')) {
                        initializeCategorySelection();
                    }
                }, 100);
            });

            // Also initialize when step 2 becomes visible (for stepper navigation)
            $(document).on('DOMNodeInserted DOMNodeRemoved', function () {
                if ($('#step-2').is(':visible') && $('#step-2').css('opacity') == '1') {
                    setTimeout(initializeCategorySelection, 50);
                }
            });
        });

        // Handle certificate exception checkbox
        $(document).on('change', 'input[name*="[exception]"]', function () {
            var $checkbox = $(this);
            var $block = $checkbox.closest('.file-upload-group');
            var $textarea = $block.find('.exception-textarea');
            var $uploadBox = $block.find('.upload-box');
            var $previewContainer = $block.find('.preview-container');

            if ($checkbox.is(':checked')) {
                $textarea.show();
                $uploadBox.css({
                    'opacity': '0.5',
                    'pointer-events': 'none',
                    'cursor': 'not-allowed'
                }); // Make upload optional visually and non-clickable

                // Clear all preview images - same as remove button functionality
                $previewContainer.find('.preview-box').remove();
                $previewContainer.empty();

                // Clear file input - same as remove button functionality
                $block.find('input[type="file"]').val('');

                // Clear any existing image from upload box
                var $uploadImg = $uploadBox.find('img');
                if ($uploadImg.length) {
                    $uploadImg.attr('src', '{{ asset("website/assets/images/upload.svg") }}');
                }
                $uploadBox.css('background-image', '');

                console.log('Certificate exception checked - images cleared and upload area disabled');
            } else {
                $textarea.hide();
                $block.find('textarea[name*="[exception_reason]"]').val(''); // Clear reason
                $uploadBox.css({
                    'opacity': '1',
                    'pointer-events': 'auto',
                    'cursor': 'pointer'
                }); // Make upload required visually and clickable

                console.log('Certificate exception unchecked - upload area enabled');
            }
        });

        // Handle custom holiday checkbox changes
        $(document).on('change', 'input[name^="custom_holidays"][type="checkbox"]', function () {
            var $checkbox = $(this);
            var $container = $checkbox.closest('.custom-holiday-div');
            var $hiddenInputs = $container.find('input[type="hidden"]');
            var $timeInputs = $container.find('input[name*="[start_time]"], input[name*="[end_time]"]');

            if ($checkbox.is(':checked')) {
                // Enable hidden inputs when checked
                $hiddenInputs.prop('disabled', false);
                $timeInputs.prop('disabled', false);
            } else {
                // Disable hidden inputs when unchecked to prevent them from being sent
                $hiddenInputs.prop('disabled', true);
                $timeInputs.prop('disabled', true);
                // Clear time values
                $timeInputs.val('');
            }
        });

        // Initialize exception checkbox state on page load
        $(document).ready(function () {
            $('input[name*="[exception]"]').each(function () {
                var $checkbox = $(this);
                var $block = $checkbox.closest('.file-upload-group');
                var $textarea = $block.find('.exception-textarea');
                var $uploadBox = $block.find('.upload-box');

                if ($checkbox.is(':checked')) {
                    $textarea.show();
                    $uploadBox.css({
                        'opacity': '0.5',
                        'pointer-events': 'none',
                        'cursor': 'not-allowed'
                    });
                } else {
                    $textarea.hide();
                    $uploadBox.css({
                        'opacity': '1',
                        'pointer-events': 'auto',
                        'cursor': 'pointer'
                    });
                }
            });

            // Initialize custom holiday checkbox state on page load
            $('input[name^="custom_holidays"][type="checkbox"]').each(function () {
                var $checkbox = $(this);
                var $container = $checkbox.closest('.custom-holiday-div');
                var $hiddenInputs = $container.find('input[type="hidden"]');
                var $timeInputs = $container.find('input[name*="[start_time]"], input[name*="[end_time]"]');

                if ($checkbox.is(':checked')) {
                    $hiddenInputs.prop('disabled', false);
                    $timeInputs.prop('disabled', false);
                } else {
                    $hiddenInputs.prop('disabled', true);
                    $timeInputs.prop('disabled', true);
                }
            });
        });
    </script>
    
    <script>
        function initIndividualServiceMap() {
            // Only use existing coordinates if we have them in the form fields (from old input or editing)
            const existingLat = document.getElementById('latitude').value;
            const existingLng = document.getElementById('longitude').value;
            const existingLocation = document.getElementById('pac-input').value;

            const userLat = existingLat && existingLat !== '' ? parseFloat(existingLat) : null;
            const userLng = existingLng && existingLng !== '' ? parseFloat(existingLng) : null;
            const userLocation = existingLocation || '';



            let map, marker;

            // Only initialize map if we have existing coordinates
            if (userLat !== null && userLng !== null) {

                // Initialize map with existing coordinates
                map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 15,
                    center: {
                        lat: userLat,
                        lng: userLng
                    }
                });

                // Create marker at existing location
                marker = new google.maps.Marker({
                    position: {
                        lat: userLat,
                        lng: userLng
                    },
                    map: map,
                    draggable: true,
                    title: userLocation || 'Your Business Location'
                });

                // Set the form values
                document.getElementById('latitude').value = userLat;
                document.getElementById('longitude').value = userLng;
            } else {

                // Show placeholder instead of map
                const mapElement = document.getElementById('map');
                mapElement.innerHTML =
                    '<div style="height: 300px; display: flex; align-items: center; justify-content: center; background: #f8f9fa; border: 2px dashed #dee2e6; color: #6c757d; text-align: center;"><div><i class="fas fa-map-marker-alt" style="font-size: 2rem; margin-bottom: 10px;"></i><br>Map will appear after you search for a location</div></div>';
            }

            // Initialize autocomplete
            const input = document.getElementById('pac-input');
            const autocomplete = new google.maps.places.Autocomplete(input);

            // Only bind to map bounds if map exists
            if (map) {
                autocomplete.bindTo('bounds', map);
            }

            // Handle place selection
            autocomplete.addListener('place_changed', function () {
                const place = autocomplete.getPlace();
                if (!place.geometry) {
                    return;
                }

                // Initialize map if it doesn't exist yet
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 15,
                        center: place.geometry.location
                    });

                    marker = new google.maps.Marker({
                        position: place.geometry.location,
                        map: map,
                        draggable: true,
                        title: 'Selected Location'
                    });

                    // Add drag listener for new marker
                    marker.addListener('dragend', function () {
                        const position = marker.getPosition();
                        document.getElementById('latitude').value = position.lat();
                        document.getElementById('longitude').value = position.lng();

                        // Reverse geocoding to update address
                        const geocoder = new google.maps.Geocoder();
                        geocoder.geocode({
                            location: position
                        }, function (results, status) {
                            if (status === 'OK' && results[0]) {
                                document.getElementById('pac-input').value = results[0]
                                    .formatted_address;
                            }
                        });
                    });
                } else {
                    // Update existing map and marker
                    if (place.geometry.viewport) {
                        map.fitBounds(place.geometry.viewport);
                    } else {
                        map.setCenter(place.geometry.location);
                        map.setZoom(17);
                    }
                    marker.setPosition(place.geometry.location);
                }

                // Update hidden inputs
                document.getElementById('latitude').value = place.geometry.location.lat();
                document.getElementById('longitude').value = place.geometry.location.lng();
            });

            // Handle marker drag (only if marker exists)
            if (marker) {
                marker.addListener('dragend', function () {
                    const position = marker.getPosition();
                    document.getElementById('latitude').value = position.lat();
                    document.getElementById('longitude').value = position.lng();

                    // Reverse geocoding to update address
                    const geocoder = new google.maps.Geocoder();
                    geocoder.geocode({
                        location: position
                    }, function (results, status) {
                        if (status === 'OK' && results[0]) {
                            document.getElementById('pac-input').value = results[0].formatted_address;
                        }
                    });
                });
            }
        }

        // Initialize when Google Maps is ready
        $(document).ready(function () {
            // Wait for Google Maps to load, then initialize
            function waitForGoogleMaps() {
                if (typeof google !== 'undefined' && typeof google.maps !== 'undefined') {
                    initIndividualServiceMap();
                } else {
                    setTimeout(waitForGoogleMaps, 100);
                }
            }
            waitForGoogleMaps();
        });

        // Toast function for notifications
        function showToast(message, type) {
            const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
            const toast = `
                            <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                                <div class="d-flex">
                                    <div class="toast-body">${message}</div>
                                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                                </div>
                            </div>
                        `;

            $('body').append(toast);
            const toastElement = $('.toast').last();
            const bsToast = new bootstrap.Toast(toastElement[0], {
                delay: 3000
            });
            bsToast.show();

            // Remove toast element after it's hidden
            toastElement.on('hidden.bs.toast', function () {
                $(this).remove();
            });
        }

        // Global function to handle issued date changes for certificates in register
        window.handleIssuedDateChangeRegister = function (issuedDateInput) {
            const $issuedInput = $(issuedDateInput);
            const issuedDate = $issuedInput.val();

            // Find the corresponding end date input in the same certificate block
            const $certificateBlock = $issuedInput.closest('.gray-card, .file-upload-group');
            const $endDateInput = $certificateBlock.find('input[name*="[end_date]"]');

            if (issuedDate) {
                // Enable end date field and restore normal styling
                $endDateInput.prop('disabled', false);
                $endDateInput.removeClass('disabled-field');
                $endDateInput.css({
                    'background-color': '#ffffff',
                    'color': '#495057',
                    'cursor': 'text',
                    'opacity': '1'
                });

                // Set minimum date for end date to be the day AFTER issued date
                const minDate = new Date(issuedDate);
                minDate.setDate(minDate.getDate() + 1);
                const minDateString = minDate.toISOString().split('T')[0];
                $endDateInput.attr('min', minDateString);

                // Clear end date if it's before or equal to the issued date
                const currentEndDate = $endDateInput.val();
                if (currentEndDate && currentEndDate <= issuedDate) {
                    $endDateInput.val('');
                    $endDateInput.siblings('.error').text('End date must be after issued date').show();
                } else {
                    $endDateInput.siblings('.error').text('').hide();
                }
            } else {
                // Disable and gray out end date field when no issued date is set
                $endDateInput.prop('disabled', true);
                $endDateInput.addClass('disabled-field');
                $endDateInput.css({
                    'background-color': '#e9ecef',
                    'color': '#6c757d',
                    'cursor': 'not-allowed',
                    'opacity': '0.7'
                });
                $endDateInput.val('');
                $endDateInput.removeAttr('min');
                $endDateInput.siblings('.error').text('').hide();
            }
        };

        // Handle issued date changes for all certificates
        $(document).on('change', 'input[name*="[issued_date]"], input.issued-date', function () {
            handleIssuedDateChangeRegister(this);
        });

        // Handle end date validation
        $(document).on('change', 'input[name*="[end_date]"], input.end-date', function () {
            const $endInput = $(this);
            const endDate = $endInput.val();

            // Find the corresponding issued date input in the same certificate block
            const $certificateBlock = $endInput.closest('.gray-card, .file-upload-group');
            const $issuedInput = $certificateBlock.find('input[name*="[issued_date]"], input.issued-date');
            const issuedDate = $issuedInput.val();

            if (issuedDate && endDate) {
                if (endDate <= issuedDate) {
                    $endInput.siblings('.error').text('End date must be after issued date').show();
                    $endInput.val('');
                } else {
                    $endInput.siblings('.error').text('').hide();
                }
            }
        });

        // Initialize existing certificates
        $(document).ready(function () {
            setTimeout(function () {
                $('.gray-card, .file-upload-group').each(function () {
                    const $block = $(this);
                    const $issuedInput = $block.find(
                        'input[name*="[issued_date]"], input.issued-date');
                    const $endInput = $block.find('input[name*="[end_date]"], input.end-date');

                    if ($issuedInput.length && $endInput.length) {
                        if ($issuedInput.val()) {
                            handleIssuedDateChangeRegister($issuedInput[0]);
                        } else {
                            // Disable end date field if no issued date
                            $endInput.prop('disabled', true);
                            $endInput.addClass('disabled-field');
                            $endInput.css({
                                'background-color': '#e9ecef',
                                'color': '#6c757d',
                                'cursor': 'not-allowed',
                                'opacity': '0.7'
                            });
                        }
                    }
                });
            }, 200);
        });

        // Add CSS for disabled field styling
        if (!$('#certificate-date-styles-register').length) {
            $('<style id="certificate-date-styles-register">')
                .text(`
                        .disabled-field {
                            background-color: #e9ecef !important;
                            color: #6c757d !important;
                            cursor: not-allowed !important;
                            opacity: 0.7 !important;
                        }
                        .disabled-field:focus {
                            box-shadow: none !important;
                            border-color: #ced4da !important;
                        }
                        input[name*="[end_date]"]:disabled,
                        input.end-date:disabled {
                            background-color: #e9ecef !important;
                            color: #6c757d !important;
                            cursor: not-allowed !important;
                            opacity: 0.7 !important;
                        }
                        .error {
                            font-size: 12px;
                            margin-top: 5px;
                            display: block;
                        }
                    `)
                .appendTo('head');
        }
    </script>
@endpush