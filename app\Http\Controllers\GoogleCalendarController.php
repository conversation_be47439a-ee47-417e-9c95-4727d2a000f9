<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use App\Models\GoogleCalendar;
use App\Models\Booking;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class GoogleCalendarController extends Controller
{
    protected $clientId;
    protected $clientSecret;
    protected $redirectUri;

    public function __construct()
    {
        $this->clientId = config('services.google_calendar.client_id');
        $this->clientSecret = config('services.google_calendar.client_secret');
        $this->redirectUri = config('services.google_calendar.redirect');
    }

    public function redirectToGoogle()
    {
        $query = http_build_query([
            'client_id' => $this->clientId,
            'redirect_uri' => $this->redirectUri,
            'response_type' => 'code',
            'scope' => 'https://www.googleapis.com/auth/calendar',
            'access_type' => 'offline',
            'prompt' => 'consent',
        ]);

        return redirect('https://accounts.google.com/o/oauth2/auth?' . $query);
    }

    public function handleGoogleCallback(Request $request)
    {
        $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
            'redirect_uri' => $this->redirectUri,
            'grant_type' => 'authorization_code',
            'code' => $request->code,
        ]);
        $tokens = $response->json();
        if (isset($tokens['access_token'])) {
            GoogleCalendar::updateOrCreate(
                ['user_id' => Auth::id()],
                [
                    'google_access_token' => $tokens['access_token'],
                    'google_refresh_token' => $tokens['refresh_token'] ?? null,
                    'google_token_expires_at' => now()->addSeconds($tokens['expires_in']),
                    'google_calendar_connected' => true,
                ]
            );

            // Create a "Google Calendar Configured" event in the user's calendar
            $this->createGoogleCalendarConfiguredEvent($tokens['access_token']);

            return redirect('/register/professional')->with([
                'type' => 'success',
                'title' => 'Done',
                'message' => 'Google Calendar connected!'
            ]);
        }
       return redirect('/register/professional')->with('error', '!Google Calendar Failed to connect!');
    }

    /**
     * Create a "Google Calendar Configured" event in the user's calendar
     */
    private function createGoogleCalendarConfiguredEvent(string $accessToken): void
    {
        try {
            $eventData = [
                'summary' => 'Google Calendar Configured',
                'description' => 'Your Google Calendar has been successfully connected to the booking system. You will now receive automatic calendar events for your bookings.',
                'start' => [
                    'date' => now()->format('Y-m-d'),
                ],
                'colorId' => '10', // Green color for success
            ];
            $response = Http::withToken($accessToken)
                ->post('https://www.googleapis.com/calendar/v3/calendars/primary/events', $eventData);

            if ($response->successful()) {
                \Log::info('Google Calendar Configured event created successfully for user: ' . Auth::id());
            } else {
                \Log::warning('Failed to create Google Calendar Configured event: ' . $response->body());
            }
        } catch (\Exception $e) {
            \Log::error('Error creating Google Calendar Configured event: ' . $e->getMessage());
        }
    }

    public function createGoogleEventForBooking(Booking $booking): ?string
    {
        // Check if provider has Google calendar configured
        $calendar = GoogleCalendar::where('user_id', $booking->provider_id)->first();
        if (!$calendar || !$calendar->google_calendar_connected || $calendar->isTokenExpired()) {
            return null;
        }
        // Load service details for event
        $booking->load('service');
        $serviceName = $booking->service->name ?? 'Service';
        $servicePrice = $booking->service_price ?? $booking->total_amount ?? 0;

        $eventData = [
            'summary' => "Booking #{$booking->booking_number} - {$serviceName}",
            'start' => [
                'dateTime' => Carbon::parse($booking->booking_time)->format('H:i'),
                'timeZone' => 'UTC',
            ],
            'location' => $booking->provider->profile->location ?? 'No location provided',
            'description' => "Booking Details\n" .
                           "Booking Number: {$booking->booking_number}\n" .
                           "Service: {$serviceName}\n" .
                           "Price: $" . number_format($servicePrice, 2) . "\n" .
                           "Duration: {$booking->duration} minutes",
        ];

        $response = Http::withToken($calendar->google_access_token)
            ->post('https://www.googleapis.com/calendar/v3/calendars/primary/events', $eventData);

        if ($response->successful()) {
            return $response->json()['id']; // Return event ID for storage
        }

        return null;
    }

    public function updateGoogleEventForBooking(Booking $booking, string $eventId): bool
    {
        // Check if provider has Google calendar configured
        $calendar = GoogleCalendar::where('user_id', $booking->provider_id)->first();
        if (!$calendar || !$calendar->google_calendar_connected || $calendar->isTokenExpired()) {
            return false;
        }
        // Load service details for event
        $booking->load('service');
        $serviceName = $booking->service->name ?? 'Service';
        $servicePrice = $booking->service_price ?? $booking->total_amount ?? 0;

        $eventData = [
            'summary' => "Booking #{$booking->booking_number} - {$serviceName} (Updated)",
            'start' => [
                'dateTime' => Carbon::parse($booking->booking_time)->format('H:i'),
                'timeZone' => 'UTC',
            ],
            'location' => $booking->provider->profile->location ?? 'No location provided',
            'description' => "Updated Booking Details\n" .
                           "Booking Number: {$booking->booking_number}\n" .
                           "Service: {$serviceName}\n" .
                           "Price: $" . number_format($servicePrice, 2) . "\n" .
                           "Duration: {$booking->duration} minutes",
        ];
        $response = Http::withToken($calendar->google_access_token)
            ->put("https://www.googleapis.com/calendar/v3/calendars/primary/events/{$eventId}", $eventData);

        return $response->successful();
    }

    public function deleteGoogleEventForBooking(int $providerId, string $eventId): bool
    {
        $calendar = GoogleCalendar::where('user_id', $providerId)->first();
        if (!$calendar || !$calendar->google_calendar_connected || $calendar->isTokenExpired()) {
            return false;
        }

        $response = Http::withToken($calendar->google_access_token)
            ->delete("https://www.googleapis.com/calendar/v3/calendars/primary/events/{$eventId}");

        return $response->successful();
    }

    public function removeGoogleCalendar()
    {
        $user = auth()->user();
        $googleCalendar = GoogleCalendar::where('user_id', $user->id)->first();
        if (!$googleCalendar) {
            return redirect()->back()->with('error', 'No Google Calendar found for this user.');
        }
        $response = Http::asForm()->post('https://oauth2.googleapis.com/revoke', [
            'token' => $googleCalendar->google_refresh_token,
        ]);
        if ($response->failed()) {
            return redirect()->back()->with('error', 'Failed to revoke Google Calendar access.');
        }
        $googleCalendar->delete();
        return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Calendar Removed Successfully!!']);
    }
}
