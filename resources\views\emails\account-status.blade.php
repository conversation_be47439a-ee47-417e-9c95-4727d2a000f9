<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Account {{ ucfirst($action) }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #ffffff;
            padding: 30px;
            border: 1px solid #e9ecef;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-radius: 0 0 8px 8px;
            font-size: 14px;
            color: #6c757d;
        }
        @if($action === 'deleted')
        .status-box {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .status-icon {
            color: #721c24;
            font-size: 18px;
            margin-right: 10px;
        }
        @else
        .status-box {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .status-icon {
            color: #856404;
            font-size: 18px;
            margin-right: 10px;
        }
        @endif
    </style>
</head>
<body>
    <div class="header">
        <h2>Account {{ ucfirst($action) }}</h2>
    </div>
    
    <div class="content">
        <p>Dear {{ $user->name }},</p>
        
        <div class="status-box">
            <span class="status-icon">
                @if($action === 'deleted')
                    🗑️
                @else
                    ⚠️
                @endif
            </span>
            <strong>Your account has been {{ $action }} by an administrator.</strong>
        </div>
        
        @if($action === 'deleted')
        <p>We are writing to inform you that your {{ ucfirst($userType) }} account has been permanently deleted from our system. This action is irreversible and all your data has been removed.</p>
        
        <p><strong>What this means:</strong></p>
        <ul>
            <li>All your account data has been permanently removed</li>
            <li>You will no longer receive any notifications from our platform</li>
            <li>You cannot recover your account or data</li>
            <li>If you wish to use our services again, you will need to create a new account</li>
        </ul>
        
        <p>If you believe this deletion was made in error, please contact our support team immediately as account recovery may not be possible.</p>
        @else
        <p>We are writing to inform you that your {{ ucfirst($userType) }} account has been deactivated. This means you will no longer be able to access your account or use our services.</p>
        
        <p>If you believe this deactivation was made in error, please contact our support team immediately.</p>
        @endif
        
        <p><strong>Account Details:</strong></p>
        <ul>
            <li><strong>Name:</strong> {{ $user->name }}</li>
            <li><strong>Email:</strong> {{ $user->email }}</li>
            <li><strong>Account Type:</strong> {{ ucfirst($userType) }}</li>
            <li><strong>{{ ucfirst($action) }} On:</strong> {{ now()->format('F d, Y \a\t g:i A') }}</li>
        </ul>
        
        @if($action === 'deleted')
        <p>Thank you for your time with us.</p>
        @else
        <p>Thank you for your understanding.</p>
        @endif
        
        <p>Best regards,<br>
        {{ config('app.name') }} Team</p>
    </div>
    
    <div class="footer">
        <p>This is an automated message. Please do not reply to this email.</p>
        <p>&copy; {{ date('Y') }} {{ config('app.name') }}. All rights reserved.</p>
    </div>
</body>
</html>
