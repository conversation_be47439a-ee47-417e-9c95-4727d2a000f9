@forelse ($transactions as $transaction)
    <tr>
        <td data-label="BOOKING ID">{{ $transaction->booking_number ?? 'N/A' }}</td>
        <td data-label="CUSTOMER NAME">{{ $transaction->customer->name ?? 'N/A' }}</td>
        <td data-label="SERVICE NAME">{{ $transaction->service->name ?? 'N/A' }}</td>
        <td data-label="PROFESSIONAL">{{ $transaction->provider->name ?? 'N/A' }}</td>
        <td data-label="AMOUNT">${{ number_format($transaction->total_amount ?? 0, 2) }}</td>
        <td data-label="STATUS" class="request-status status
            @if ($transaction->status == 1) paid-status
            @elseif($transaction->status == 0) pending-status
            @elseif($transaction->status == 2) cancelled-status unpaid-status
            @else unpaid-status @endif">
          <span class="status-text">  {{ $transaction->status_text }} </span>
        </td>
        <td data-label="DATE & TIME">
            {{ $transaction->created_at ? \Carbon\Carbon::parse($transaction->created_at)->format('d M, Y') : 'N/A' }}
            <span class="wallet-time status unpaid-status ps-4">
                {{ $transaction->created_at ? \Carbon\Carbon::parse($transaction->created_at)->format('g:i A') : '' }}
            </span>
        </td>
        <td data-label="ACTION">
            <div class="dropdown">
                <button class="drop-btn" type="button" id="dropdownMenuButton{{ $transaction->id }}"
                    data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ $transaction->id }}">
                    <li>
                        <a class="dropdown-item view fs-14 regular" href="{{ route('booking.detail', ['ids' => $transaction->ids, 'booking_number' => $transaction->booking_number]) }}">
                            <i class="bi bi-eye view-icon"></i>
                            View Details
                        </a>
                    </li>
                </ul>
            </div>
        </td>
    </tr>
@empty
    <tr>
        <td colspan="8" class="text-center">No Customer Transactions Found</td>
    </tr>
@endforelse
