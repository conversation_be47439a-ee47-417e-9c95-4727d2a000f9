@if ($unregistered_users->count() > 0)
    @foreach ($unregistered_users as $user)
        <tr>
            <td data-label="EMAIL ADDRESS">{{ $user->email }}</td>
            <td data-label="STATUS" class="professional-status status paid-status">
               <span class="status-text"> Unregistered </span>
            </td>
            <td data-label="JOINED DATE">
                {{ $user->created_at->format('M d, Y') }}</td>
            <td data-label="">
                <div class="dropdown">
                    <button class="drop-btn" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown"
                        aria-expanded="false">
                        <i class="bi bi-three-dots-vertical"></i>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li>
                            <form action="{{ route('professional.delete', $user->ids) }}" method="POST" class="delete-form">
                                @csrf
                                @method('DELETE')
                                <button class="dropdown-item cancel fs-14 regular" type="button" onclick="showDeleteConfirmation(this)">
                                    <i class="fa-solid fa-xmark cancel-icon"></i> Delete
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </td>
        </tr>
    @endforeach
@else
    <tr>
        <td colspan="4" class="text-center py-5">
            <div class="d-flex flex-column align-items-center">
                <i class="fas fa-user-slash fa-3x text-muted mb-3"></i>
                <h5 class="text-muted mb-2">No unregistered users found</h5>
                <p class="text-muted">All users have completed their registration</p>
            </div>
        </td>
    </tr>
@endif
