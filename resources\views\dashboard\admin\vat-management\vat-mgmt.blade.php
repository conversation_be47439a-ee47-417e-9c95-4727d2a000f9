@extends('dashboard.layout.master')
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">VAT Management</h6>
                        <p class="fs-14 sora light-black m-0"> Set up, manage, and track VAT rules and rates here. </p>
                    </div>
                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap mb-5">
                            <div class="search_box  pb-sm-0 pb-5">
                                <label for="vatSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="vatSearchInput"
                                    placeholder="Search..." />
                            </div>

                            <!-- add-city btn -->
                            @can('vatmanagements-create')
                                <div class="search_box d-block ms-auto">
                                    <a class="add-btn" data-bs-toggle="modal" data-bs-target="#add-country">
                                        <i class="fa-solid fa-plus me-3"></i> Add Country-Vat
                                    </a>
                                </div>
                            @endcan

                        </div>
                        <table id="responsiveTable" class="manage-holiday vat-managment-table display w-100">
                            <thead>
                                <tr>
                                    <th>COUNTRY NAME</th>
                                    <th class="min-w-500px">VAT RATE (%)</th>
                                    <th>ACTION</th>
                                </tr>
                            </thead>
                            <tbody id="vatTableBody">
                                @include('dashboard.admin.vat-management.partials.vat-table', [
                                    'vatmanagements' => $vatmanagements,
                                ])
                            </tbody>
                        </table>

                        <!-- Load More Button -->
                        <div class="text-center mt-4" id="loadMoreContainer"
                            style="{{ $totalCount > 10 ? '' : 'display: none;' }}">
                            <button type="button" class=" add-btn" id="loadMoreBtn">
                                <i class="fas fa-plus me-2"></i>Load More
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.vat-management.modal.add-country-modal')
    @include('dashboard.admin.vat-management.modal.edit-vat-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for elements with data-control="select2"
            $('[data-control="select2"]').each(function() {
                $(this).select2({
                    placeholder: $(this).data('placeholder') || 'Select an option',
                    allowClear: true,
                    width: '100%'
                });
            });
            let searchTimeout;
            let isLoading = false;
            let currentSearch = '';
            let currentVatManagementId = null;
            let currentOffset = 10; // Start from 10 since first 10 are already loaded

            // Add form validation

            $("#vatForm").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    if (element.attr("name") === "country_id") {
                        // Place error after the Select2 container
                        error.insertAfter(element.next('.select2'));
                    } else if (element.closest('.input-group').length) {
                        // Place error after input-group (VAT field)
                        error.insertAfter(element.closest('.input-group'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                rules: {
                    country_name: {
                        required: true
                    },
                    vat: {
                        required: true,
                        number: true,
                        min: 0,
                        max: 100
                    }
                },
                messages: {
                    country_name: {
                        required: "Please enter country name"
                    },
                    vat: {
                        required: "Please enter VAT value",
                        number: "Please enter a valid number",
                        min: "VAT cannot be negative",
                        max: "VAT cannot exceed 100%"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });

            // Edit form validation
            $("#editVatForm").validate({
                errorClass: "error",
                errorElement: "label",
                errorPlacement: function(error, element) {
                    error.insertAfter(element);
                },
                rules: {
                    vat: {
                        required: true,
                        number: true,
                        min: 0,
                        max: 100
                    }
                },
                messages: {
                    vat: {
                        required: "Please enter VAT value",
                        number: "Please enter a valid number",
                        min: "VAT cannot be negative",
                        max: "VAT cannot exceed 100%"
                    }
                },
                submitHandler: function(form) {
                    var formData = new FormData(form);
                    formData.append('_method', 'PUT');
                    $.ajax({
                        url: '/admin/vatmanagements/' + currentVatManagementId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-vat-modal').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            });
                            setTimeout(() => {
                                // Reload the page to maintain all loaded records
                                location.reload();
                            }, 1500);
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                }
            });

            // Search functionality with debounce
            $('#vatSearchInput').on('keyup', function() {
                let search = $(this).val().trim();

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounce (300ms delay)
                searchTimeout = setTimeout(function() {
                    currentSearch = search;
                    currentOffset = 0; // Reset offset for search
                    searchVatManagement(); // Search function
                }, 300);
            });

            // Function to search VAT management records
            function searchVatManagement() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object - only include non-empty values
                let data = {
                    offset: currentOffset
                };

                // Only add search if it's not empty (but "0" is valid)
                if (currentSearch !== '') {
                    data.search = currentSearch;
                }

                $.ajax({
                    url: "{{ route('vatmanagements.index') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            // Replace table content for search
                            $('#vatTableBody').html(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();

                            // Show success toast with result count only when searching
                            if (data.search) {
                                if (response.count === 0) {
                                    showToast('No results found for your search criteria', 'warning');
                                } else {
                                    showToast(`Found ${response.count} result(s)`, 'success');
                                }
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('VAT search error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Search failed. Please check your connection.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Load more functionality
            $('#loadMoreBtn').on('click', function() {
                loadMoreVatManagement();
            });

            // Function to load more VAT management records
            function loadMoreVatManagement() {
                if (isLoading) return;

                isLoading = true;

                // Prepare data object
                let data = {
                    offset: currentOffset
                };

                // Only add search if it's not empty (but "0" is valid)
                if (currentSearch !== '') {
                    data.search = currentSearch;
                }

                $.ajax({
                    url: "{{ route('vatmanagements.load-more') }}",
                    type: "GET",
                    data: data,
                    success: function(response) {
                        if (response.success) {
                            // Append content for load more
                            $('#vatTableBody').append(response.html);

                            // Update offset for next load more
                            currentOffset = response.next_offset;

                            // Update load more button visibility
                            updateLoadMoreButton(response);

                            // Rebind edit buttons
                            bindEditButtons();
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Load more error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Failed to load more records. Please try again.', 'error');
                    },
                    complete: function() {
                        isLoading = false;
                    }
                });
            }

            // Function to update load more button visibility
            function updateLoadMoreButton(response) {
                // Show/hide load more button based on whether there are more records
                if (response.has_more && response.count > 0) {
                    $('#loadMoreContainer').show();
                } else {
                    $('#loadMoreContainer').hide();
                }
            }

            // Function to bind edit buttons (needed after AJAX content load)
            function bindEditButtons() {
                $('.edit-vatmanagement').off('click').on('click', function(e) {
                    e.preventDefault();
                    var vatManagementId = $(this).data('id');
                    currentVatManagementId = vatManagementId;
                    $.ajax({
                        url: '/admin/vatmanagements/' + vatManagementId + '/edit',
                        method: 'GET',
                        success: function(data) {
                            $('#edit_country_name').val(data.country_name);
                            $('#edit_vat').val(data.vat);
                            $('#edit-vat-modal').modal('show');
                        },
                        error: function(xhr, status, error) {
                            console.error('Error fetching VAT management data:', error);
                            alert('Error loading data. Please try again.');
                        }
                    });
                });
            }

            // Initial binding of edit buttons
            bindEditButtons();

            // Toast function for notifications
            function showToast(message, type) {
                const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                $('body').append(toast);
                const toastElement = $('.toast').last();
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    delay: 3000
                });
                bsToast.show();

                // Remove toast element after it's hidden
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

            // Reset edit modal validation when opened
            $('#edit-vat-modal').on('show.bs.modal', function() {
                // Clear any existing validation errors
                if ($("#editVatForm").data('validator')) {
                    $("#editVatForm").validate().resetForm();
                }
            });

            // Initialize Select2 when add country modal is shown
            $('#add-country').on('shown.bs.modal', function() {
                // Reset the form first to clear any old values
                $('#vatForm')[0].reset();

                // Clear any select2 selections if they exist
                $('[data-control="select2"]').val(null).trigger('change');

                // Clear any validation errors
                if ($("#vatForm").data('validator')) {
                    $("#vatForm").validate().resetForm();
                }

                // Remove any error styling
                $('#vatForm input, #vatForm select').removeClass('error');
                $('#vatForm .error').remove();

                // Re-initialize Select2 for the country dropdown in case it wasn't initialized
                $('#service').select2({
                    placeholder: 'Select Country',
                    allowClear: true,
                    width: '100%',
                    dropdownParent: $('#add-country')
                });
            });

            // Reset modal when it's hidden (closed)
            $('#add-country').on('hidden.bs.modal', function() {
                // Reset the form
                $('#vatForm')[0].reset();

                // Clear Select2 selection
                $('#service').val(null).trigger('change');

                // Clear any validation errors
                if ($("#vatForm").data('validator')) {
                    $("#vatForm").validate().resetForm();
                }

                // Remove any error styling
                $('#vatForm input, #vatForm select').removeClass('error');
                $('#vatForm .error').remove();
            });

        });
    </script>
@endpush
