@extends('layouts.app')

@section('content')
    <div class="container-fluid login-page">
        <div class="row">
            <div
                class="col-md-6 px-lg-20 px-md-20 px-sm-10 d-flex flex-column align-items-center justify-content-center my-10">
                <form class="form w-100" id="loginForm" method="POST" action="{{ route('login') }}">
                    @csrf
                    <div class="text-center mb-8">
                        <h1 class="text-dark fw-bolder mb-3">Sign In</h1>
                    </div>
                    <div class="form-group fv-row mb-8">
                        <input id="email" type="email" placeholder="Email"
                            class="form-control  bg-transparent" name="email"
                            value="{{ old('email') }}" required autocomplete="email" autofocus>
                        @error('email')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>
                    <div class="form-group fv-row mb-3 position-relative login-icon">
                        <input id="password" type="password" placeholder="Password" class="form-control  bg-transparent"
                            name="password" required autocomplete="current-password">

                        @error('password')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror


                        <span id="toggle-password"
                            class=" btn-sm btn-icon position-absolute translate-middle end-0 pb-12 pe-2 ">
                            <i class="fa-solid fa-eye"></i>
                            <i class="fa-solid fa-eye-slash d-none"></i>
                        </span>
                    </div>


                    <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
                        <div></div>
                        <a href="{{ route('password.request') }}" class="blue-text">Forgot Password ?</a>
                    </div>

                    <div class="d-grid mb-6">
                        <button type="submit" id="kt_sign_in_submit" class="blue-btn">
                            <span class="indicator-label">Sign In</span>
                            <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                        </button>
                    </div>

                    <div class="text-gray-500 text-center fw-semibold fs-6">Not a Member yet?
                        <a href="{{ route('register') }}" class="blue-text">Sign up</a>
                    </div>
                </form>

                <div class="site_logo pt-10">
                    <a href="{{ url('/') }}" class="text-center">
                        <img src="{{ asset('website') }}/assets/images/header_primary.svg" alt="icon">
                        <h4 class="blue-text pt-2"> Stylenest </h4>
                    </a>
                </div>
            </div>

            <div class="col-md-6 login-side-image">
                <img src="{{ asset('website') }}/assets/images/login-banner.png" alt="icon">
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function() {
            // Check for session messages and display them
            @if(session('title') && session('message'))
                Swal.fire({
                    title: "{{ session('title') }}",
                    text: "{{ session('message') }}",
                    icon: "{{ session('type') === 'error' ? 'error' : 'success' }}"
                });
            @endif

            $('#toggle-password').on('click', function() {
                var passwordField = $('#password');
                var passwordFieldType = passwordField.attr('type');

                if (passwordFieldType === 'password') {
                    passwordField.attr('type', 'text');
                    $(this).find('.fa-eye-slash').removeClass('d-none');
                    $(this).find('.fa-eye').addClass('d-none');
                } else {
                    passwordField.attr('type', 'password');
                    $(this).find('.fa-eye').removeClass('d-none');
                    $(this).find('.fa-eye-slash').addClass('d-none');
                }
            });

            // Form validation
            $('#loginForm').validate({
                rules: {
                    email: {
                        required: true,
                        email: true
                    },
                    password: {
                        required: true
                    }
                },
                messages: {
                    email: {
                        required: "Please Enter Your Email Address",
                        email: "Please Enter Valid Email Address"
                    },
                    password: {
                        required: "Please Enter Your Password"
                    }
                },
                errorPlacement: function(error, element) {
                    error.addClass('invalid-feedback');
                    element.closest(".form-group").append(error);
                },
                submitHandler: function(form) {
                    // Prevent default form submission
                    event.preventDefault();
                    
                    // Get form data
                    var formData = $('#loginForm').serialize();
                    
                    // Disable submit button and show loading
                    var submitBtn = $('#kt_sign_in_submit');
                    var originalText = submitBtn.html();
                    submitBtn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Signing in...');
                    
                    // AJAX request
                    $.ajax({
                        url: "{{ route('login') }}",
                        type: "POST",
                        data: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest',
                            'Accept': 'application/json'
                        },
                        success: function(response) {
                            console.log('Login response:', response);
                            
                            // Check if response exists and contains error about inactive account
                            if (response && typeof response === 'object' && response.error && response.message) {
                                Swal.fire({
                                    title: response.title || "Account Issue",
                                    text: response.message,
                                    icon: "error"
                                });
                            } else if (response && response.success) {
                                // Login was successful - redirect to dashboard
                                console.log('Login successful, redirecting to dashboard...');
                                window.location.href = response.redirect_url || "{{ route('dashboard') }}";
                            } else {
                                // Fallback for successful login (no specific response)
                                console.log('Login successful, redirecting to dashboard...');
                                window.location.href = "{{ route('dashboard') }}";
                            }
                        },
                        error: function(xhr) {
                            // Handle different types of errors
                            if (xhr.status === 422) {
                                // Validation errors
                                var errors = xhr.responseJSON.errors;
                                var errorMessage = '';
                                for (var field in errors) {
                                    errorMessage += errors[field][0] + '\n';
                                }
                                Swal.fire({
                                    title: "Validation Error",
                                    text: errorMessage,
                                    icon: "error"
                                });
                            } else if (xhr.status === 401) {
                                // Authentication failed
                                Swal.fire({
                                    title: "Login Failed",
                                    text: "Invalid credentials. Please check your email and password.",
                                    icon: "error"
                                });
                            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                                // Custom error message
                                Swal.fire({
                                    title: xhr.responseJSON.title || "Error",
                                    text: xhr.responseJSON.message,
                                    icon: "error"
                                });
                            } else {
                                // Generic error
                                Swal.fire({
                                    title: "Login Failed",
                                    text: "An error occurred. Please try again.",
                                    icon: "error"
                                });
                            }
                        },
                        complete: function() {
                            // Re-enable submit button
                            submitBtn.prop('disabled', false).html(originalText);
                        }
                    });
                    
                    return false; // Prevent form submission
                }
            });
        });
    </script>
@endpush
