<div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard business-home">
    <div id="kt_app_content_container" class="app-container container-fluid">
        <section class="business-home-sec padding-block">
            <div class="container">
                <div class="row">
                    <div class="col-md-12">
                        <h6 class="semi_bold">Welcome, {{ $user->name ?? 'Service Provider' }}</h6>
                        <p class="fs-14 normal">Here's your business overview for today.</p>
                    </div>
                </div>
                <div class="row row-gap-5 mb-10 card-wrapper">
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-blue">
                                        @include('svg.dollar')
                                    </div>
                                </div>
                                <div class="card-body w-150px">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Revenue
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $totalRevenue ?? 0 }}" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 {{ ($revenueChange ?? 0) >= 0 ? 'green green-box' : 'red red-box' }}">
                                        <i class="fa-solid fa-arrow-{{ ($revenueChange ?? 0) >= 0 ? 'up' : 'down' }}"
                                           style="color: {{ ($revenueChange ?? 0) >= 0 ? '#059669' : '#dc2626' }};"></i>
                                        {{ abs($revenueChange ?? 0) }}%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-light-green">
                                        @include('svg.sales')
                                    </div>
                                </div>
                                <div class="card-body w-150px ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        This Month
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $currentMonthRevenue ?? 0 }}" data-kt-countup-prefix="$">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 {{ ($revenueChange ?? 0) >= 0 ? 'green green-box' : 'red red-box' }}">
                                        <i class="fa-solid fa-arrow-{{ ($revenueChange ?? 0) >= 0 ? 'up' : 'down' }}"
                                           style="color: {{ ($revenueChange ?? 0) >= 0 ? '#059669' : '#dc2626' }};"></i>
                                        {{ abs($revenueChange ?? 0) }}%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-purple">
                                        @include('svg.earning')
                                    </div>
                                </div>
                                <div class="card-body w-150px ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Completed Bookings
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $completedBookings ?? 0 }}">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up" style="color: #059669;"></i>
                                        {{ $completedBookings ?? 0 > 0 ? '100' : '0' }}%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-3 col-lg-6 col-sm-6 col-12">
                        <a href="javascript:void(0)">
                            <div class=" card-box d-flex flex-row gap-3 align-items-center">
                                <div class="card-header">
                                    <div class="icon_cards bg-orange">
                                        @include('svg.booking')
                                    </div>
                                </div>
                                <div class="card-body w-75 ">
                                    <p class="fs-14 opacity-6 light-black m-0">
                                        Total Bookings
                                    </p>
                                    <p class="fs-24 semi_bold sora dark-black m-0" data-kt-countup="true"
                                        data-kt-countup-value="{{ $totalBookings ?? 0 }}">
                                    </p>
                                </div>
                                <div class="card-footer w-50">
                                    <div class="fs-12 w-700 green green-box">
                                        <i class="fa-solid fa-arrow-up" style="color: #059669;"></i>
                                        {{ $totalBookings ?? 0 > 0 ? '100' : '0' }}%
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="row row-gap-7">
                    <div class="col-lg-6 col-md-12">
                        <div class="card-box">
                            <div class="d-flex justify-content-between align-items-center mb-3 px-2">
                                <p class="fs-18 semi_bold sora mb-0">Upcoming Bookings</p>
                                <a href="{{route('booking')}}" class="view-all-btn">View All<i
                                        class="fa-solid fa-chevron-right arrow"></i>
                                </a>
                            </div>
                            <div class="cards">
                                @forelse($upcomingBookingsList ?? [] as $booking)
                                    <div class="booking_card d-flex justify-content-between align-items-center flex-row">
                                        <div class="left d-flex align-items-center">
                                            <div class="date-box">
                                                <div class="day">{{ \Carbon\Carbon::parse($booking->booking_date)->format('d') }}</div>
                                                <div class="month">{{ \Carbon\Carbon::parse($booking->booking_date)->format('M') }}</div>
                                            </div>
                                            <div class="details">
                                                <div class="service-name">{{ $booking->service->name ?? 'Service' }}</div>
                                                <div class="service-time">
                                                    {{ \Carbon\Carbon::parse($booking->booking_date)->format('D, d M Y') }}
                                                    {{ \Carbon\Carbon::parse($booking->booking_time)->format('H:i') }}
                                                    <span class="status {{ $booking->status == 1 ? 'complete' : ($booking->status == 0 ? 'booked' : 'cancelled') }}">
                                                        {{ $booking->status == 1 ? 'Complete' : ($booking->status == 0 ? 'Booked' : 'Cancelled') }}
                                                    </span>
                                                </div>
                                                <div class="customer-info fs-14 normal sora mt-2 light-black">
                                                    <span class="opacity-6">For</span> {{ $booking->user_id ? $booking->customer->name : $booking->client_name  }}
                                                    <span class="opacity-6">With</span> {{ $user->name ?? 'You' }}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="price fs-20 sora semi_bold">${{ number_format($booking->total_amount ?? 0, 2) }}</div>
                                    </div>
                                @empty
                                    <div class="text-center py-4">
                                        <p class="fs-14 normal sora light-black opacity-6">No upcoming bookings found</p>
                                    </div>
                                @endforelse
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card-box">
                            <div class="row">
                                <div class="col-md-12 d-flex justify-content-between">
                                    <div class="weekly-content">
                                        <p class="fs-18 semi_bold sora">Booking Overview</p>
                                        <p class="fs-14 normal sora light-black" id="period-description">Last 7 days</p>
                                        <p class="fs-32 normal sora" id="total-bookings-display">{{ $weeklyBookingData['total'] ?? 0 }}</p>
                                    </div>
                                    <div class="select-wrapper">
                                        <select class="normal booking-period-dropdown" id="booking-period-dropdown" style="border: 1px solid #e1e5e9; border-radius: 6px; padding: 8px 12px; background: white; font-size: 14px; color: #5e6278;">
                                            <option value="week">This Week</option>
                                            <option value="month">This Month</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div id="monthly-customers-container" class="pt-3">
                                <canvas id="booking_chart" class="mh-400px"> </canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card-box ">
                            <div class="d-flex justify-content-between align-items-center mb-3 px-2">
                                <p class="fs-18 semi_bold sora mb-0">Booked Time</p>
                                <a href="{{url('booking')}}" class="view-all-btn ">View All<i
                                        class="fa-solid fa-chevron-right arrow"></i>
                                </a>
                            </div>
                            @php
                                $colors = ['bg-blue', 'bg-light-green', 'bg-purple', 'bg-orange', 'bg-blue', 'bg-light-green', 'bg-purple'];
                            @endphp
                            @forelse($bookingTimeStats ?? [] as $index => $stat)
                                <div class="book-time-card mb-5">
                                    <div class="d-flex align-items-center justify-content-between flex-wrap w-100 gap-3">
                                        <!-- Day -->
                                        <div class="semi_bold fs-14 light-black sora opacity-8"
                                            style="min-width: 0; flex: 1;">
                                            <strong>{{ $stat['day'] }}</strong>
                                        </div>

                                        <!-- Time -->
                                        {{-- <div class="semi_bold fs-14 light-black sora opacity-8"
                                            style="min-width: 0; flex: 1;">
                                            {{ $stat['time_range'] }}
                                        </div> --}}

                                        <!-- Progress with percent -->
                                        <div class="d-flex align-items-center" style="min-width: 0; flex: 1;">
                                            <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                                <div class="progress-bar {{ $colors[$index % count($colors)] }}"
                                                     style="width: {{ $stat['percentage'] }}%;"></div>
                                            </div>
                                            <span class="semi_bold fs-14 sora black">{{ $stat['percentage'] }}%</span>
                                        </div>
                                    </div>
                                </div>
                            @empty
                                <div class="text-center py-4">
                                    <p class="fs-14 normal sora light-black opacity-6">No booking data available</p>
                                </div>
                            @endforelse


                        </div>

                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card-box">
                            <div class="notification-bg">
                                <div class="d-flex justify-content-between align-items-center mb-3 px-2">
                                    <p class="fs-18 semi_bold sora mb-0">Recent Notifications</p>

                                    <a href="{{url('notification')}}" class="view-all-btn ">View All<i
                                        class="fa-solid fa-chevron-right arrow"></i>
                                </a>
                                </div>
                                <div class="scrollbar px-2">
                                    <div class="row">
                                        @forelse($notifications ?? [] as $notification)
                                            <div class="col-lg-12">
                                                <div class="noti-content d-flex flex-row gap-5 align-items-center">
                                                    <div class="card-header profile-img">
                                                        <img src="{{asset('website') . '/' . ($notification->notification_image ?? $notification->user->profile->pic ?? 'no_avatar.jpg')}}"
                                                            class='rounded-pill' alt="notification">
                                                    </div>
                                                    <div class="card-body">
                                                        <div class="noti-heading pt-5 d-flex justify-content-between">
                                                            <p class="fs-15 semi_bold sora mb-2">{{ $notification->title ?? 'Notification' }}</p>
                                                            <p class="dark-cool-gray fs-12 normal">{{ $notification->created_at->diffForHumans() }}</p>
                                                        </div>
                                                        <div class="mail-massage">
                                                            <p class="fs-14 normal dark-cool-gray line-clamp-1">{{ $notification->message ?? 'No message' }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @empty
                                            <div class="col-lg-12">
                                                <div class="text-center py-4">
                                                    <p class="fs-14 normal sora light-black opacity-6">No recent notifications</p>
                                                </div>
                                            </div>
                                        @endforelse
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

@include('dashboard.templates.modal.add-service-details-modal')



@push('js')
    <script>
        var bookingChart;
        var ctx = document.getElementById('booking_chart');
        var fontFamily = KTUtil.getCssVariableValue('--bs-font-sans-serif');

        // Initial data from server
        var weeklyBookingData = @js($weeklyBookingData ?? ['labels' => [], 'upcoming' => [], 'completed' => []]);
        var monthlyBookingData = @js($monthlyBookingData ?? ['labels' => [], 'upcoming' => [], 'completed' => []]);

        function initBookingChart(data) {
            if (bookingChart) {
                bookingChart.destroy();
            }

            const chartData = {
                labels: data.labels,
                datasets: [
                    {
                        label: 'Upcoming Bookings',
                        data: data.upcoming,
                        borderColor: '#3B82F6',
                        backgroundColor: '#3B82F6',
                        fill: false,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        lineTension: 0
                    },
                    {
                        label: 'Completed Bookings',
                        data: data.completed,
                        borderColor: '#10B981',
                        backgroundColor: '#10B981',
                        fill: false,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        lineTension: 0
                    }
                ]
            };

            const config = {
                type: 'line',
                data: chartData,
                options: {
                    plugins: {
                        legend: {
                            display: true,
                            position: 'bottom',
                            align: 'start',
                            labels: {
                                usePointStyle: true,
                                pointStyle: 'circle',
                                padding: 20,
                                boxWidth: 8,
                                boxHeight: 8,
                            }
                        },
                        title: {
                            display: false,
                        },
                        tooltip: {
                            callbacks: {
                                title: function(context) {
                                    return context[0].label;
                                },
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y + ' bookings';
                                }
                            }
                        }
                    },
                    responsive: true,
                    interaction: {
                        intersect: false,
                    },
                    scales: {
                        x: {
                            grid: {
                                display: true,
                            },
                        },
                        y: {
                            grid: {
                                display: true,
                            },
                            min: 0,
                            ticks: {
                                display: true,
                                stepSize: 1,
                                callback: function (value) {
                                    return Math.floor(value) === value ? value : '';
                                }
                            }
                        }
                    }
                },
                defaults: {
                    global: {
                        defaultFont: fontFamily
                    }
                }
            };

            bookingChart = new Chart(ctx, config);
        }

        // Initialize chart with weekly data
        initBookingChart(weeklyBookingData);

        // Handle dropdown change
        document.getElementById('booking-period-dropdown').addEventListener('change', function() {
            const selectedPeriod = this.value;
            const periodDescription = document.getElementById('period-description');
            const totalBookingsDisplay = document.getElementById('total-bookings-display');

            if (selectedPeriod === 'week') {
                initBookingChart(weeklyBookingData);
                periodDescription.textContent = 'Last 7 days';
                totalBookingsDisplay.textContent = weeklyBookingData.total || 0;
            } else if (selectedPeriod === 'month') {
                initBookingChart(monthlyBookingData);
                periodDescription.textContent = 'This month';
                totalBookingsDisplay.textContent = monthlyBookingData.total || 0;
            }
        });
    </script>
@endpush
