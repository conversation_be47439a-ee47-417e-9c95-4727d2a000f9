<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\Booking;
use App\Models\BookingService;
use App\Models\Staff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\BookingsExport;
use App\Models\Category;
use App\Models\CouponUsage;
use App\Models\DiscountCoupon;
use App\Models\Friend;
use App\Models\User;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Refund;
use Stripe\Transfer;
use Stripe\Webhook;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    public function saveBookingDetails(Request $request)
    {
        $service = Service::where('ids', $request->service_id)->with('user')->firstOrFail();
        // If booking number is provided, update the existing booking in the database
        if ($request->booking_number != null) {
            $booking = Booking::where('booking_number', $request->booking_number)->firstOrFail();
            $booking->update([
                'user_id' => $request->user_id,
                'service_id' => $service->id,
                'booking_date' => $request->booking_date,
                'booking_time' => $request->booking_time,
                'comments' => $request->comments,
            ]);
            $provider = User::find($booking->provider_id);
            if ($booking->outlook_event_id && $provider && $provider->hasOutlookCalendarConnected()) {
                $outlookController = new OutlookCalendarController();
                $outlookController->updateOutlookEventForBooking($booking, $booking->outlook_event_id);
            }
            if ($booking->google_event_id && $provider && $provider->hasGoogleCalendarConnected()) {
                $googleController = new GoogleCalendarController();
                $googleController->updateGoogleEventForBooking($booking, $booking->google_event_id);
            }
            return response()->json([
                'status' => 'success',
                'booking' => $booking,
            ]);
        }
        if ($request->category == 'family') {
            $friend = Friend::where('ids', $request->user_id)->first();
            $user = $friend->user;
        } else {
            $user = User::find($request->user_id);
        }
        $user->profile()->update([
            'location' => $request->address,
            'lat' => $request->lat,
            'lng' => $request->lng,
        ]);
        // If no booking number is provided, store in session as before
        // Create a unique session-only identifier for this cart item (not persisted to DB)
        $cartItemId = (string) Str::uuid();

        $bookingCard = [
            'service' => $service,
            'booking_date' => $request->booking_date,
            'booking_time' => $request->booking_time,
            'comments' => $request->comments,
            'loc' => $request->loc,
            'loc_type' => $request->loc_type,
            'lat' => $request->lat,
            'lng' => $request->lng,
            'address' => $request->address,
            'user_id' => $request->user_id,
            'category' => $request->category,
            'selected_professionals' => $request->selected_professionals ?? [],
            // Session-only unique identifier for editing items in cart UI
            'cart_item_id' => $cartItemId,
        ];

        $bookingCards = session()->get('booking_cards', []);
        $currentUserId = auth()->id();
        $requestUserId = $request->user_id;
        $isBookingForSelf = ($requestUserId == $currentUserId);

        // Check for existing cart items with validation logic
        foreach ($bookingCards as $index => $card) {
            if (isset($card['service']) && $card['service']['ids'] == $request->service_id) {
                $existingUserId = $card['user_id'];
                $existingTime = $card['booking_time'];
                $existingDate = $card['booking_date'];
                $newTime = $request->booking_time;
                $newDate = $request->booking_date;

                // Only check conflicts for the SAME USER (not different users/friends)
                if ($existingUserId == $requestUserId) {

                    // Case 1: Exact same booking (same service, same user, same date, same time)
                    if ($existingDate == $newDate && $existingTime == $newTime) {
                        return response()->json([
                            'status' => 'already_exists',
                            'message' => 'Already added to your cart',
                            'show_cart_options' => true
                        ]);
                    }
                }

                // Case 3: Same service, same time slot, but different users (not allowed)
                if ($existingDate == $newDate && $existingTime == $newTime && $existingUserId != $requestUserId) {
                    return response()->json([
                        'status' => 'time_conflict',
                        'message' => 'This service is already booked for the same time slot. Please choose a different time.',
                        'existing_time' => $existingTime
                    ]);
                }

                // Case 4: Same service, different users, different time - ALLOW (add as new item)
                // This will continue the loop and eventually add as new item
            }
        }

        // If no existing item found, add new item to cart
        $bookingCards[] = $bookingCard;
        session(['booking_cards' => $bookingCards]);

        return response()->json([
            'status' => 'success',
            'message' => 'Service added to cart successfully',
            'booking_cards' => session('booking_cards'),
        ]);
    }

    public function updateCartBooking(Request $request)
    {
        $request->validate([
            'cart_item_id' => 'required|string',
            'service_id' => 'required|string',
            'booking_date' => 'required|date',
            'booking_time' => 'required|string',
        ]);

        $bookingCards = session()->get('booking_cards', []);
        // Duplicate guard: block updates that would duplicate an existing cart item
        $requestUserId = $request->user_id ?? auth()->id();
        foreach ($bookingCards as $card) {
            // Skip the current item being updated
            if (isset($card['cart_item_id']) && $card['cart_item_id'] === $request->cart_item_id) {
                continue;
            }
            $existingServiceIds = $card['service']['ids'] ?? null;
            $existingDate = $card['booking_date'] ?? null;
            $existingTime = $card['booking_time'] ?? null;
            $existingUserId = $card['user_id'] ?? auth()->id();

            if ($existingUserId == $requestUserId) {
                if (
                    $existingServiceIds === $request->service_id &&
                    $existingDate === $request->booking_date &&
                    $existingTime === $request->booking_time
                ) {
                    return response()->json([
                        'status' => 'already_exists',
                        'message' => 'Already added to your cart',
                        'show_cart_options' => true,
                    ]);
                }
            }
        }
        $updated = false;

        foreach ($bookingCards as $index => $card) {
            if (isset($card['cart_item_id']) && $card['cart_item_id'] === $request->cart_item_id) {
                $bookingCards[$index]['booking_date'] = $request->booking_date;
                $bookingCards[$index]['booking_time'] = $request->booking_time;
                $bookingCards[$index]['comments'] = $request->comments;
                $bookingCards[$index]['loc'] = $request->loc;
                $bookingCards[$index]['loc_type'] = $request->loc_type;
                $bookingCards[$index]['lat'] = $request->lat;
                $bookingCards[$index]['lng'] = $request->lng;
                $bookingCards[$index]['address'] = $request->address;
                $bookingCards[$index]['category'] = $request->category;
                if ($request->has('selected_professionals')) {
                    $bookingCards[$index]['selected_professionals'] = $request->selected_professionals ?? [];
                }
                $updated = true;
                break;
            }
        }

        if (!$updated) {
            return response()->json([
                'status' => 'error',
                'message' => 'Cart item not found',
            ], 404);
        }

        session(['booking_cards' => $bookingCards]);

        return response()->json([
            'status' => 'success',
            'message' => 'Cart item updated successfully',
            'booking_cards' => $bookingCards,
        ]);
    }
    public function saveProfessionalBookingDetails(Request $request)
    {
        try {
            DB::beginTransaction();

            // Validate the request
            $request->validate([
                'clientName' => 'required|string|max:255',
                'clientEmail' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'service_id' => 'required|string',
                'booking_date' => 'required|date',
                'booking_time' => 'required|string',
            ]);

            $service = Service::where('ids', $request->service_id)->with('user')->firstOrFail();

            // Create new booking
            $booking = new Booking();
            $booking->booking_number = 'BK-' . mt_rand(10000000, 99999999);
            $booking->service_id = $service->id;
            // $booking->user_id = auth()->id(); // Current authenticated user as customer
            $booking->provider_id = $service->user->id; // Service provider
            $booking->service_price = $service->price; // Service provider
            $booking->total_amount = $service->price; // Service provider
            $booking->booking_date = $request->booking_date;
            $booking->booking_time = $request->booking_time;
            $booking->duration = (int) $service->duration;;
            $booking->comments = $request->comments ?? null;
            $booking->client_name = $request->clientName;
            $booking->client_email = $request->clientEmail;
            $booking->client_phone_number = $request->phone;
            $booking->save();

            // Store selected professionals in booking_service pivot table if provided
            if ($request->has('selected_professionals') && !empty($request->selected_professionals)) {
                foreach ($request->selected_professionals as $selectedProfessional) {
                    // Get the staff member details
                    $staff = Staff::find($selectedProfessional['id']);

                    if ($staff) {
                        BookingService::create([
                            'booking_id' => $booking->id,
                            'service_id' => $service->id,
                            'staff_id' => $staff->id,
                            'user_id' => $staff->user_id, // Professional's user account if linked
                        ]);
                    }
                }
            }

            // Send notifications to admin and provider only
            $provider = User::find($booking->provider_id);
            $customer = auth()->user();

            // Notification to Professional/Provider
            if ($provider) {
                $this->user_notification(
                    $provider->id,
                    'New Booking Received',
                    "You have received a new booking for {$service->name} on {$request->booking_date} at {$request->booking_time}. Booking ID: {$booking->booking_number}",
                    $booking->id,
                    'booking'
                );
            }

            // Notification to Admin
            $this->notifyAdmins(
                'New Booking Created',
                "A new booking has been created by {$customer->name} for {$service->name} with {$provider->name}. Booking ID: {$booking->booking_number}",
                auth()->user()->id,
                'booking'
            );

            // Create calendar events if calendars are connected
            if ($provider && $provider->hasOutlookCalendarConnected()) {
                $outlookController = new OutlookCalendarController();
                $outlookEventId = $outlookController->createOutlookEventForBooking($booking);
                if ($outlookEventId) {
                    $booking->outlook_event_id = $outlookEventId;
                    $booking->save();
                }
            }
            if ($provider && $provider->hasGoogleCalendarConnected()) {
                $googleController = new GoogleCalendarController();
                $googleEventId = $googleController->createGoogleEventForBooking($booking);
                if ($googleEventId) {
                    $booking->google_event_id = $googleEventId;
                    $booking->save();
                }
            }
            DB::commit();
            return response()->json([
                'status' => 'success',
                'message' => 'Booking created successfully',
                'booking' => $booking,
            ]);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'status' => 'error',
                'message' => 'Error saving booking details: ' . $e->getMessage()
            ], 500);
        }
    }

    public function checkout(Request $request)
    {
        Stripe::setApiKey(env('STRIPE_SECRET'));
        $session = Session::create([
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => 'usd',
                    'product_data' => [
                        'name' => 'Test Product',
                    ],
                    'unit_amount' => $request->total * 100, // $10.00
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => route('success'),
            'cancel_url' => route('cancel'),
        ]);
        session()->put([
            'session_id' => $session->id,
            'coupon_code' => $request->coupon_code,
            'discount_amount' => $request->discount_amount,
            'original_total' => $request->original_total,
        ]);
        return redirect($session->url);
    }
    public function paymentSuccess(Request $request)
    {
        // try {
        DB::beginTransaction();
        $bookingCards = session()->get('booking_cards');
        $session_id = session()->get('session_id');
        $couponCode = session()->get('coupon_code');
        $coupon = DiscountCoupon::where('coupon_code', $couponCode)->first();
        $discountAmount = session()->get('discount_amount');
        if (!$bookingCards || !is_array($bookingCards)) {
            return response()->json(['message' => 'No booking data found in session.'], 400);
        }
        $bookingIds = [];
        foreach ($bookingCards as $card) {
            $service = $card['service'];
            $user = $service['user'];
            $booking = new Booking();
            $booking->booking_number = 'BK-' . mt_rand(10000000, 99999999);
            $booking->stripe_session_id = $session_id;
            $booking->service_id = $service['id'];
            if ($card['category'] == 'family') {
                $friend = Friend::where('ids', $card['user_id'])->first();
                $user_id = $friend->id;
            } else {
                $user_id = auth()->id();
            }
            $servicePrice = (float) $service['price'];
            $totalAmount = $servicePrice;

            if ($coupon) {
                $booking->discount_coupon_id = $coupon->id;
                $booking->discount_amount = $discountAmount;
                // Calculate total amount after discount
                $totalAmount = $servicePrice - $discountAmount;
            }

            $booking->user_id = $user_id;
            $booking->provider_id = $user['id'];
            $booking->booking_date = $card['booking_date'];
            $booking->booking_time = $card['booking_time'];
            $booking->duration = (int) $service['duration'];
            $booking->vat_amount = null;
            $booking->service_price = $servicePrice;
            $booking->total_amount = $totalAmount;
            $booking->comments = $card['comments'] ?? null;
            $booking->save();
            $bookingIds[] = $booking->id;
            $provider = User::find($booking->provider_id);
            if ($provider && $provider->hasOutlookCalendarConnected()) {
                $outlookController = new OutlookCalendarController();
                $outlookEventId = $outlookController->createOutlookEventForBooking($booking);
                if ($outlookEventId) {
                    $booking->outlook_event_id = $outlookEventId;
                    $booking->save();
                }
            }
            if ($provider && $provider->hasGoogleCalendarConnected()) {
                $googleController = new GoogleCalendarController();
                $googleEventId = $googleController->createGoogleEventForBooking($booking);
                if ($googleEventId) {
                    $booking->google_event_id = $googleEventId;
                    $booking->save();
                }
            }
            if (isset($card['selected_professionals']) && !empty($card['selected_professionals'])) {
                foreach ($card['selected_professionals'] as $selectedProfessional) {
                    $staff = Staff::find($selectedProfessional['id']);
                    if ($staff) {
                        BookingService::create([
                            'booking_id' => $booking->id,
                            'service_id' => $service['id'],
                            'staff_id' => $staff->id,
                            'user_id' => $staff->user_id,
                        ]);
                    }
                }
            }
            if ($provider) {
                $this->user_notification(
                    $provider->id,
                    'New Booking Received',
                    "You have received a new booking for {$service['name']} on {$card['booking_date']} at {$card['booking_time']}. Booking ID: {$booking->booking_number}",
                    auth()->user()->id,
                    'booking'
                );
            }

            // Notification to Customer
            $this->user_notification(
                $user_id,
                'Booking Confirmed',
                "Your booking for {$service['name']} has been confirmed for {$card['booking_date']} at {$card['booking_time']}. Booking ID: {$booking->booking_number}",
                $user_id,
                'booking'
            );

            // Notification to Admin
            $user = auth()->user();
            $this->notifyAdmins(
                'New Booking Created',
                "A new booking has been created by {$user->name} for {$service['name']} with {$provider->name}. Booking ID: {$booking->booking_number}",
                $user_id,
                'booking'
            );
        }
        if ($coupon) {
            $coupon->user_limit -= 1;
            $coupon->save();
        }
        CouponUsage::create([
            'user_id' => auth()->id(),
            'discount_coupon_id' => $coupon->id,
            'amount' => $discountAmount,
            'booking_id' => json_encode($bookingIds)
        ]);

        session()->forget(['booking_cards', 'coupon_code', 'discount_amount', 'original_total']);
        DB::commit();
        return redirect('/')->with([
            'type' => 'success',
            'message' => 'Payment done successfully'
        ]);
    }
    public function paymentCancel(Request $request)
    {
        return redirect()->route('cart')->with([
            'type' => 'Success',
            'message' => 'Payment cancelled'
        ]);
    }

    public function paymentRefund(Request $request)
    {
        $request->validate([
            'reason' => 'required|string|max:1000',
        ]);
        try {
            $booking = Booking::where('ids', $request->booking_id)->firstOrFail();
            $booking->cancel_reason = $request->reason;

            $user = auth()->user();
            $provider = User::find($booking->provider_id);
            $customer = User::find($booking->user_id);
            $service = $booking->service;

            if ($user && $user->hasAnyRole(['admin', 'super admin'])) {
                // ---- Admin: Do Refund ----
                Stripe::setApiKey(env('STRIPE_SECRET'));

                if ($booking->stripe_session_id) {
                    $session = Session::retrieve($booking->stripe_session_id);

                    if ($session && $session->payment_status === 'paid') {
                        Refund::create([
                            'payment_intent' => $session->payment_intent,
                        ]);

                        // Delete events if exist
                        if ($booking->outlook_event_id && $provider && $provider->hasOutlookCalendarConnected()) {
                            (new OutlookCalendarController())->deleteOutlookEventForBooking($booking->provider_id, $booking->outlook_event_id);
                        }
                        if ($booking->google_event_id && $provider && $provider->hasGoogleCalendarConnected()) {
                            (new GoogleCalendarController())->deleteGoogleEventForBooking($booking->provider_id, $booking->google_event_id);
                        }

                        $booking->status = 3; // Refunded
                    }
                }
                $booking->save();
                // Notifications → to customer & provider
                if ($provider && $customer && $service) {
                    $this->user_notification(
                        $provider->id,
                        'Booking Refunded',
                        "A booking for {$service->name} has been refunded. Booking ID: {$booking->booking_number}. Reason: {$request->reason}",
                        $booking->id,
                        'refund'
                    );

                    $this->user_notification(
                        $customer->id,
                        'Refund Processed',
                        "Your refund for {$service->name} has been processed successfully. Booking ID: {$booking->booking_number}.",
                        $booking->id,
                        'refund'
                    );
                }
            } elseif ($user && $user->hasRole('customer')) {
                // ---- Customer: Cancel ----
                $booking->status = 2; // Cancelled
                $booking->save();

                if ($provider && $service) {
                    $this->user_notification(
                        $provider->id,
                        'Booking Cancelled',
                        "A booking for {$service->name} has been cancelled by the customer. Booking ID: {$booking->booking_number}. Reason: {$request->reason}",
                        $booking->id,
                        'cancel'
                    );
                }

                $this->notifyAdmins(
                    'Booking Cancelled',
                    "Booking {$booking->booking_number} has been cancelled by the customer. Reason: {$request->reason}",
                    $booking->id,
                    'cancel'
                );
            } elseif ($user && $user->hasAnyRole(['individual', 'business', 'professional'])) {
                // ---- Business/Individual/Professional: Cancel ----
                $booking->status = 2; // Cancelled
                $booking->save();

                if ($customer && $service) {
                    $this->user_notification(
                        $customer->id,
                        'Booking Cancelled',
                        "Your booking for {$service->name} has been cancelled by the provider. Booking ID: {$booking->booking_number}. Reason: {$request->reason}",
                        $booking->id,
                        'cancel'
                    );
                }

                $this->notifyAdmins(
                    'Booking Cancelled',
                    "Booking {$booking->booking_number} has been cancelled by the provider. Reason: {$request->reason}",
                    $booking->id,
                    'cancel'
                );
            }

            $msgStatus = $booking->status == 3 ? 'refunded' : 'cancelled';
            return redirect('/')->with([
                'type' => 'success',
                'message' => "Booking has been {$msgStatus}."
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => 'Payment action failed: ' . $e->getMessage()
            ]);
        }
    }

    public function customerBooking()
    {
        // $timezone = 'Asia/Karachi';   // ✅ set timezone in one place
        $now = Carbon::now();

        // Get all bookings for the user
        $allBookings = Booking::with("service")
            ->where('user_id', auth()->id())
            ->get();

        // Upcoming bookings
        $upcomingBookings = $allBookings->filter(function ($booking) use ($now) {
            $bookingDateTime = Carbon::parse(
                $booking->booking_date . ' ' . $booking->booking_time
            );
            return $bookingDateTime->gt($now);
        })->sortBy(function ($booking) {
            return $booking->booking_date . ' ' . $booking->booking_time;
        });

        // Past bookings
        $pastBookings = $allBookings->filter(function ($booking) use ($now) {
            $bookingDateTime = Carbon::parse(
                $booking->booking_date . ' ' . $booking->booking_time
            );
            return $bookingDateTime->lte($now);
        })->sortByDesc(function ($booking) {
            return $booking->booking_date . ' ' . $booking->booking_time;
        });

        return view('dashboard.customer.customer-booking', compact('upcomingBookings', 'pastBookings'));
    }

    /**
     * Filter customer bookings for AJAX requests
     * Note: Filters only apply to past bookings, upcoming bookings remain unfiltered
     */
    public function filterCustomerBookings(Request $request)
    {
        $now = Carbon::now();

        // Get original upcoming bookings (unfiltered - filters don't apply to upcoming bookings)
        $upcomingBookings = Booking::with(['service', 'provider'])
            ->where('user_id', auth()->id())
            ->get()
            ->filter(function ($booking) use ($now) {
                $bookingDateTime = Carbon::parse($booking->booking_date . ' ' . $booking->booking_time);
                return $bookingDateTime->gt($now);
            })
            ->sortBy(function ($booking) {
                return $booking->booking_date . ' ' . $booking->booking_time;
            });

        // Base query for past bookings only (with filters applied)
        $pastQuery = Booking::with(['service', 'provider'])
            ->where('user_id', auth()->id());

        // Apply search filter to past bookings only
        if ($request->filled('search')) {
            $searchTerm = $request->search;
            $pastQuery->where(function ($q) use ($searchTerm) {
                $q->where('booking_number', 'like', "%{$searchTerm}%")
                    ->orWhereHas('service', function ($serviceQuery) use ($searchTerm) {
                        $serviceQuery->where('name', 'like', "%{$searchTerm}%");
                    })
                    ->orWhereHas('provider', function ($providerQuery) use ($searchTerm) {
                        $providerQuery->where('name', 'like', "%{$searchTerm}%");
                    })
                    ->orWhere('total_amount', 'like', "%{$searchTerm}%");
            });
        }

        // Apply status filter to past bookings only
        if ($request->filled('status') && $request->status !== 'all') {
            if ($request->status === 'completed') {
                $pastQuery->where('status', 1);
            } elseif ($request->status === 'cancelled') {
                $pastQuery->where('status', 2);
            } elseif ($request->status === 'ongoing') {
                $pastQuery->where('status', 0);
            }
        }

        // Apply date range filter to past bookings only
        if ($request->filled('date_range')) {
            $dateRange = $request->date_range;

            // Check if it's a range (contains ' to ')
            if (strpos($dateRange, ' to ') !== false) {
                $dates = explode(' to ', $dateRange);
                if (count($dates) === 2) {
                    $startDate = Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                    $endDate = Carbon::createFromFormat('M d, Y', trim($dates[1]))->format('Y-m-d');
                    $pastQuery->whereBetween('booking_date', [$startDate, $endDate]);
                }
            } else {
                // Single date selected
                try {
                    $singleDate = Carbon::createFromFormat('M d, Y', trim($dateRange))->format('Y-m-d');
                    $pastQuery->whereDate('booking_date', $singleDate);
                } catch (\Exception $e) {
                    // If date parsing fails, ignore the filter
                    \Illuminate\Support\Facades\Log::warning('Failed to parse date filter: ' . $dateRange);
                }
            }
        }

        // Get filtered past bookings
        $pastBookings = $pastQuery->get()
            ->filter(function ($booking) use ($now) {
                $bookingDateTime = Carbon::parse($booking->booking_date . ' ' . $booking->booking_time);
                return $bookingDateTime->lte($now);
            })
            ->sortByDesc(function ($booking) {
                return $booking->booking_date . ' ' . $booking->booking_time;
            });

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            // Only return past bookings HTML since filters don't apply to upcoming bookings
            $pastHtml = view('dashboard.customer.partials.past-bookings-table', compact('pastBookings'))->render();

            return response()->json([
                'success' => true,
                'past_html' => $pastHtml,
                'past_count' => $pastBookings->count()
            ]);
        }

        return view('dashboard.customer.customer-booking', compact('upcomingBookings', 'pastBookings'));
    }

    public function getCustomerBookingsForCalendar()
    {
        try {
            // Only select columns that exist in the database based on the migration
            $bookings = Booking::select([
                'id', 'booking_number', 'service_id', 'user_id', 'provider_id',
                'booking_date', 'booking_time', 'duration', 'vat_amount',
                'comments', 'outlook_event_id', 'status'
            ])
            ->with(['service:id,name,user_id,price', 'service.user:id,name'])
            ->where('user_id', auth()->id())
            ->whereNotNull('service_id')
            ->whereNotNull('booking_date')
            ->whereNotNull('booking_time')
            ->get()
            ->filter(function ($booking) {
                // Additional filter to ensure service relationship exists
                return $booking->service !== null;
            })
            ->map(function ($booking) {
                $duration = $booking->duration ?? 60;
                $startDateTime = $booking->booking_date . 'T' . $booking->booking_time;
                $endDateTime = date('Y-m-d\TH:i:s', strtotime($booking->booking_date . ' ' . $booking->booking_time . ' +' . $duration . ' minutes'));

                return [
                    'id' => $booking->id,
                    'title' => $booking->service->name ?? 'Service',
                    'start' => $startDateTime,
                    'end' => $endDateTime,
                    'backgroundColor' => '#006AA0',
                    'borderColor' => '#006AA0',
                    'textColor' => '#ffffff',
                    'allDay' => false,
                    'extendedProps' => [
                        'booking_number' => $booking->booking_number ?? '',
                        'service_name' => $booking->service->name ?? 'Service',
                        'service_price' => $booking->service->price ?? 0,
                        'provider_name' => optional($booking->service->user)->name ?? 'N/A',
                        'booking_time' => date('h:i A', strtotime($booking->booking_time)),
                        'duration' => $duration,
                        'comments' => $booking->comments ?? '',
                        'status' => $booking->status ?? 0
                    ]
                ];
            });

            return response()->json($bookings->values());
        } catch (\Exception $e) {
            Log::error('Customer calendar data error: ' . $e->getMessage(), [
                'user_id' => auth()->id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to load calendar data',
                'message' => 'Unable to retrieve booking information'
            ], 500);
        }
    }
    public function dashboardBooking()
    {
        $bookings = Booking::query();
        $bookings->with(['service', 'service.user', 'customer']);

        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $bookings->where('provider_id', auth()->id());
        }
        $bookings = $bookings->orderBy('created_at', 'desc')->get();
        // Calculate booking statistics
        $stats = $this->calculateBookingStats();
        $categories = Category::where('status', '1')->get();
        return view('dashboard.business.business-booking', compact('bookings', 'stats', 'categories'));
    }

    public function filterBookings(Request $request)
    {
        $query = Booking::query();
        $query->with(['service', 'service.user', 'customer']);

        // Apply role-based filtering
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $query->where('provider_id', auth()->id());
        }

        // Apply search filter
        if ($request->has('search')) {
            $search = $request->get('search');
            if ($search !== '') {
                $query->where(function ($q) use ($search) {
                    $q->where('booking_number', 'like', "%{$search}%")
                        ->orWhereHas('customer', function ($customerQuery) use ($search) {
                            $customerQuery->where('name', 'like', "%{$search}%");
                        })
                        ->orWhereHas('service', function ($serviceQuery) use ($search) {
                            $serviceQuery->where('name', 'like', "%{$search}%");
                        });
                });
            }
        }

        // Apply status filter
        if ($request->filled('status') && $request->get('status') !== 'all') {
            $status = $request->get('status');
            switch ($status) {
                case 'ongoing':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                    break;
                case 'upcoming':
                    $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                    break;
                case 'complete':
                    $query->where('status', 1);
                    break;
                case 'canceled':
                    $query->where('status', 2);
                    break;
            }
        }

        // Apply category filter
        if ($request->filled('category') && $request->get('category') !== 'all') {
            $category = $request->get('category');
            $query->whereHas('service', function ($serviceQuery) use ($category) {
                $serviceQuery->whereHas('category', function ($catQuery) use ($category) {
                    $catQuery->where('name', $category);
                });
            });
        }

        // Apply staff filter (for business users)
        if ($request->filled('staff') && $request->get('staff') !== 'all' && auth()->user()->hasRole('business')) {
            $staff = $request->get('staff');
            $query->whereHas('bookingServices', function ($bookingServiceQuery) use ($staff) {
                $bookingServiceQuery->whereHas('staff', function ($staffQuery) use ($staff) {
                    $staffQuery->where('name', 'like', "%{$staff}%");
                });
            });
        }

        // Apply date filter
        if ($request->filled('date')) {
            $dateRange = $request->get('date');
            $dates = explode(' - ', $dateRange);
            if (count($dates) == 2) {
                $startDate = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                $endDate = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[1]))->format('Y-m-d');
                $query->whereBetween('booking_date', [$startDate, $endDate]);
            } elseif (count($dates) == 1) {
                $date = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                $query->whereDate('booking_date', $date);
            }
        }

        $bookings = $query->orderBy('booking_date', 'desc')->get();

        // Return JSON response for AJAX requests
        if ($request->ajax()) {
            $html = view('dashboard.business.partials.booking-table-rows', compact('bookings'))->render();
            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $bookings->count()
            ]);
        }

        $stats = $this->calculateBookingStats();
        return view('dashboard.business.business-booking', compact('bookings', 'stats'));
    }

    private function calculateBookingStats()
    {
        $baseQuery = Booking::query();

        // Apply role-based filtering for stats
        if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
            $baseQuery->where('provider_id', auth()->id());
        }

        // Total bookings
        $totalBookings = (clone $baseQuery)->count();

        // Completed bookings (status 1)
        $completedBookings = (clone $baseQuery)->where('status', 1)->count();

        // Ongoing bookings (status 0 - pending bookings where time has passed)
        $ongoingBookings = (clone $baseQuery)
            ->where('status', 0)
            ->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()])
            ->count();

        // Upcoming bookings (status 0 - pending bookings where time has not passed)
        $upcomingBookings = (clone $baseQuery)
            ->where('status', 0)
            ->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()])
            ->count();

        // Calculate percentage changes (mock data for now - you can implement actual historical comparison)
        return [
            'total_bookings' => $totalBookings,
            'total_bookings_change' => 17.2, // Mock percentage
            'active_bookings' => $completedBookings, // This is now "Completed Bookings" as per your UI change
            'active_bookings_change' => 17.2, // Mock percentage
            'ongoing_bookings' => $ongoingBookings,
            'ongoing_bookings_change' => 8.5, // Mock percentage
            'upcoming_bookings' => $upcomingBookings,
            'upcoming_bookings_change' => -2.3, // Mock percentage
        ];
    }

    public function updateBookingStatus(Request $request)
    {
        try {
            $request->validate([
                'booking_id' => 'required|exists:bookings,id',
                'action' => 'required|in:complete,cancel'
            ]);
            $booking = Booking::findOrFail($request->booking_id);

            if (auth()->user()->hasAnyRole(['individual', 'professional', 'business']) && $booking->provider_id !== auth()->id()) {
                return response()->json(['success' => false, 'message' => 'Unauthorized'], 403);
            }

            if ($request->action === 'cancel' && $booking->stripe_session_id) {
                try {
                    Stripe::setApiKey(env('STRIPE_SECRET'));
                    $session = Session::retrieve($booking->stripe_session_id);

                    if ($session->payment_status === 'paid') {
                        if (!$session->payment_intent) {
                            return response()->json([
                                'success' => false,
                                'showModal' => true,
                                'message' => 'No payment intent found. Please contact support.'
                            ]);
                        }
                        Refund::create(['payment_intent' => $session->payment_intent]);
                    }
                } catch (\Exception $e) {
                    Log::error('Stripe refund error: ' . $e->getMessage());
                    return response()->json([
                        'success' => false,
                        'showModal' => true,
                        'message' => 'Error processing refund: ' . $e->getMessage()
                    ]);
                }
                $booking->status = 2; // Cancelled

                // Delete calendar events if they exist and calendars are connected
                $provider = User::find($booking->provider_id);
                if ($booking->outlook_event_id && $provider && $provider->hasOutlookCalendarConnected()) {
                    $outlookController = new OutlookCalendarController();
                    $outlookController->deleteOutlookEventForBooking($booking->provider_id, $booking->outlook_event_id);
                }
                if ($booking->google_event_id && $provider && $provider->hasGoogleCalendarConnected()) {
                    $googleController = new GoogleCalendarController();
                    $googleController->deleteGoogleEventForBooking($booking->provider_id, $booking->google_event_id);
                }

                // Send cancellation notifications
                $customer = User::find($booking->user_id);
                $service = $booking->service;

                if ($provider && $customer && $service) {
                    // Notification to Customer
                    $this->user_notification(
                        $customer->id,
                        'Booking Cancelled',
                        "Your booking for {$service->name} on {$booking->booking_date} at {$booking->booking_time} has been cancelled by the provider. Booking ID: {$booking->booking_number}. A refund will be processed if applicable.",
                        'cancellation',
                        $booking->id
                    );

                    // Notification to Admin
                    $this->notifyAdmins(
                        'Booking Cancelled by Provider',
                        "Booking {$booking->booking_number} has been cancelled by provider {$provider->name}. Customer: {$customer->name}, Service: {$service->name}",
                        $booking->id,
                        'cancellation'
                    );
                }
            } elseif ($request->action === 'complete') {
                if (auth()->user()->stripe_account_id) {
                    $booking->status = 1; // Completed
                    Stripe::setApiKey(env('STRIPE_SECRET'));
                    $commissionRate = 10;
                    $commission = ($booking->total_amount * $commissionRate) / 100;
                    $transfer = Transfer::create([
                        'amount' => ($booking->total_amount - $commission) * 100, // Convert to cents
                        'currency' => 'usd',
                        'destination' => auth()->user()->stripe_account_id,
                    ]);

                    // Send completion notifications
                    $provider = User::find($booking->provider_id);
                    $customer = User::find($booking->user_id);
                    $service = $booking->service;

                    if ($provider && $customer && $service) {
                        // Notification to Customer
                        $this->user_notification(
                            $customer->id,
                            'Service Completed',
                            "Your booking for {$service->name} has been marked as completed. Booking ID: {$booking->booking_number}. Thank you for choosing our service!",
                            'completion',
                            $booking->id
                        );

                        // Notification to Admin
                        $this->notifyAdmins(
                            'Service Completed',
                            "Booking {$booking->booking_number} has been completed by provider {$provider->name}. Customer: {$customer->name}, Service: {$service->name}",
                            $booking->id,
                            'completion'
                        );
                    }

                    return response()->json([
                        'success' => true,
                        'message' => 'All eligible payments processed successfully.',
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'Please setup your Stripe Connect account first.',
                        'redirect' => route('stripe.connect')
                    ]);
                }
            }
            $booking->save();
            return response()->json([
                'success' => true,
                'message' => 'Booking status updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Booking status update error: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error updating booking status: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getBusinessBookingsForCalendar()
    {
        try {
            \Log::info('getBusinessBookingsForCalendar method called', [
                'request' => request()->all(),
                'user' => auth()->user() ? auth()->user()->id : 'not authenticated',
                'url' => request()->url(),
                'method' => request()->method()
            ]);

            $query = Booking::with(['service', 'customer']);

            // Apply role-based filtering
            if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
                $query->where('provider_id', auth()->id());
            }

            $allBookings = $query->get();
            \Log::info('Raw bookings query result', [
                'count' => $allBookings->count(),
                'user_id' => auth()->id(),
                'user_roles' => auth()->user()->getRoleNames(),
                'sql' => $query->toSql(),
                'bindings' => $query->getBindings()
            ]);

            if ($allBookings->isEmpty()) {
                \Log::warning('No bookings found for user', [
                    'user_id' => auth()->id(),
                    'user_roles' => auth()->user()->getRoleNames()
                ]);
                return response()->json([]);
            }

            $bookings = $allBookings->map(function ($booking) {
                try {
                    \Log::info('Processing booking', [
                        'booking_id' => $booking->id,
                        'service_id' => $booking->service_id,
                        'customer_id' => $booking->customer_id,
                        'has_service' => $booking->service ? 'yes' : 'no',
                        'has_customer' => $booking->customer ? 'yes' : 'no'
                    ]);

                    $backgroundColor = '#006AA0'; // Default blue
                    $textColor = '#ffffff';

            // Set colors based on status and date
            switch ($booking->status) {
                case 0: // Pending/Active
                    if ($booking->hasTimePassed()) {
                        $backgroundColor = '#F59E0B'; // Orange for ongoing/past pending
                        $statusText = 'Ongoing';
                    } else {
                        $backgroundColor = '#3B82F6'; // Blue for upcoming
                        $statusText = 'Upcoming';
                    }
                    break;
                case 1: // Completed
                    $backgroundColor = '#10B981'; // Green
                    $statusText = 'Completed';
                    break;
                case 2: // Cancelled
                    $backgroundColor = '#EF4444'; // Red
                    $statusText = 'Cancelled';
                    break;
                default:
                    $statusText = 'Unknown';
            }

            // Create unique event ID to avoid duplication
            $eventId = 'booking_' . $booking->id;

            return [
                'id' => $eventId,
                'title' => $booking->service->name . ' - ' . ($booking->customer->name ?? 'N/A'),
                'start' => $booking->booking_date . 'T' . $booking->booking_time,
                'end' => date('Y-m-d\TH:i:s', strtotime($booking->booking_date . ' ' . $booking->booking_time . ' +' . ($booking->duration ?? 60) . ' minutes')),
                'backgroundColor' => $backgroundColor,
                'borderColor' => $backgroundColor,
                'textColor' => $textColor,
                'allDay' => false,
                'extendedProps' => [
                    'booking_id' => $booking->id,
                    'booking_number' => $booking->booking_number,
                    'service_name' => $booking->service->name,
                    'client_name' => $booking->client_name,
                    'customer_name' => $booking->customer->name ?? 'N/A',
                    'customer' => $booking->customer,
                    'service_price' => $booking->total_amount ?? $booking->total_amount ?? 0,
                    'booking_date' => $booking->booking_date,
                    'booking_time' => date('h:i A', strtotime($booking->booking_time)),
                    'duration' => $booking->duration ?? 60,
                    'comments' => $booking->comments,
                    'status' => $booking->status,
                    'status_text' => $statusText
                ]
            ];
                } catch (\Exception $e) {
                    \Log::error('Error processing booking ' . $booking->id . ': ' . $e->getMessage());
                    return null; // Skip this booking if there's an error
                }
            })->filter(); // Remove null values

            \Log::info('Calendar data prepared', ['count' => $bookings->count()]);

            return response()->json($bookings->values()); // Use values() to ensure clean array indices
        } catch (\Exception $e) {
            \Log::error('Calendar data error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to load calendar data'], 500);
        }
    }

    /**
     * Show booking detail page
     */
    public function showBookingDetail($ids, $booking_number)
    {
        try {
            $query = Booking::with([
                'service',
                'service.category',
                'service.user',
                'customer',
            ]);

            // Apply role-based filtering for security
            if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
                $query->where('provider_id', auth()->id())->where('booking_number', $booking_number)->where('ids', $ids);
            }

            $query->where('booking_number', $booking_number)->where('ids', $ids);
            $booking = $query->firstOrFail();
            return view('dashboard.business.booking-detail', compact('booking'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return view('dashboard.business.booking-detail', [
                'error' => 'Booking not found or you do not have permission to view this booking.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error showing booking details: ' . $e->getMessage());
            return view('dashboard.business.booking-detail', [
                'error' => 'An error occurred while loading booking details. Please try again.'
            ]);
        }
    }

    /**
     * Show customer booking detail page
     */
    public function showCustomerBookingDetail($ids, $booking_number)
    {
        try {
            $query = Booking::with([
                'service',
                'service.category',
                'service.user',
                'service.user.profile',
                'customer',
                'assignedProfessionals',
                'bookingServices.staff',
            ]);

            // Apply customer filtering for security - only show customer's own bookings
            $query->where('booking_number', $booking_number)
                ->where('ids', $ids);

            $booking = $query->firstOrFail();

            return view('dashboard.customer.booking-detail', compact('booking'));
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return view('dashboard.customer.booking-detail', [
                'error' => 'Booking not found or you do not have permission to view this booking.'
            ]);
        } catch (\Exception $e) {
            return view('dashboard.customer.booking-detail', [
                'error' => 'An error occurred while loading booking details. Please try again.'
            ]);
        }
    }

    /**
     * Export bookings to CSV
     */
    public function exportBookings(Request $request)
    {
        try {
            $query = Booking::query();
            $query->with(['service', 'service.category', 'customer']);

            // Apply role-based filtering (same as filter method)
            if (auth()->user()->hasAnyRole(['individual', 'professional', 'business'])) {
                $query->where('provider_id', auth()->id());
            }

            // Apply search filter (same as filter method)
            if ($request->has('search')) {
                $search = $request->get('search');
                if ($search !== '') {
                    $query->where(function ($q) use ($search) {
                        $q->where('booking_number', 'like', "%{$search}%")
                            ->orWhereHas('customer', function ($customerQuery) use ($search) {
                                $customerQuery->where('name', 'like', "%{$search}%");
                            })
                            ->orWhereHas('service', function ($serviceQuery) use ($search) {
                                $serviceQuery->where('name', 'like', "%{$search}%");
                            });
                    });
                }
            }

            // Apply status filter (same as filter method)
            if ($request->filled('status') && $request->get('status') !== 'all') {
                $status = $request->get('status');
                switch ($status) {
                    case 'ongoing':
                        $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) < ?", [now()]);
                        break;
                    case 'upcoming':
                        $query->where('status', 0)->whereRaw("CONCAT(booking_date, ' ', booking_time) >= ?", [now()]);
                        break;
                    case 'complete':
                        $query->where('status', 1);
                        break;
                    case 'canceled':
                        $query->where('status', 2);
                        break;
                }
            }

            // Apply category filter (same as filter method)
            if ($request->filled('category') && $request->get('category') !== 'all') {
                $category = $request->get('category');
                $query->whereHas('service', function ($serviceQuery) use ($category) {
                    $serviceQuery->whereHas('category', function ($catQuery) use ($category) {
                        $catQuery->where('name', $category);
                    });
                });
            }

            // Apply staff filter (same as filter method)
            if ($request->filled('staff') && $request->get('staff') !== 'all' && auth()->user()->hasRole('business')) {
                $staff = $request->get('staff');
                $query->whereHas('bookingServices', function ($bookingServiceQuery) use ($staff) {
                    $bookingServiceQuery->whereHas('staff', function ($staffQuery) use ($staff) {
                        $staffQuery->where('name', 'like', "%{$staff}%");
                    });
                });
            }

            // Apply date filter (same as filter method)
            if ($request->filled('date')) {
                $dateRange = $request->get('date');
                $dates = explode(' - ', $dateRange);
                if (count($dates) == 2) {
                    $startDate = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                    $endDate = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[1]))->format('Y-m-d');
                    $query->whereBetween('booking_date', [$startDate, $endDate]);
                } elseif (count($dates) == 1) {
                    $date = \Carbon\Carbon::createFromFormat('M d, Y', trim($dates[0]))->format('Y-m-d');
                    $query->whereDate('booking_date', $date);
                }
            }

            $bookings = $query->orderBy('created_at', 'desc')->get();

            // Generate filename with current date and filters
            $filename = 'bookings_' . date('Y-m-d') . '.csv';

            return Excel::download(new BookingsExport($bookings), $filename);
        } catch (\Exception $e) {
            Log::error('Booking export error: ' . $e->getMessage());
            return redirect()->back()->with([
                'title' => 'Error',
                'message' => 'Failed to export bookings. Please try again.',
                'type' => 'error'
            ]);
        }
    }
}
