<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\VatManagement;
use App\Http\Requests\VatManagementRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;

class VatManagementsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
         $this->middleware('permission:vatmanagements-list|vatmanagements-create|vatmanagements-edit|vatmanagements-delete', ['only' => ['index','store']]);
         $this->middleware('permission:vatmanagements-create', ['only' => ['create','store']]);
         $this->middleware('permission:vatmanagements-edit', ['only' => ['edit','update']]);
         $this->middleware('permission:vatmanagements-delete', ['only' => ['destroy']]);
         $this->middleware('permission:vatmanagements-list', ['only' => ['show']]);

    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $offset = $request->get('offset', 0);
        $limit = 10;

        // Base query
        $query = VatManagement::where('status', 1);
        // Apply search filter if search term is provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('vat', 'LIKE', "%{$search}%")
                  ->orWhere('country_name', 'LIKE', "%{$search}%");
            });
        }
        // Get total count for pagination
        $totalCount = $query->count();
        // Get records with offset and limit
        $vatmanagements = $query->offset($offset)->limit($limit)->get();
        // If this is an AJAX request
        if ($request->ajax()) {
            $html = view('dashboard.admin.vat-management.partials.vat-table', [
                'vatmanagements' => $vatmanagements
            ])->render();
            return response()->json([
                'success' => true,
                'html' => $html,
                'count' => $vatmanagements->count(),
                'total' => $totalCount,
                'offset' => $offset,
                'next_offset' => $offset + $vatmanagements->count(),
                'has_more' => ($offset + $limit) < $totalCount
            ]);
        }
        return view('dashboard.admin.vat-management.vat-mgmt', [
            'vatmanagements' => $vatmanagements,
            'totalCount' => $totalCount
        ]);
    }

    /**
     * Load more VAT management records via AJAX
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function loadMore(Request $request)
    {
        // Use has() and trim to properly handle "0" as valid search term
        $search = $request->has('search') ? trim($request->get('search')) : '';
        $offset = $request->get('offset', 0);
        $limit = 10;

        // Base query
        $query = VatManagement::where('status', 1);

        // Apply search filter if search term is provided (check !== '' to handle "0" as valid search)
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('vat', 'LIKE', "%{$search}%")
                  ->orWhere('country_name', 'LIKE', "%{$search}%");
            });
        }

        // Get total count for pagination
        $totalCount = $query->count();

        // Get records with offset and limit
        $vatmanagements = $query->offset($offset)->limit($limit)->get();

        $html = view('dashboard.admin.vat-management.partials.vat-table', [
            'vatmanagements' => $vatmanagements
        ])->render();

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $vatmanagements->count(),
            'total' => $totalCount,
            'offset' => $offset,
            'next_offset' => $offset + $vatmanagements->count(),
            'has_more' => ($offset + $limit) < $totalCount
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('vatmanagements.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  VatManagementRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(VatManagementRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'country_name' => 'required|unique:vat_managements,country_name',
            'vat' => 'required|integer',
        ],[
            'country_name.required' => 'The country name is required.',
            'country_name.unique' => 'You have already registered the VAT for this country.',
            'vat.required' => 'The VAT field is required.',
            'vat.integer' => 'The VAT must be an integer.',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $vatmanagementData = $validator->validated();
            VatManagement::create($vatmanagementData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Vat Management Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $vatmanagement = VatManagement::where('ids', $id)->firstOrFail($id);
        return view('vatmanagements.show',['vatmanagement'=>$vatmanagement]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit($id)
    {
        $vatmanagement = VatManagement::where('ids', $id)->first();
        if (!$vatmanagement) {
            return response()->json(['error' => 'VAT Management not found'], 404);
        }
        return response()->json([
            'id' => $vatmanagement->id,
            'vat' => $vatmanagement->vat,
            'country_name' => $vatmanagement->country_name,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  VatManagementRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(VatManagementRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'vat' => 'required|integer',
        ],[
            'vat.required' => 'The VAT field is required.',
            'vat.integer' => 'The VAT must be an integer.',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $vatmanagement = VatManagement::where('ids', $id)->first();
            if (!$vatmanagement) {
                return response()->json(["type" => "error", "message" => "VAT Management not found", "title" => "Error"]);
            }
            $vatmanagementData = $validator->validated();
            $vatmanagement->update($vatmanagementData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Updated", "message" => 'VAT Management updated successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $vatmanagement = VatManagement::where('ids', $id)->firstOrFail();
        $vatmanagement->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Vat Management deleted successfully'
        ]);
    }
}
