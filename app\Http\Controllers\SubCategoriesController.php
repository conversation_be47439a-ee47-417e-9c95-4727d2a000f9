<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Support\Str;
use App\Models\SubCategory;
use App\Http\Requests\SubCategoryRequest;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class SubCategoriesController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
        $this->middleware('permission:subcategories-list|subcategories-create|subcategories-edit|subcategories-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:subcategories-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:subcategories-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:subcategories-delete', ['only' => ['destroy']]);
        $this->middleware('permission:subcategories-list', ['only' => ['show']]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $subcategories = SubCategory::all();
        return view('subcategories.index', ['subcategories' => $subcategories]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('subcategories.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  SubCategoryRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(SubCategoryRequest $request)
    {
        $alreadyAdded = SubCategory::where('category_id', $request->category_id)->where('name', $request->name)->exists();
        if ($alreadyAdded) {
            return back()->with([
                'type' => 'warning',
                'message' => 'You have already added this sub category',
                'title' => 'Already added'
            ]);
        }
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'description' => ['required', 'regex:' . config('constant.description_regex')],
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'image_description' => ['required', 'regex:' . config('constant.description_regex')],
            'alt_tag' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'category_id' => 'required',
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'alt_tag.required' => 'Please enter alt tag',
            'alt_tag.regex' => $messagesConfig['input']['regex'],
            'alt_tag.max' => $messagesConfig['input']['max'],
            'image_description.required' => 'Please enter image description',
            'name.required' => 'Please enter sub category name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'description.required' => 'Please enter description',
            'category_id.required' => 'Please select category',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $subCategoryData = $request->all();
            if ($request->hasFile("avatar")) {
                $subCategoryData['image'] = $this->storeImage("subcategory-images", $request->file('avatar'));
            }
            $subCategoryData['slug'] = Str::slug($request->input('name'));
            SubCategory::create($subCategoryData);
            DB::commit();
            return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Sub-Category Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->back()->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View|\Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $subcategory = SubCategory::with('category')->where('ids', $id)->firstOrFail();
        $subcategory->category_name = $subcategory->category->name ?? '';
        return response()->json($subcategory);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $subcategory = SubCategory::with('category')->where('ids', $id)->firstOrFail();
        $subcategory->category_name = $subcategory->category->name ?? '';
        return response()->json($subcategory);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  SubCategoryRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(SubCategoryRequest $request, $id)
    {
        // Debug: Log the request data
        Log::info('Subcategory update request:', [
            'id' => $id,
            'data' => $request->all()
        ]);

        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'description' => ['required', 'regex:' . config('constant.description_regex')],
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:' . config('constant.image_size')],
            'image_description' => ['required', 'regex:' . config('constant.description_regex')],
            'alt_tag' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'category_id' => ['required', 'exists:categories,id'],
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'alt_tag.required' => 'Please enter alt tag',
            'alt_tag.regex' => $messagesConfig['input']['regex'],
            'alt_tag.max' => $messagesConfig['input']['max'],
            'image_description.required' => 'Please enter image description',
            'name.required' => 'Please enter sub category name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'description.required' => 'Please enter description',
            'category_id.required' => 'Please select category',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $subCategory = SubCategory::where('ids', $id)->firstOrFail();
            $subCategoryData = $validator->validated();
            if ($request->hasFile('avatar')) {
                $this->deleteImage($subCategory->image);
                $subCategoryData['image'] = $this->storeImage('subcategory-images', $request->file('avatar'));
            }
            if ($subCategory->name !== $request->input('name')) {
                $subCategoryData['slug'] = Str::slug($request->input('name'));
            }
            $subCategory->update($subCategoryData);
            DB::commit();

            // Debug: Log successful update
            Log::info('Subcategory updated successfully:', [
                'id' => $id,
                'updated_data' => $subCategoryData
            ]);

            return response()->json(["type" => "success", "title" => "Updated", "message" => 'Sub-Category Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $subCategory = SubCategory::where('ids', $id)->firstOrFail();
        $this->deleteImage($subCategory->image);
        $subCategory->delete();
        return redirect()->back()->with([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    }

    public function subUpdateStatus(Request $request)
    {
        $subcategory = SubCategory::findOrFail($request->subcategory_id);
        $subcategory->status = $request->status;
        $subcategory->save();
        return response()->json(['success' => true]);
    }
}
