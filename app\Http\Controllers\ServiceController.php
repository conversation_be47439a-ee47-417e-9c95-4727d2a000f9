<?php

namespace App\Http\Controllers;

use App\Http\Requests\ServiceRequest;
use App\Models\Category;
use App\Models\Service;
use App\Models\ServiceDuration;
use App\Models\Staff;
use App\Services\UserService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ServiceController extends Controller
{
    public $userService;
    function __construct()
    {
        $this->userService = new UserService();
    }

    function index(Request $request)
    {
        $activeTab = $request->get('tab', 'services');
        $isGroup = $activeTab === 'classes';
        $isAdmin = auth()->user()->hasAnyRole(['admin', 'super admin']);
        $userId = $isAdmin ? null : auth()->id();

        if ($isAdmin) {
            if ($isGroup) {
                $individual_services = collect();
                $group_services = $this->userService
                    ->getServices(type: 'group', withRelations: true)
                    ->sortByDesc('created_at')
                    ->values();
            } else {
                $individual_services = $this->userService
                    ->getServices(type: 'individual', withRelations: true)
                    ->sortByDesc('created_at')
                    ->values();
                $group_services = collect();
            }
        } else {
            if ($isGroup) {
                $individual_services = collect();
                $group_services = $this->userService
                    ->getServices(userId: $userId, type: 'group', withRelations: true)
                    ->sortByDesc('created_at')
                    ->values();
            } else {
                $individual_services = $this->userService
                    ->getServices(userId: $userId, type: 'individual', withRelations: true)
                    ->sortByDesc('created_at')
                    ->values();
                $group_services = collect();
            }
        }

        // Get categories for filter dropdown
        $categories = Category::active()->get();

        return view('dashboard.service.index', compact('individual_services', 'group_services', 'categories', 'activeTab'));
    }

    public function filter(Request $request)
    {
        $tab = $request->get('tab', 'services');
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $categoryId = $request->filled('category') && $request->get('category') !== 'all' ? $request->get('category') : '';

        // Determine service type based on tab
        $type = $tab === 'classes' ? 'group' : 'individual';

        // Build query
        $query = Service::with(['category', 'subcategory', 'staff', 'availabilities']);

        // Apply user filter if not admin
        if (!auth()->user()->hasAnyRole(['admin', 'super admin'])) {
            $query->where('user_id', auth()->id());
        }

        // Apply type filter
        $query->where('type', $type);

        // Apply search filter
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%")
                    ->orWhereHas('category', function ($categoryQuery) use ($search) {
                        $categoryQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Apply category filter
        if (!empty($categoryId)) {
            $query->where('category_id', $categoryId);
        }

        $services = $query->get();

        // Return appropriate view based on tab
        if ($tab === 'classes') {
            $html = view('dashboard.service.partials.classes-table', ['group_services' => $services])->render();
        } else {
            $html = view('dashboard.service.partials.services-table', ['individual_services' => $services])->render();
        }

        return response()->json([
            'success' => true,
            'html' => $html,
            'count' => $services->count()
        ]);
    }

    function create($type)
    {
        $user = auth()->user();
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }

        // Load categories based on user role
        if ($user->hasAnyRole(['admin', 'super admin'])) {
            // Admin and super admin can see all active categories
            $categories = Category::active()->get();
        } else {
            // Regular users see only their assigned categories
            $categories = $user->categories()->get();
        }

        $staffs = Staff::where('user_id', $user->id)->where('email', '!=', $user->email)->where('status', 1)->get();
        return view('dashboard.service.create', compact('type', 'categories', 'staffs'));
    }

    function store(ServiceRequest $request, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $alreadyExists = Service::where('name', $request->name)->where('user_id', auth()->id())->exists();
        if($alreadyExists){
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service Already exists', 'title' => 'Error']);
        }
        $service = $this->userService->createService(request_data: $request, type: $type);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service creation failed', 'title' => 'Error']);
        }

        // Check if there are staff availability conflicts
        if (is_array($service) && isset($service['error'])) {
            $conflictMessage = $service['error'] . "\n\nConflicts:\n";
            foreach ($service['conflicts'] as $conflict) {
                $conflictMessage .= "- {$conflict['staff_name']} on {$conflict['date']} at {$conflict['time']} conflicts with service '{$conflict['conflicting_service']}' at {$conflict['conflicting_time']}\n";
            }
            return redirect()->back()->with(['type' => 'error', 'message' => $conflictMessage, 'title' => 'Staff Availability Conflict']);
        }
        return redirect()->route('services.index')->with(['type' => 'success', 'message' => 'Service created successfully', 'title' => 'Created']);
    }

    function edit($ids, $type)
    {
        $user = auth()->user();
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $service = $this->userService->getSingleService(service_ids: $ids);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service not found', 'title' => 'Error']);
        }

        // Load categories based on user role
        if ($user->hasAnyRole(['admin', 'super admin'])) {
            // Admin and super admin can see all active categories
            $categories = Category::active()->get();
        } else {
            // Regular users see only their assigned categories
            $categories = $user->categories()->get();
        }

        $staffs = Staff::where('user_id', $user->id)->where('email', '!=', $user->email)->where('status', 1)->get();
        return view('dashboard.service.edit', compact('service', 'type', 'categories', 'staffs'));
    }

    function update(ServiceRequest $request, $ids, $type)
    {
        if (!in_array($type, ['individual', 'group'])) {
            return redirect()->route("services.index")->with(['type' => 'error', 'message' => 'Invalid Service Type', 'title' => 'Error']);
        }
        $alreadyExists = Service::where('name', $request->name)->where('user_id', auth()->id())->where('ids', '!=', $ids)->exists();
        if($alreadyExists){
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service Already exists', 'title' => 'Error']);
        }
        $service = $this->userService->updateService(request_data: $request, service_ids: $ids, type: $type);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service update failed', 'title' => 'Error']);
        }

        // Check if there are staff availability conflicts
        if (is_array($service) && isset($service['error'])) {
            $conflictMessage = $service['error'] . "\n\nConflicts:\n";
            foreach ($service['conflicts'] as $conflict) {
                $conflictMessage .= "- {$conflict['staff_name']} on {$conflict['date']} at {$conflict['time']} conflicts with service '{$conflict['conflicting_service']}' at {$conflict['conflicting_time']}\n";
            }
            return redirect()->back()->with(['type' => 'error', 'message' => $conflictMessage, 'title' => 'Staff Availability Conflict']);
        }
        return redirect()->route('services.index')->with(['type' => 'success', 'message' => 'Service updated successfully', 'title' => 'Updated']);
    }

    public function fetchSubCategories($categoryId)
    {
        $user = auth()->user();
        try {
            $category = Category::find($categoryId);
            if (!$category) {
                return response()->json(['success' => false, 'message' => 'Category not found']);
            }

            // Load subcategories based on user role
            if ($user->hasAnyRole(['admin', 'super admin'])) {
                // Admin and super admin can see all subcategories for the category
                $subcategories = $category->subcategories;
            } else {
                // Regular users see only their assigned subcategories
                $subcategories = $user->subcategories()->where('sub_categories.category_id', $categoryId)->get();
            }

            return response()->json(['success' => true, 'subcategories' => $subcategories]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Error fetching subcategories']);
        }
    }

    function destroy($ids)
    {
        $del_service = $this->userService->deleteService(service_ids: $ids);
        if (!$del_service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service deletion failed', 'title' => 'Error']);
        }
        return redirect()->back()->with(['type' => 'success', 'message' => 'Service deleted successfully', 'title' => 'Deleted']);
    }

    function show($ids)
    {
        $service = $this->userService->getSingleService(service_ids: $ids);
        if (!$service) {
            return redirect()->back()->with(['type' => 'error', 'message' => 'Service not found', 'title' => 'Error']);
        }
        return view('dashboard.service.show', compact('service'));
    }

    public function featured($id)
    {
        if (!auth()->user()->hasAnyRole(['admin', 'super admin'])) {
            return redirect()->back()->with(['title' => 'Error', 'message' => 'Unauthorized', 'type' => 'error']);
        }
        $service = Service::where('ids', $id)->firstOrFail();
        if ($service->is_featured == true) {
            $service->is_featured = false;
            $service->save();
            return redirect()->back()->with(['title' => 'Done', 'message' => 'Service unfeatured successfully', 'type' => 'success']);
        }
        $service->is_featured = true;
        $service->save();
        return redirect()->back()->with(['title' => 'Done', 'message' => 'Service marked as featured successfully', 'type' => 'success']);
    }

    public function updateStatus(Request $request)
    {
        // Validate the request
        $request->validate([
            'service_id' => 'required|integer|exists:services,id',
            'status' => 'required|in:0,1'
        ]);
        try {
            $service = Service::findOrFail($request->service_id);
            // Check if user has permission to update this service
            if (!auth()->user()->hasAnyRole(['admin', 'super admin']) && $service->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized to update this service'
                ], 403);
            }
            $service->status = $request->status;
            $service->save();
            return response()->json([
                'success' => true,
                'message' => 'Service status updated successfully',
                'status' => $service->status
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update service status'
            ], 500);
        }
    }
}
