@extends('dashboard.layout.master')


@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Customers</h6>
                        <p class="fs-14 sora light-black m-0"> Manage and keep track of all your customers from here.</p>
                    </div>

                </div>
                <div class="col-lg-12">
                    <div class="table-container">
                        <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                            <!-- search -->
                            <div class="search_box">
                                <label for="customSearchInput">
                                    <i class="fas fa-search"></i>
                                </label>
                                <input class="search_input search" type="text" id="customersSearchInput"
                                    placeholder="Search..." />
                            </div>
                            <!-- Select with dots -->
                            <div class="dropdown search_box select-box" id="customersStatusDropdown">
                                <button
                                    class="dropdown-toggle search_input status-dropdown-button d-flex align-items-center justify-content-start"
                                    type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <span><span class="dot"></span>
                                        All</span>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item customers-status-filter" href="#" data-label="All"
                                            data-color="" data-status="all"><span class="dot"></span>
                                            All</a></li>
                                    <li><a class="dropdown-item dropdown-status customers-status-filter" href="#"
                                            data-status="active" data-label="Active" data-color="#10B981"><span
                                                class="dot completed"></span>
                                            Active</a></li>
                                    <li><a class="dropdown-item dropdown-status customers-status-filter" href="#"
                                            data-status="inactive" data-label="Inactive" data-color="#EF4444"><span
                                                class="dot cancelled-dot"></span>
                                            Inactive</a></li>
                                </ul>
                            </div>
                            <!-- Date Picker -->
                            <label for="datePicker" class="date_picker">
                                <div class="date-picker-container">
                                    <i class="bi bi-calendar-event calender-icon"></i>
                                    <input type="text" name="customersDatePicker" id="customersDatePicker"
                                        class="datePicker ms-3 w-200px" placeholder="Select date range" readonly>
                                    <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                </div>
                            </label>

                            <!-- export btn -->
                            <div class="search_box d-block ms-auto">
                                <a href="javascript:void(0);" id="exportCustomersBtn" class="search_input fs-14 normal link-gray">
                                    <i class="bi bi-file-earmark-excel me-2"></i>Export Data <i
                                        class="ms-1 bi bi-file-arrow-down"></i>
                                </a>
                            </div>
                        </div>
                        <table id="responsiveTable" class="display w-100">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Email Address</th>
                                    <th>Category</th>
                                    <th>Bookings</th>
                                    <th>Action</th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                @include('dashboard.admin.customers.partials.customers-table', [
                                    'customers' => $customers,
                                ])
                            </tbody>
                        </table>
                        <div class="d-flex justify-content-center" id="customersPagination" style="{{ $customers->hasPages() ? '' : 'display: none;' }}">
                            @if($customers->hasPages())
                                {{ $customers->links('pagination::bootstrap-4') }}
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        // Function to parse different date filter formats
        function parseDateFilter(dateValue) {
            if (!dateValue || dateValue.trim() === '') return null;

            let today = new Date();
            let result = {};

            // Check for predefined options
            if (dateValue.toLowerCase().includes('today')) {
                result.start_date = formatDate(today);
                result.end_date = formatDate(today);
                result.type = 'single';
            } else if (dateValue.toLowerCase().includes('yesterday')) {
                let yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                result.start_date = formatDate(yesterday);
                result.end_date = formatDate(yesterday);
                result.type = 'single';
            } else if (dateValue.toLowerCase().includes('last week')) {
                let lastWeekStart = new Date(today);
                lastWeekStart.setDate(today.getDate() - 7);
                result.start_date = formatDate(lastWeekStart);
                result.end_date = formatDate(today);
                result.type = 'range';
            } else if (dateValue.toLowerCase().includes('last month')) {
                let lastMonthStart = new Date(today);
                lastMonthStart.setMonth(today.getMonth() - 1);
                result.start_date = formatDate(lastMonthStart);
                result.end_date = formatDate(today);
                result.type = 'range';
            } else if (dateValue.includes(' - ')) {
                // Custom date range format: "MMM D, YYYY - MMM D, YYYY"
                let dates = dateValue.split(' - ');
                if (dates.length === 2) {
                    // Convert from "MMM D, YYYY" format to a more standard format
                    try {
                        let startDate = new Date(dates[0].trim());
                        let endDate = new Date(dates[1].trim());
                        result.start_date = formatDate(startDate);
                        result.end_date = formatDate(endDate);
                        result.type = 'range';
                    } catch (e) {
                        console.error('Error parsing date range:', e);
                        return null;
                    }
                }
            } else {
                // Single date
                try {
                    let singleDate = new Date(dateValue);
                    result.start_date = formatDate(singleDate);
                    result.end_date = formatDate(singleDate);
                    result.type = 'single';
                } catch (e) {
                    console.error('Error parsing single date:', e);
                    return null;
                }
            }

            return result;
        }

        // Helper function to format date as YYYY-MM-DD
        function formatDate(date) {
            return date.getFullYear() + '-' +
                String(date.getMonth() + 1).padStart(2, '0') + '-' +
                String(date.getDate()).padStart(2, '0');
        }

        $(document).ready(function() {
            let customersStatus = 'all';
            let searchTimeout;
            let datePickerUsed = false; // Track if date picker was actually used

            // Initialize date picker using the function
            resetCustomersDatePicker();

            // Function to completely reset customers date picker to initial state
            function resetCustomersDatePicker() {
                datePickerUsed = false;
                $('#customersDatePicker').val('');

                // Destroy and recreate the date picker to ensure clean state
                if ($('#customersDatePicker').data('daterangepicker')) {
                    $('#customersDatePicker').data('daterangepicker').remove();
                }

                // Reinitialize with same settings as initial load
                $('#customersDatePicker').daterangepicker({
                    autoUpdateInput: false,
                    opens: 'center',
                    locale: {
                        format: 'MMM D, YYYY',
                        cancelLabel: 'Clear'
                    },
                    ranges: {
                        'Today': [moment(), moment()],
                        'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                        'Last Week': [moment().subtract(6, 'days'), moment()],
                        'Last Month': [moment().subtract(29, 'days'), moment()],
                        'This Month': [moment().startOf('month'), moment().endOf('month')]
                    }
                });

                // Reattach event handlers
                attachCustomersDatePickerEvents();
                console.log('Customers date picker completely reset to initial state');
            }

            // Function to attach customers date picker event handlers
            function attachCustomersDatePickerEvents() {
                // Handle apply event
                $('#customersDatePicker').on('apply.daterangepicker', function(ev, picker) {
                    datePickerUsed = true;
                    if (picker.startDate.isSame(picker.endDate, 'day')) {
                        $(this).val(picker.startDate.format('MMM D, YYYY'));
                    } else {
                        $(this).val(picker.startDate.format('MMM D, YYYY') + ' - ' + picker.endDate.format('MMM D, YYYY'));
                    }
                    console.log('Customers date range applied:', $(this).val());
                    filterCustomers();
                });

                // Handle cancel event
                $('#customersDatePicker').on('cancel.daterangepicker', function(ev, picker) {
                    resetCustomersDatePicker();
                    filterCustomers();
                });

                // Handle clear event
                $('#customersDatePicker').on('clear.daterangepicker', function(ev, picker) {
                    resetCustomersDatePicker();
                    filterCustomers();
                });
            }

            // Function to filter customers
            function filterCustomers() {
                let search = $('#customersSearchInput').val().trim();
                let status = customersStatus;
                let date = $('#customersDatePicker').val().trim();

                // Only parse date if date picker was actually used
                let dateFilter = null;
                if (datePickerUsed && date) {
                    dateFilter = parseDateFilter(date);
                }

                // Prepare data object - only include non-empty values
                let data = {};

                if (search) {
                    data.search = search;
                }

                if (status && status !== 'all') {
                    data.status = status;
                }

                // Only include date data if date picker was actually used
                if (datePickerUsed && date) {
                    data.date = date;
                    if (dateFilter) {
                        data.date_filter = dateFilter;
                    }
                }

                console.log('Customers filter data:', data);

                // Show loading state
                showLoadingState();

                $.ajax({
                    url: "{{ route('customers.filter') }}",
                    type: "GET",
                    data: data,
                    beforeSend: function() {
                        // Add searching class to search input
                        $('#customersSearchInput').addClass('searching');
                    },
                    success: function(response) {
                        console.log('Customers filter response:', response);
                        if (response.success) {
                            $('#customersTableBody').html(response.html);

                            // Handle pagination - update with response pagination HTML
                            console.log('Pagination data:', {
                                has_pages: response.has_pages,
                                count: response.count,
                                total: response.total,
                                pagination_html_length: response.pagination ? response.pagination.length : 0
                            });

                            if (response.has_pages && response.count > 0 && response.pagination && response.pagination.trim() !== '') {
                                $('#customersPagination').html(response.pagination).show();
                                console.log('Pagination shown');
                            } else {
                                $('#customersPagination').html('').hide().css('display', 'none');
                                console.log('Pagination hidden - has_pages:', response.has_pages, 'count:', response.count);
                            }

                            console.log('Customers table updated with', response.count, 'customers');

                            // Show success toast with result count
                            if (data.search || data.date || data.status !== 'all') {
                                if (response.count === 0) {
                                    showToast('No results found for your search criteria', 'warning');
                                } else {
                                    showToast(`Found ${response.count} result(s)`, 'success');
                                }
                            }
                        } else {
                            console.error('Filter response not successful:', response);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Customers filter error:', error);
                        console.error('Response:', xhr.responseText);
                        showToast('Search failed. Please check your connection.', 'error');
                    },
                    complete: function() {
                        // Remove loading states
                        $('#customersSearchInput').removeClass('searching');
                        hideLoadingState();
                    }
                });
            }

            // Search on keyup with debounce
            $('#customersSearchInput').on('keyup', function() {
                console.log('Search input changed:', $(this).val());

                // Clear previous timeout
                clearTimeout(searchTimeout);

                // Set new timeout for debounce (300ms delay)
                searchTimeout = setTimeout(function() {
                    filterCustomers();
                }, 300);
            });

            // Status filter
            $('.customers-status-filter').on('click', function(e) {
                e.preventDefault();
                let label = $(this).data('label');
                let color = $(this).data('color');
                let status = $(this).data('status');

                console.log('Status filter clicked:', status, label);
                customersStatus = status;
                $('#customersStatusDropdown .dropdown-toggle span').html(
                    '<span class="dot" style="background-color: ' + color + '"></span> ' + label);
                filterCustomers();
            });

            // Export customers with current filters
            $('#exportCustomersBtn').on('click', function(e) {
                e.preventDefault();

                let search = $('#customersSearchInput').val().trim();
                let status = customersStatus;
                let date = $('#customersDatePicker').val().trim();

                // Only parse date if date picker was actually used
                let dateFilter = null;
                if (datePickerUsed && date) {
                    dateFilter = parseDateFilter(date);
                }

                // Build export URL with query parameters
                let exportUrl = "{{ route('customers.export') }}";
                let params = [];

                if (search) {
                    params.push('search=' + encodeURIComponent(search));
                }

                if (status && status !== 'all') {
                    params.push('status=' + encodeURIComponent(status));
                }

                // Only include date data if date picker was actually used
                if (datePickerUsed && date) {
                    params.push('date=' + encodeURIComponent(date));
                    if (dateFilter) {
                        params.push('date_filter=' + encodeURIComponent(JSON.stringify(dateFilter)));
                    }
                }

                if (params.length > 0) {
                    exportUrl += '?' + params.join('&');
                }

                console.log('Exporting customers with URL:', exportUrl);

                // Redirect to export URL
                window.location.href = exportUrl;
            });


            // Handle manual input changes (only if date picker was used)
            $('#customersDatePicker').on('change', function() {
                if (datePickerUsed) {
                    console.log('Customers date picker manually changed:', $(this).val());
                    filterCustomers();
                }
            });

            // Handle down arrow click to open date picker
            $('.date-picker-container .down-arrow').on('click', function() {
                $('#customersDatePicker').data('daterangepicker').show();
            });

            // Function to reset all filters
            function resetFilters() {
                $('#customersSearchInput').val('');
                resetCustomersDatePicker();
                customersStatus = 'all';
                $('#customersStatusDropdown .dropdown-toggle span').html('<span class="dot"></span> All');
                filterCustomers();
            }

            // Optional: Add a reset button functionality (if you want to add a reset button later)
            $(document).on('click', '.reset-filters', function() {
                resetFilters();
            });

            // Helper functions for loading state and toast notifications
            function showLoadingState() {
                const loadingHtml = `
                    <tr class="loading-row">
                        <td colspan="6" class="text-center py-4">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span>Searching...</span>
                            </div>
                        </td>
                    </tr>
                `;
                $('#customersTableBody').html(loadingHtml);
            }

            function hideLoadingState() {
                $('.loading-row').remove();
            }

            function showToast(message, type) {
                const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                $('body').append(toast);
                const toastElement = $('.toast').last();
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    delay: 3000
                });
                bsToast.show();

                // Remove toast element after it's hidden
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }
        });
    </script>
@endpush
