<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Profile;
use App\Models\Staff;
use App\Models\SubCategory;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class StaffController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();
        $staffs = $user->staffs()->with('category')->where('email', '!=', $user->email)->orderBy('created_at', 'desc')->paginate(10);
        $categories = Category::all();
        $subcategories = SubCategory::all();
        return view('dashboard.business.staffs.index', compact('staffs', 'categories', 'subcategories'));
    }

    /**
     * Filter staff members based on search, status, category, and subcategory
     */
    public function filterStaffs(Request $request)
    {
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $status = $request->filled('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $category = $request->filled('category') ? $request->get('category') : '';
        $subcategory = $request->filled('subcategory') ? $request->get('subcategory') : '';
        $limit = 10;

        // Base query
        $query = auth()->user()->staffs()->with(['category', 'subcategory']);

        // Apply search filter if search term is provided
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        // Apply status filter if provided
        if ($status !== '') {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        // Apply category filter if provided
        if (!empty($category)) {
            $query->where('category_id', $category);
        }

        // Apply subcategory filter if provided
        if (!empty($subcategory)) {
            $query->where('subcategory_id', $subcategory);
        }

        // Get paginated results with ordering
        $staffs = $query->orderBy('created_at', 'desc')->paginate($limit);

        // Generate table HTML
        $html = view('dashboard.business.staffs.partials.staff-table', ['staffs' => $staffs])->render();

        // Generate pagination HTML
        $paginationHtml = '';
        if ($staffs->hasPages()) {
            $paginationHtml = $staffs->appends(request()->query())->links('pagination::bootstrap-4')->render();
        }

        return response()->json([
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $staffs->count(),
            'total' => $staffs->total(),
            'has_pages' => $staffs->hasPages()
        ]);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $categories = Category::all();
        return view('dashboard.business.staffs.create', compact('categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['required', 'mimes:' . config('constant.image_mimes'), 'max:250'],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex'),'unique:staffs,email',],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'phone' => ['required', 'regex:' . config('constant.phone_regex')],
            'country_code' => ['required', 'string', 'max:10'],
            'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
        ], [
            'avatar.required' => 'Please upload an image',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => 'Image size must not exceed 250 KB',
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'email.unique'=> 'This email is already taken',
            'category_id.required' => 'Please select category',
            'phone.required' => 'Please enter phone number',
            'phone.regex' => 'Please enter valid phone number',
            'country_code.required' => 'Please select country code',
            'country_code.max' => 'Country code is too long',
            'subcategory_id.required' => 'Please select sub category',
            'facebook.regex' => 'Please enter valid facebook url',
            'instagram.regex' => 'Please enter valid instagram url',
            'tiktok.regex' => 'Please enter valid tiktok url',
            'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staffData = $validator->validated();
            $staffData['user_id'] = auth()->user()->id;
            
            // Combine country code and phone number
            $countryCode = $staffData['country_code'];
            $phoneNumber = $staffData['phone'];
            $staffData['phone'] = '+' . $countryCode . $phoneNumber;
            unset($staffData['country_code']); // Remove country_code as it's not in the model
            
            if ($request->hasFile('avatar')) {
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff = Staff::create($staffData);
            DB::commit();
            return redirect()->route('staffs.index')->with(["type" => "success", "title" => "Created", "message" => 'Staff Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return redirect()->route('staffs.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $staff = Staff::where('ids', $id)
            ->with([
                'category',
                'subcategory'
            ])
            ->firstOrFail();

        // Calculate booking statistics for this staff member
        $bookingCount = \App\Models\BookingService::where('staff_id', $staff->id)
            ->whereHas('booking', function ($query) {
                $query->where('status', 1); // Only completed bookings
            })
            ->count();

        // Get current month booking count for percentage calculation
        $currentMonthBookings = \App\Models\BookingService::where('staff_id', $staff->id)
            ->whereHas('booking', function ($query) {
                $query->where('status', 1)
                    ->whereMonth('booking_date', now()->month)
                    ->whereYear('booking_date', now()->year);
            })
            ->count();

        // Calculate booking percentage (mock calculation - you can adjust this logic)
        $bookingPercentage = $bookingCount > 0 ? round(($currentMonthBookings / $bookingCount) * 100, 1) : 0;

        return view('dashboard.business.staffs.show', compact('staff', 'bookingCount', 'bookingPercentage'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        return response()->json($staff);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $messagesConfig = config('constant.messages');
        $validator = Validator::make($request->all(), [
            'avatar' => ['nullable', 'mimes:' . config('constant.image_mimes'), 'max:250'],
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.input_text_length')],
            'email' => ['required', 'regex:' . config('constant.email_regex'),'unique:staffs,email,' . $id . ',ids',],
            'category_id' => 'required',
            'subcategory_id' => 'required',
            'phone' => ['required', 'regex:' . config('constant.phone_regex')],
            'country_code' => ['required', 'string', 'max:10'],
            'facebook' => ['nullable', 'regex:' . config('constant.url_regex')],
            'instagram' => ['nullable', 'regex:' . config('constant.url_regex')],
            'tiktok' => ['nullable', 'regex:' . config('constant.url_regex')],
            'youtube' => ['nullable', 'regex:' . config('constant.url_regex')],
        ], [
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => 'Image size must not exceed 250 KB',
            'avatar.mimes' => $messagesConfig['image']['mimes'],
            'avatar.max' => $messagesConfig['image']['max'],
            'name.required' => 'Please enter name',
            'name.regex' => $messagesConfig['input']['regex'],
            'name.max' => $messagesConfig['input']['max'],
            'email.required' => 'Please enter email',
            'email.regex' => 'Please enter valid email',
            'email.unique' => 'This email is already taken',
            'category_id.required' => 'Please select category',
            'subcategory_id.required' => 'Please select sub category',
            'phone.required' => 'Please enter phone number',
            'phone.regex' => 'Please enter valid phone number',
            'country_code.required' => 'Please select country code',
            'country_code.max' => 'Country code is too long',
            'facebook.regex' => 'Please enter valid facebook url',
            'instagram.regex' => 'Please enter valid instagram url',
            'tiktok.regex' => 'Please enter valid tiktok url',
            'youtube.regex' => 'Please enter valid youtube url',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return response()->json([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $staff = Staff::where('ids', $id)->firstOrFail();
            $staffData = $validator->validated();
            
            // Combine country code and phone number
            $countryCode = $staffData['country_code'];
            $phoneNumber = $staffData['phone'];
            $staffData['phone'] = '+' . $countryCode . $phoneNumber;
            unset($staffData['country_code']); // Remove country_code as it's not in the model
            
            if ($request->hasFile('avatar')) {
                $this->deleteImage($staff->image);
                $staffData['image'] = $this->storeImage('staff-images', $request->file('avatar'));
            }
            $staff->update($staffData);
            DB::commit();
            return response()->json(["type" => "success", "title" => "Updated", "message" => 'Staff Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return response()->json(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $staff = Staff::where('ids', $id)->firstOrFail();
        $this->deleteImage($staff->image);
        $staff->delete();
        return redirect()->back()->with(["type" => "success", "title" => "Deleted", "message" => 'Staff Deleted Successfully!!']);
    }

    public function updateStatus(Request $request)
    {
        $staff = Staff::findOrFail($request->staff_id);
        $staff->status = $request->status;
        $staff->save();
        return response()->json(['success' => true]);
    }

}
