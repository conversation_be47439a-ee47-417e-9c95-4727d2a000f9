<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\DiscountCoupon;
use App\Http\Requests\DiscountCouponRequest;
use App\Models\Category;
use App\Models\Service;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;
use App\Models\Subscription;
use App\Models\User;
use Stripe\Stripe;
use Stripe\Coupon;
use Stripe\PromotionCode;
use Stripe\Webhook;

class DiscountCouponsController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response;
     */
    function __construct()
    {
        $this->middleware('permission:discountcoupons-list|discountcoupons-create|discountcoupons-edit|discountcoupons-delete', ['only' => ['index', 'store']]);
        $this->middleware('permission:discountcoupons-create', ['only' => ['create', 'store']]);
        $this->middleware('permission:discountcoupons-edit', ['only' => ['edit', 'update']]);
        $this->middleware('permission:discountcoupons-delete', ['only' => ['destroy']]);
        $this->middleware('permission:discountcoupons-list', ['only' => ['show']]);

        // Set Stripe API key once in constructor
        Stripe::setApiKey(config('services.stripe.secret_key'));
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index(Request $request)
    {
        $tab = $request->get('tab', 'service'); // Default to service tab

        // Restrict subscription access to admin and super admin only
        if ($tab === 'subscription' && !auth()->user()->hasRole(['admin', 'super admin'])) {
            $tab = 'service'; // Force to service tab for non-admin users
        }

        $discountType = $tab === 'subscription' ? 'subscription' : 'service';

        $discountcoupons = DiscountCoupon::where('user_id', auth()->user()->id)
            ->where('discount_type', $discountType)
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('dashboard.admin.discount coupons.index', compact('discountcoupons', 'tab'));
    }

    /**
     * Filter discount coupons based on search and status
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filterDiscountCoupons(Request $request)
    {
        // Only get values if they are actually provided and not empty
        $search = $request->filled('search') ? trim($request->get('search')) : '';
        $status = $request->filled('status') && $request->get('status') !== 'all' ? $request->get('status') : '';
        $tab = $request->get('tab', 'service'); // Get the current tab

        // Restrict subscription access to admin and super admin only
        if ($tab === 'subscription' && !auth()->user()->hasRole(['admin', 'super admin'])) {
            $tab = 'service'; // Force to service tab for non-admin users
        }

        $discountType = $tab === 'subscription' ? 'subscription' : 'service';

        // Base query - scope to current user and discount type
        $query = DiscountCoupon::where('user_id', auth()->id())
            ->where('discount_type', $discountType);

        // Only apply search filter if search term is provided
        if ($search !== '') {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                    ->orWhere('coupon_code', 'LIKE', "%{$search}%");
            });
        }

        // Only apply status filter if status is provided
        if (!empty($status)) {
            if ($status === 'active') {
                $query->where('status', 1);
            } elseif ($status === 'inactive') {
                $query->where('status', 0);
            }
        }

        $discountcoupons = $query->orderBy('created_at', 'desc')->paginate(10);

        $html = view('dashboard.admin.discount coupons.partials.table-rows', ['discountcoupons' => $discountcoupons])->render();

        // Generate pagination HTML
        $paginationHtml = '';
        if ($discountcoupons->hasPages()) {
            $paginationHtml = $discountcoupons->links('pagination::bootstrap-4')->render();
        }

        return response()->json([
            'html' => $html,
            'pagination' => $paginationHtml,
            'count' => $discountcoupons->count(),
            'total' => $discountcoupons->total(),
            'has_pages' => $discountcoupons->hasPages()
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create($type)
    {
        // Restrict subscription coupon creation to admin and super admin only
        if ($type === 'subscription' && !auth()->user()->hasRole(['admin', 'super admin'])) {
            return redirect()->route('discount-coupons.create', ['type' => 'service'])
                ->with(['error' => true, 'message' => 'You do not have permission to create subscription coupons']);
        }

        $categories = Category::all();
        $services = auth()->user()->hasAnyRole(['admin', 'super admin'])
            ? Service::all()
            : Service::where('user_id', auth()->id())->get();

        // For subscription coupons, get subscriptions
        $subscriptions = [];
        if ($type === 'subscription') {
            // Assuming you have a Subscription model - adjust the query as needed
            $subscriptions = Subscription::all();
        }

        return view('dashboard.admin.discount coupons.create&edit', compact('categories', 'services', 'subscriptions', 'type'));
    }
    /**
     * Store a newly created resource in storage.
     *
     * @param  DiscountCouponRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(DiscountCouponRequest $request)
    {
        $rules = [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'coupon_code' => ['required', 'regex:' . config('constant.input_regex'), 'max:50', 'unique:discount_coupons,coupon_code,NULL,id,discount_type,' . $request->discount_type],
            'type' => 'required|in:percentage,amount',
            'discount' => 'required|integer',
            'user_limit' => 'required|integer',
            'discount_type' => 'required|in:service,subscription',
            // 'user_id' => 'required|exists:users,id', // Will be set automatically
            'end_date' => 'required|date|after:today',
        ];

        // Add start_date validation only for service coupons
        if ($request->discount_type === 'service') {
            $rules['start_date'] = 'required|date';
            $rules['end_date'] = 'required|date|after_or_equal:start_date';
        }

        // Add conditional validation based on discount_type
        if ($request->discount_type === 'subscription') {
            $rules['duration'] = 'required|in:1,2,3,6,9,12';
            $rules['subscriptions'] = 'required|array|min:1';
            $rules['subscriptions.*'] = 'exists:subscriptions,id';
        } else {
            $rules['applies_to'] = 'required|in:category,service';
            if ($request->applies_to === 'category') {
                $rules['categories'] = 'required|array|min:1';
                $rules['categories.*'] = 'exists:categories,id';
            } else {
                $rules['services'] = 'required|array|min:1';
                $rules['services.*'] = 'exists:services,id';
            }
        }

        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $discountcouponData = $validator->validated();
            $discountcouponData['user_id'] = auth()->user()->id;
            $discountcouponData['usage_count'] = 0; // Initialize usage count, managed by webhooks

            // Create Stripe coupon for subscription discounts
            if ($request->discount_type === 'subscription') {
                $stripeCoupon = $this->createStripeCoupon($request);
                $discountcouponData['stripe_coupon_id'] = $stripeCoupon->id;
            }

            $coupon = DiscountCoupon::create($discountcouponData);

            // Handle relationships based on discount type
            if ($request->discount_type === 'subscription') {
                // Sync subscriptions for subscription coupons
                if ($request->has('subscriptions')) {
                    $coupon->subscriptions()->sync($request->subscriptions);
                }
            } else {
                // Handle service/category relationships for service coupons
                if ($request->applies_to === 'category' && $request->has('categories')) {
                    $coupon->categories()->sync($request->categories);
                }
                if ($request->applies_to === 'service' && $request->has('services')) {
                    $coupon->services()->sync($request->services);
                }
            }

            DB::commit();
            return to_route('discount-coupons.index')->with(["type" => "success", "title" => "Created", "message" => 'Discount Coupon Created Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return to_route('discount-coupons.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $discountcoupon = DiscountCoupon::where('ids', $id)->firstOrFail();
        return view('discountcoupons.show', ['discountcoupon' => $discountcoupon]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $discountcoupon = DiscountCoupon::where('ids', $id)->firstOrFail();
        $categories = Category::all();
        $services = Service::all();

        // Determine the type based on discount_type
        $type = $discountcoupon->discount_type;

        // If it's a subscription coupon, fetch subscriptions
        $subscriptions = null;
        if ($type === 'subscription') {
            $subscriptions = Subscription::all();
        }

        return view('dashboard.admin.discount coupons.create&edit', compact('discountcoupon', 'categories', 'services', 'type', 'subscriptions'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  DiscountCouponRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(DiscountCouponRequest $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'regex:' . config('constant.input_regex'), 'max:' . config('constant.name_length')],
            'coupon_code' => ['required', 'regex:' . config('constant.input_regex'), 'max:50', 'unique:discount_coupons,coupon_code,' . $id . ',ids,discount_type,' . $request->discount_type],
            'type' => 'required|in:percentage,amount',
            'discount' => 'required|integer',
            'user_limit' => 'required|integer',
            'applies_to' => 'required|in:category,service',
            'discount_type' => 'required|in:service,subscription',
            // 'user_id' => 'required|exists:users,id', // Will be set automatically
            'end_date' => 'required|date|after:today',
        ]);
        if ($validator->fails()) {
            $errors = implode('<br>', $validator->errors()->all());
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $errors,
                'title' => 'Validation Errors',
            ]);
        }
        try {
            DB::beginTransaction();
            $discountcouponData = $validator->validated();
            $coupon = DiscountCoupon::where('ids', $id)->firstOrFail();
            $coupon->update($discountcouponData);

            // Update Stripe coupon for subscription discounts
            if ($request->discount_type === 'subscription' && $coupon->stripe_coupon_id) {
                $this->updateStripeCoupon($coupon, $request);
            }

            // Handle relationships based on discount type
            if ($request->discount_type === 'subscription') {
                // Detach and sync subscriptions for subscription coupons
                $coupon->subscriptions()->detach();
                if ($request->has('subscriptions')) {
                    $coupon->subscriptions()->sync($request->subscriptions);
                }
            } else {
                // Handle service/category relationships for service coupons
                $coupon->categories()->detach();
                $coupon->services()->detach();
                if ($request->applies_to === 'category' && $request->has('categories')) {
                    $coupon->categories()->sync($request->categories);
                }
                if ($request->applies_to === 'service' && $request->has('services')) {
                    $coupon->services()->sync($request->services);
                }
            }
            DB::commit();
            return to_route('discount-coupons.index')->with(["type" => "success", "title" => "Created", "message" => 'Discount Coupon Updated Successfully!!']);
        } catch (\Throwable $th) {
            DB::rollback();
            return to_route('discount-coupons.index')->with(["type" => "error", "message" => $th->getMessage(), "title" => "Error"]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        try {
            $discountcoupon = DiscountCoupon::where('ids', $id)->firstOrFail();

            // Delete from Stripe if it's a subscription coupon
            if ($discountcoupon->discount_type === 'subscription' && $discountcoupon->stripe_coupon_id) {
                $this->deleteStripeCoupon($discountcoupon->stripe_coupon_id);
            }

            $discountcoupon->delete();
            return redirect()->back()->with([
                'success' => true,
                'message' => 'Discount Coupon deleted successfully'
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'error' => true,
                'message' => 'Failed to delete coupon: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Check if coupon code already exists
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function checkCouponCode(Request $request)
    {
        $couponCode = $request->input('coupon_code');
        $currentId = $request->input('id');
        $discountType = $request->input('discount_type');

        $query = DiscountCoupon::where('coupon_code', $couponCode)->where('discount_type', $discountType);
        if ($currentId) {
            $query->where('ids', '!=', $currentId);
        }
        $exists = $query->exists();
        return response()->json([
            'available' => !$exists
        ]);
    }

    public function updateStatus(Request $request)
    {
        try {
            $discountcoupon = DiscountCoupon::where('ids', $request->discountcoupon_id)->firstOrFail();
            $discountcoupon->status = $request->status;
            $discountcoupon->save();

            // For subscription coupons, also update Stripe promotion codes
            if ($discountcoupon->discount_type === 'subscription' && $discountcoupon->stripe_coupon_id) {
                $this->updateStripePromotionCodeStatus($discountcoupon->stripe_coupon_id, $request->status);
            }

            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            \Log::error('Failed to update coupon status: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to update status']);
        }
    }

    /**
     * Create Stripe coupon and promotion code for subscription discounts
     */
    private function createStripeCoupon($request)
    {
        // First create the coupon
        $couponData = [
            'id' => $request->coupon_code,
            'name' => $request->name,
        ];

        // Set discount type and value
        if ($request->type === 'percentage') {
            $couponData['percent_off'] = $request->discount;
        } else {
            $couponData['amount_off'] = $request->discount * 100; // Convert to cents
            $couponData['currency'] = 'usd'; // Adjust as needed
        }

        // Set duration
        if ($request->has('duration')) {
            $couponData['duration'] = 'repeating';
            $couponData['duration_in_months'] = (int) $request->duration;
        } else {
            $couponData['duration'] = 'once';
        }

        // Set redemption limits
        if ($request->user_limit) {
            $couponData['max_redemptions'] = $request->user_limit;
        }

        // Set validity period - Stripe coupons are active immediately upon creation
        if ($request->has('end_date')) {
            $couponData['redeem_by'] = strtotime($request->end_date);
        }

        $coupon = Coupon::create($couponData);

        // Now create a promotion code for the coupon
        $promotionCodeData = [
            'coupon' => $coupon->id,
            'code' => $request->coupon_code,
            'active' => true,
        ];

        // Set expiration date if provided
        if ($request->has('end_date')) {
            $promotionCodeData['expires_at'] = strtotime($request->end_date);
        }

        // Set max redemptions if provided
        if ($request->user_limit) {
            $promotionCodeData['max_redemptions'] = $request->user_limit;
        }

        $promotionCode = PromotionCode::create($promotionCodeData);

        return $coupon;
    }

    /**
     * Update Stripe coupon for subscription discounts
     */
    private function updateStripeCoupon($coupon, $request)
    {
        try {
            // Note: Stripe coupons are immutable - we can't update them directly
            // We need to delete the old one and create a new one
            $oldCouponId = $coupon->stripe_coupon_id;

            // Delete old Stripe coupon and its promotion codes
            $this->deleteStripeCoupon($oldCouponId);

            // Create new Stripe coupon with updated data (this will also create promotion code)
            $newStripeCoupon = $this->createStripeCoupon($request);

            // Update the coupon with new Stripe ID
            $coupon->update(['stripe_coupon_id' => $newStripeCoupon->id]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to update Stripe coupon: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Delete Stripe coupon and its promotion codes when deleting discount coupon
     */
    private function deleteStripeCoupon($stripeCouponId)
    {
        try {
            // First, deactivate all promotion codes for this coupon
            $promotionCodes = PromotionCode::all(['coupon' => $stripeCouponId]);
            foreach ($promotionCodes->data as $promotionCode) {
                $promotionCode->update(['active' => false]);
            }

            // Then delete the coupon (this will also deactivate any remaining promotion codes)
            $coupon = Coupon::retrieve($stripeCouponId);
            $coupon->delete();

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to delete Stripe coupon: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle Stripe webhook for coupon usage tracking
     */
    public function handleStripeWebhook(Request $request)
    {
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');
        $endpoint_secret = config('services.stripe.coupons_webhook_secret');

        try {
            $event = Webhook::constructEvent($payload, $sig_header, $endpoint_secret);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        // Handle coupon usage events
        if ($event->type === 'checkout.session.completed') {
            $session = $event->data->object;
            if (isset($session->discount) && isset($session->discount->coupon)) {
                $this->trackCouponUsage($session->discount->coupon->id);
            }
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Track coupon usage and send admin notification
     */
    private function trackCouponUsage($stripeCouponId)
    {
        $coupon = DiscountCoupon::where('stripe_coupon_id', $stripeCouponId)->first();

        if ($coupon) {
            // Increment usage count
            $coupon->increment('usage_count');

            // Send admin notification
            $this->sendCouponUsageNotification($coupon);
        }
    }

    /**
     * Send notification to admin and super admin when coupon is used
     */
    private function sendCouponUsageNotification($coupon)
    {
        $this->notifyAdmins(
            'Coupon Used',
            "Coupon '{$coupon->name}' ({$coupon->coupon_code}) has been used. Total usage: {$coupon->usage_count}",
            null,
            'coupon_used'
        );
    }

    /**
     * Validate subscription coupon with date and usage checks
     */
    public function validateSubscriptionCoupon(Request $request)
    {
        try {
            $couponCode = $request->input('coupon_code');

            if (!$couponCode) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Please enter a coupon code'
                ]);
            }

            $coupon = DiscountCoupon::where('coupon_code', $couponCode)
                ->where('discount_type', 'subscription')
                ->where('status', 1)
                ->first();

            if (!$coupon) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Invalid coupon code'
                ]);
            }

            // Check if coupon has expired
            $now = now();
            if ($coupon->end_date && $now->gt($coupon->end_date)) {
                return response()->json([
                    'valid' => false,
                    'message' => 'Coupon has expired. Valid until ' . $coupon->end_date
                ]);
            }

            // Check usage limit
            if ($coupon->user_limit && $coupon->usage_count >= $coupon->user_limit) {
                return response()->json([
                    'valid' => false,
                    'message' => 'This coupon has reached its usage limit'
                ]);
            }

            return response()->json([
                'valid' => true,
                'coupon' => $coupon
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'valid' => false,
                'message' => 'Error validating coupon: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Update Stripe promotion code status (active/inactive)
     */
    private function updateStripePromotionCodeStatus($stripeCouponId, $status)
    {
        try {
            // Get all promotion codes for this coupon
            $promotionCodes = PromotionCode::all(['coupon' => $stripeCouponId]);

            foreach ($promotionCodes->data as $promotionCode) {
                $promotionCode->update(['active' => (bool) $status]);
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to update Stripe promotion code status: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create promotion code for existing Stripe coupon
     */
    public function createPromotionCodeForExistingCoupon($couponId)
    {
        try {
            $coupon = DiscountCoupon::where('ids', $couponId)->firstOrFail();

            if ($coupon->discount_type === 'subscription' && $coupon->stripe_coupon_id) {
                // Check if promotion code already exists
                $existingPromotionCodes = PromotionCode::all([
                    'coupon' => $coupon->stripe_coupon_id,
                    'code' => $coupon->coupon_code
                ]);

                if ($existingPromotionCodes->data->count() > 0) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Promotion code already exists for this coupon'
                    ]);
                }

                // Create promotion code
                $promotionCodeData = [
                    'coupon' => $coupon->stripe_coupon_id,
                    'code' => $coupon->coupon_code,
                    'active' => true,
                ];

                // Set expiration date if provided
                if ($coupon->end_date) {
                    $promotionCodeData['expires_at'] = strtotime($coupon->end_date);
                }

                // Set max redemptions if provided
                if ($coupon->user_limit) {
                    $promotionCodeData['max_redemptions'] = $coupon->user_limit;
                }

                $promotionCode = PromotionCode::create($promotionCodeData);

                return response()->json([
                    'status' => true,
                    'message' => 'Promotion code created successfully',
                    'promotion_code_id' => $promotionCode->id
                ]);
            }

            return response()->json([
                'status' => false,
                'message' => 'This is not a subscription coupon or no Stripe coupon ID found'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to create promotion code: ' . $e->getMessage()
            ]);
        }
    }
}
