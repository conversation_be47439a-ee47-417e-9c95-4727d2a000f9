<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Spatie\Permission\Models\Role;

class Profile extends Model
{
    protected $guarded= [];

    protected $fillable = [
        'user_id', 'bio', 'gender','dob','age','pic','country','state','city','address','postal',
        'full_name', 'company_name', 'phone', 'website', 'facebook', 'instagram', 'tiktok',
        'location', 'lat', 'lng', 'location_service', 'company_id', 'vat_number', 'avatar',
        'services', 'certifications', 'custom_certifications', 'availability', 'holidays',
        'banner_image', 'gallery_images', 'bank_name', 'account_number', 'sort_code',
        'iban', 'swift', 'slug'
    ];

    public function user(){
        return $this->belongsTo(User::class);
    }

    public function user_roles(){
        return $this->belongsTo(Role::class);
    }
    public function setCompanyNameArribute($value)
    {
        $this->attributes['company_name'] = ucfirst($value);
    }

    /**
     * Generate a unique slug for the profile
     */
    public function generateSlug()
    {
        $companyName = $this->company_name ?: 'company';
        $userName = $this->user->name ?: 'user';
        
        // Create base slug from company name and user name with hyphen
        $companySlug = $this->createSlug($companyName);
        $userSlug = $this->createSlug($userName);
        $baseSlug = $companySlug . '-' . $userSlug;
        
        // Check if slug already exists
        $slug = $baseSlug;
        $counter = 1;
        
        while (static::where('slug', $slug)->where('id', '!=', $this->id)->exists()) {
            $slug = $companySlug . '-' . $userSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Create a URL-friendly slug from a string
     */
    private function createSlug($string)
    {
        // Convert to lowercase
        $slug = strtolower($string);
        
        // Replace forward slashes with hyphens first
        $slug = str_replace('/', '-', $slug);
        
        // Replace spaces and special characters with hyphens
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        
        // Remove leading/trailing hyphens
        $slug = trim($slug, '-');
        
        // Limit length
        return substr($slug, 0, 100);
    }

    /**
     * Boot method to auto-generate slug when profile is created/updated
     */
    protected static function boot()
    {
        parent::boot();
        
        static::saving(function ($profile) {
            if (empty($profile->slug) || $profile->isDirty(['company_name']) || $profile->isDirty(['user_id'])) {
                $profile->slug = $profile->generateSlug();
            }
        });
    }
}
