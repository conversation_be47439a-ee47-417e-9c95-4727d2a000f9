<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\CmsPage;
use App\Models\Home;
use App\Models\PrivacyAndTerm;
use App\Models\Service;
use App\Models\User;
use App\Models\Booking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;

class WebsiteController extends Controller
{
    function __construct()
    {
        $page = CmsPage::all();
        view()->share('pages', $page);
    }
    public function index()
    {
        $user = auth()->user();
        $page = Home::with('details')->first();
        $categories = Category::where('status', 1)->get();
        $services = Service::active()->with(['reviews.user.profile', 'user.profile'])->get();
        $professionals = User::professional()->where('approval', 1)->where('registration_completed', 1)->orderBy('created_at', 'desc')->limit(10)->get();
        $featured_services = Service::featured()->with(['reviews.user.profile', 'user.profile'])->get();
        $featured_professionals = User::featured_professional()->orderBy('created_at', 'desc')->limit(10)->get();
        
        // Get professional count for each category
        $categoryProfessionalCounts = [];
        foreach ($categories as $category) {
            $count = User::whereHas('userCategories', function($query) use ($category) {
                $query->where('category_id', $category->id);
            })
            ->whereHas('roles', function($query) {
                $query->whereIn('name', ['individual', 'business']);
            })
            ->where('approval', 1)
            ->where('registration_completed', 1)
            ->where('status', 1)
            ->count();
            $categoryProfessionalCounts[$category->id] = $count;
        }
        if($user?->hasRole('customer') && $user->registration_completed == 1){
            $lat = $user->profile->lat;
            $lng = $user->profile->lng;
            $nearbyUsers = User::nearby($lat, $lng, 20)->where('users.id', '!=', auth()->id())->where('users.status', 1)->get();
        }else{
            $nearbyUsers = null;
        }
        return view('website.index', compact('page', 'categories', 'professionals', 'featured_services', 'featured_professionals', 'nearbyUsers', 'categoryProfessionalCounts'));
    }

    public function services($category = null, $subcategory = null)
    {
        $user = auth()->user();
        $recommendedServices = null;
        try {
            if ($user) {
                $url = "https://985ffb1583bd.ngrok-free.app/recommendations/{$user->id}";
                $response = Http::get($url);
                if ($response->successful()) {
                    $recommendations = $response->json()['recommendations'] ?? [];
                    if (!empty($recommendations)) {
                        $ids = collect($recommendations)->pluck('id')->toArray();
                        $recommendedServices = Service::whereIn('id', $ids)->get();
                    }
                }
            }
            $categories = Category::whereHas('subcategories')->active()->get();
            if (!$category && !$subcategory) {
                $firstCategory = $categories->first();
                if ($firstCategory) {
                    $firstSubcategory = $firstCategory->subcategories->first();
                    if ($firstSubcategory) {
                        return redirect()->route('website_services', [
                            'category' => $firstCategory->slug,
                            'subcategory' => $firstSubcategory->slug
                        ]);
                    }
                }
            }
            $recommendedServices = $recommendedServices ?: [];
            $active_category = $category;
            $active_subcategory = $subcategory;
            return view('website.service', compact('recommendedServices', 'categories', 'active_category', 'active_subcategory'));
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while processing the request',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function filterServices(Request $request)
    {
        try {
            $search = trim($request->get('search', ''));
            $categorySlug = $request->get('category');
            $subcategorySlug = $request->get('subcategory');

            if ($search) {
                // For search results, load services globally and don't show categories/subcategories
                $services = Service::with(['reviews.user.profile', 'user.profile'])
                    ->where('name', 'like', "%{$search}%")
                    ->orWhereHas('user.profile', function ($q) use ($search) {
                        $q->where('company_name', 'like', "%{$search}%");
                    })
                    ->active()
                    ->orderByDesc('is_featured')
                    ->latest()
                    ->get();

                // For search results, don't show categories/subcategories - they'll be hidden by frontend
                $categories = collect();
                $selectedCategory = null;
                $subcategories = collect();
                $selectedSubcategory = null;

                $resultCount = $services->count();
                $message = $resultCount > 0 ? "{$resultCount} result" . ($resultCount > 1 ? 's' : '') . " found" : "No results found";
            } else {
                // Load categories and subcategories for normal category/subcategory filtering
                $categories = Category::whereHas('subcategories')->active()->with('subcategories')->get();
                $selectedCategory = $categories->firstWhere('slug', $categorySlug) ?? $categories->first();
                $subcategories = $selectedCategory?->subcategories ?? collect();
                $selectedSubcategory = $subcategories->firstWhere('slug', $subcategorySlug) ?? $subcategories->first();

                // Load services based on category/subcategory
                $services = Service::with(['reviews.user.profile', 'user.profile'])
                    ->when($selectedCategory, function ($query) use ($selectedCategory, $selectedSubcategory) {
                        $query->where('category_id', $selectedCategory->id)
                            ->where('subcategory_id', $selectedSubcategory->id);
                    })
                    ->active()
                    ->orderByDesc('is_featured')
                    ->latest()
                    ->get();

                $resultCount = $services->count();
                $message = "Services";
            }

            // Render view
            $data = view('website.template.subcategory-service', compact(
                'categories',
                'subcategories',
                'services',
                'selectedSubcategory'
            ))->render();

            return api_response(true, $message, [
                'page' => $data,
                'category' => $selectedCategory?->slug ?? '',
                'subcategory' => $selectedSubcategory?->slug ?? '',
                'count' => $resultCount
            ]);
        } catch (\Exception $e) {
            return api_response(false, 'Something went wrong');
        }
    }

    public function headerSearch(Request $request)
    {
        $query = $request->get('query', '');

        if (empty($query)) {
            return response()->json([
                'success' => true,
                'services' => []
            ]);
        }

        // Search services by name and description, limit to 5 results
        $services = Service::active()
            ->where(function ($q) use ($query) {
                $q->where('name', 'LIKE', '%' . $query . '%')
                  ->orWhere('description', 'LIKE', '%' . $query . '%');
            })
            ->with(['category', 'subcategory', 'user'])
            ->limit(5)
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'category' => $service->category->name ?? '',
                    'subcategory' => $service->subcategory->name ?? '',
                    'price' => $service->price ? '$' . number_format($service->price, 2) : 'Price on request',
                    'professional_name' => $service->user->name ?? '',
                    'url' => route('professional_profile', $service->user->profile->slug)
                ];
            });

        return response()->json([
            'success' => true,
            'services' => $services
        ]);
    }
    public function getServiceDetails(Request $request, $id)
    {
        $service = Service::where('ids', $id)->with(['availabilities', 'reviews.user.profile', 'user.profile'])->firstOrFail();
        $duration = $service->duration;
        $timeSlots = $service->availabilitiesOnDate($request->date)->get(['start_time', 'end_time'])->map(function ($slot) use ($duration) {
            return [
                'start_time' => $slot->start_time,
                'end_time'   => $slot->end_time,
                'duration'   => $duration,
            ];
        });
        return response()->json([
            'status' => true,
            'message' => 'Service Details',
            'data' => view('website.template.service-details', compact('service'))->render(),
            'services' => $service,  // raw service data sent separately
            'slots' => $timeSlots  // raw service data sent separately
        ]);
    }
    public function getById($id, $service_id = null)
    {
        $services = Service::where('user_id', $id)->get();
        $service_id = $service_id ?? $services->first()->ids;
        $html = view('dashboard.templates.modal.professional.add-professional-services', compact('services', 'service_id'))->render();

        return response()->json([
            'html' => $html,
            'service_id' => $service_id
        ]);
    }
    public function getServiceTimeSlots(Request $request, $id)
    {
        $service = Service::where('ids', $id)->with('availabilities')->firstOrFail();
        $duration = $service->duration;
        $date = $request->get('date', now()->format('Y-m-d'));

        $timeSlots = $service->availabilitiesOnDate($date)->get(['start_time', 'end_time'])->map(function ($slot) use ($duration) {
            return [
                'start_time' => $slot->start_time,
                'end_time'   => $slot->end_time,
                'duration'   => $duration,
            ];
        });
        $bookedSlots = Booking::where('service_id', $service->id)->whereNotIn('status', [2, 3, 4])->where('booking_date', $date)->pluck('booking_time');
        return response()->json([
            'status' => true,
            'message' => 'Time Slots Retrieved',
            'slots' => $timeSlots,
            'date' => $date,
            'bookedSlots' => $bookedSlots
        ]);
    }

    public function getServiceAvailabiliesDetails($id)
    {
        $service = Service::where('ids', $id)->with(['user.profile'])->firstOrFail();

        return api_response(true, "Service Details", view('website.template.service-details', compact('service'))->render());
    }
    public function getfamilyDetails($type)
    {
        $friends  = auth()->user()->friends;
        return api_response(true, "Service Details", view('website.template.related-friends', compact('friends'))->render());
    }

    public function professional($category = null, $subcategory = null)
    {
        $user = auth()->user();
        $categories = Category::wherehas('subcategories')->active()->get();
        $active_category = $category ?? $categories->first()->slug;
        $active_subcategory = $subcategory;
        $professionals = User::professional()->orderBy('is_featured', 'desc')->latest()->get();
        if($user){
            $lat = $user->profile->lat;
            $lng = $user->profile->lng;
            $nearbyUsers = User::nearby($lat, $lng, 20)->where('users.id', '!=', auth()->id())->where('users.status', 1)->get();
        }else{
            $nearbyUsers = collect(); // Empty collection instead of null
        }
        return view('website.professional', compact('professionals', 'categories', 'active_category', 'active_subcategory', 'nearbyUsers'));
    }

    public function filterProfessional(Request $request)
    {
        try {
            $search = $request->input('search');
            $user = auth()->user();

            // Get location if logged in
            $lat = $user?->profile->lat;
            $lng = $user?->profile->lng;

            // Nearby users (used in both branches)
            $nearbyUsers = $user ? User::nearby($lat, $lng, 20)->where('users.id', '!=', $user->id)->where('users.status', 1)->get() : collect();

            if ($search) {
                $professionals = User::professional()
                    ->where(function ($query) use ($search) {
                        $query->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%")
                            ->orWhereHas('profile', function ($q) use ($search) {
                                $q->where('bio', 'like', "%{$search}%")
                                    ->orWhere('location', 'like', "%{$search}%")
                                    ->orWhere('company_name', 'like', "%{$search}%");
                            });
                    })
                    ->orderByDesc('is_featured')
                    ->latest()
                    ->get();

                // For search results, don't show categories/subcategories - they'll be hidden by frontend
                $selectedCategory = null;
                $subcategories = collect();
                $selectedSubcategory = null;

                $resultCount = $professionals->count();
                $message = $resultCount > 0 ? "{$resultCount} result" . ($resultCount > 1 ? 's' : '') . " found" : "No results found";

            } else {
                $selectedCategory = Category::with('subcategories')
                    ->active()
                    ->when($request->filled('category'), function ($query) use ($request) {
                        $query->where('slug', $request->category);
                    })
                    ->first();

                if (!$selectedCategory) {
                    return api_response(false, 'Category not found');
                }

                $subcategories = $selectedCategory->subcategories;
                $selectedSubcategory = $subcategories
                    ->when($request->filled('subcategory'), function ($collection) use ($request) {
                        return $collection->where('slug', $request->subcategory);
                    })
                    ->first();

                if (!$selectedSubcategory) {
                    return api_response(false, 'Subcategory not found');
                }

                $professionals = User::professional()
                    ->whereHas('userCategories', function ($q) use ($selectedSubcategory) {
                        $q->where('subcategory_id', $selectedSubcategory->id);
                    })
                    ->orderByDesc('is_featured')
                    ->latest()
                    ->get();

                $resultCount = $professionals->count();
                $message = "Professionals loaded";
            }

            $data = view('website.template.subcategory-professional', compact(
                'selectedCategory',
                'selectedSubcategory',
                'subcategories',
                'professionals',
                'nearbyUsers'
            ))->render();

            return api_response(true, $message, [
                'page' => $data,
                'category' => $selectedCategory?->slug,
                'subcategory' => $selectedSubcategory?->slug,
                'search' => $search,
                'count' => $resultCount
            ]);

        } catch (\Exception $e) {
            return api_response(false, 'Something went wrong');
        }
    }

    public function privacyPolicy()
    {
        $policies = PrivacyAndTerm::where('type', 'privacy')->get();
        return view('website.privacy-policy', compact('policies'));
    }
    public function terms()
    {
        $terms = PrivacyAndTerm::where('type', 'term')->get();
        return view('website.term', compact('terms'));
    }

    public function clear_all()
    {
        Artisan::call('route:clear');
        Artisan::call('cache:clear');
        Artisan::call('optimize:clear');
        Artisan::call('view:clear');
        Artisan::call('config:clear');
        return '<div style="text-align:center;"> <h1 style="text-align:center;">Cache and Config and permission cache are cleared.</h1><h4><a href="/">Go to home</a></h4></div>';
    }

    public function showPage($slug)
    {
        $page = CmsPage::where('slug', $slug)->first();
        $decodedSnippet = html_entity_decode($page->code_snippets);
        if (!$page) {
            return redirect()->route('home');
        }
        return view('website.show', compact('page', 'decodedSnippet'));
    }
    public function searchTreatments(Request $request)
    {
        if ($request->filled('date') && $request->filled('time')) {
            $searchDate = Carbon::parse($request->date);
            $searchTime = $request->time;

            // Condition 1: If date is today and time has passed, return no results
            if ($searchDate->isToday()) {
                $searchDateTime = Carbon::parse($request->date . ' ' . $searchTime);
                if ($searchDateTime->isPast()) {
                    return view('website.search-results', ['services' => collect(), 'message' => 'Selected time has already passed.']);
                }
            }
        }
        $query = Service::query();
        // Apply search filter
        if ($request->filled('search')) {
            $query->where('name', 'LIKE', '%' . $request->search . '%');
        }
        // Apply location filter
        if ($request->filled('lat') && $request->filled('lng')) {
            $query->nearLocation($request->lat, $request->lng, 4);
        }
        $services = $query->with(['reviews.user.profile', 'user.profile'])->get();

        // Apply date and time filters if provided
        if ($request->filled('date') && $request->filled('time')) {
            $searchDate = $request->date;
            $searchTime = Carbon::parse($request->time)->format('H:i:s'); // Convert to 24-hour format
            $searchDay = Carbon::parse($request->date)->format('l'); // Get day name (Monday, Tuesday, etc.)

            // Condition 2: Exclude services with existing bookings
            $bookedServiceIds = Booking::where('booking_date', $searchDate)
                ->where('booking_time', $searchTime)
                ->pluck('service_id')
                ->toArray();

            // Condition 3: Filter services based on slot availability
            $services = $services->filter(function ($service) use ($searchDate, $searchTime, $searchDay) {
                // Get service availability for the date
                $availability = $service->availabilities()
                    ->where('date', $searchDate)
                    ->where('day', $searchDay)
                    ->first();

                if (!$availability) return false;

                // Generate time slots based on service duration
                $slots = $this->generateTimeSlots($availability->start_time, $availability->end_time, $service->duration);

                // Check if search time falls in any slot
                foreach ($slots as $slot) {
                    if ($searchTime >= $slot['start'] && $searchTime < $slot['end']) {
                        // If today, check if slot time has passed
                        if (Carbon::parse($searchDate)->isToday()) {
                            $slotDateTime = Carbon::parse($searchDate . ' ' . $slot['start']);
                            if ($slotDateTime->isPast()) {
                                continue; // Skip this slot, check next one
                            }
                        }

                        // Check if this slot is not booked
                        $isBooked = Booking::where('service_id', $service->id)
                            ->where('booking_date', $searchDate)
                            ->where('booking_time', $slot['start'])
                            ->exists();

                        return !$isBooked;
                    }
                }

                return false;
            });
        }
        return view('website.search-results', compact('services'));
    }

    private function generateTimeSlots($startTime, $endTime, $duration)
    {
        $slots = [];
        $start = Carbon::parse($startTime);
        $end = Carbon::parse($endTime);

        while ($start->addMinutes($duration) <= $end) {
            $slotStart = $start->copy()->subMinutes($duration);
            $slots[] = [
                'start' => $slotStart->format('H:i:s'),
                'end' => $start->format('H:i:s')
            ];
        }

        return $slots;
    }

    public function professional_profile($slug)
    {
        $user = User::whereHas('profile', function($query) use ($slug) {
                $query->where('slug', $slug);
            })
            ->with([
                'profile',
                'userCategories.category',
                'userCategories.subcategory',
                'socials',
                'galleries',
                'product_cerficates',
                'certificates',
                'allOpeningHours',
                'allHolidays',
                'introCards',
                'services',
                'staffs',
                'receivedReviews.user.profile',
                'portfolios'
            ])
            ->firstOrFail();
        $userCategories = $user->userCategories->whereNotNull('category_id')
            ->unique('category_id')
            ->pluck('category')
            ->filter();

        $userSubcategories = $user->userCategories->whereNotNull('subcategory_id')
            ->pluck('subcategory')
            ->filter();
        $userSubcategoriesByCategory = $userSubcategories->groupBy('category_id');

        // Get reviews for this professional through their bookings
        $reviews = $user->receivedReviews;

        // Check if current user has favorited this professional
        $isFavorited = false;
        if (auth()->check()) {
            $isFavorited = auth()->user()->favoriteProfessionals()->where('professional_id', $user->id)->exists();
        }

        return view('dashboard.customer.professional_profile', compact('user', 'userCategories', 'userSubcategoriesByCategory', 'reviews', 'isFavorited'));
    }

    public function getFavoriteProfessionalsPartial()
    {
        if (!auth()->check()) {
            return response('', 204);
        }

        $favoriteProfessionals = auth()->user()->favoriteProfessionals;

        if ($favoriteProfessionals->count() === 0) {
            return response('', 204);
        }

        return view('website.partials.favorite-professionals', compact('favoriteProfessionals'))->render();
    }

    /**
     * Generate a unique short URL for a user's profile
     */
    public function generateShortUrl(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,ids'
            ]);

            $userId = $request->user_id;
            $user = User::where("ids", $userId)->first();
            if ($user->short_url) {
                $fullShortUrl = url('/p/' . $user->short_url);
                return response()->json([
                    'success' => true,
                    'short_url' => $fullShortUrl,
                    'message' => 'Short URL retrieved successfully'
                ]);
            }
            $shortUrl = $this->generateUniqueShortUrl();
            $user->short_url = $shortUrl;
            $user->save();
            $fullShortUrl = url('/p/' . $shortUrl);
            return response()->json([
                'success' => true,
                'short_url' => $fullShortUrl,
                'message' => 'Short URL generated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate short URL. Please try again.'
            ], 500);
        }
    }

    private function generateUniqueShortUrl($length = 8)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $maxAttempts = 100;
        $attempts = 0;

        do {
            $shortUrl = '';
            for ($i = 0; $i < $length; $i++) {
                $shortUrl .= $characters[rand(0, strlen($characters) - 1)];
            }

            $attempts++;

            // Check if this short URL already exists
            $exists = User::where('short_url', $shortUrl)->exists();
        } while ($exists && $attempts < $maxAttempts);

        if ($attempts >= $maxAttempts) {
            return $this->generateUniqueShortUrl($length + 1);
        }

        return $shortUrl;
    }
    public function redirectShortUrl($shortUrl)
    {
        $user = User::where('short_url', $shortUrl)->first();
        if (!$user) {
            abort(404, 'Short URL not found');
        }
        return redirect()->route('professional_profile', $user->profile->slug);
    }
}
