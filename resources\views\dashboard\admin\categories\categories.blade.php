@extends('dashboard.layout.master')
@push('css')
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
@endpush
@section('content')
    <div id="kt_app_content" class="app-content flex-column-fluid customer_dashboard add-service">
        <div id="kt_app_content_container" class="app-container container padding-block">
            <div class="row row-gap-5">
                <div class="col-md-12 d-flex flex-wrap justify-content-between align-items-center">
                    <div>
                        <h6 class="sora black">Categories</h6>
                        <p class="fs-14 sora py-5 light-black m-0">Manage and organize all your categories and subcategories from here. </p>
                    </div>
                    @can('categories-create')
                        <a class="add-btn" id="add-category-button" data-bs-toggle="modal" data-bs-target="#add-category" style="display: none;">
                            <i class="fa-solid fa-plus me-3"></i> Add Category
                        </a>
                    @endcan
                    @can('subcategories-create')
                        <a class="add-btn" id="add-subcategory-button" data-bs-toggle="modal"
                            data-bs-target="#add-sub-category" style="display: none;">
                            <i class="fa-solid fa-plus me-3"></i> Add Sub Category
                        </a>
                    @endcan
                </div>
                <div class="col-lg-12">
                    <ul class="nav nav-pills mb-10" id="pills-tab" role="tablist">
                        @can('categories-list')
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active business-services" id="category-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-category" type="button" role="tab"
                                    aria-controls="pills-category" aria-selected="true">
                                    Main Category
                                </button>
                            </li>
                        @endcan
                        @can('subcategories-list')
                            <li class="nav-item" role="presentation">
                                <button class="nav-link business-services" id="subcategory-tab" data-bs-toggle="pill"
                                    data-bs-target="#pills-sub-category" type="button" role="tab"
                                    aria-controls="pills-group-service" aria-selected="false">
                                    Sub Category
                                </button>
                            </li>
                        @endcan
                    </ul>


                    <div class="tab-content" id="pills-tabContent">
                        <div class="tab-pane fade show active" id="pills-category" role="tabpanel"
                            aria-labelledby="category-tab" tabindex="0">
                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="categorySearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="categorySearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="categoryDatePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="categoryDatePicker" id="categoryDatePicker" class="datePicker ms-3 w-200px"
                                                placeholder="Select date range" readonly>
                                            <i class="fa fa-chevron-down down-arrow  ms-9"></i>
                                        </div>
                                    </label>
                                </div>
                                <table id="responsiveTable" class="display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>CATEGRORY NAME</th>
                                            <th>SUB CATEGRORY</th>
                                            <th>DATE</th>
                                            <th></th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($categories as $category)
                                            <tr>
                                                <td data-label="">
                                                    <div class="card  flex-row shadow-none p-0 gap-3">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ asset('website') . '/' . $category->image ?? '' }}"

                                                                alt="{{ $category->alt_tag ?? '' }}" />
                                                        </div>

                                                    </div>
                                                </td>
                                                <td data-label="CATEGRORY NAME">{{ $category->name ?? '' }}</td>
                                                <td data-label="SUB CATEGRORY">{{ $category->subcategories->count() ?? '' }}
                                                </td>
                                                <td data-label="DATE">{{ $category->created_at->format('d M, Y') ?? '' }}
                                                </td>
                                                <td data-label="">
                                                    <div class="toggle-container">
                                                        <label class="switch">
                                                            <!-- Dynamically set checked based on category status -->
                                                            <input type="checkbox" class="toggle-input category-toggle"
                                                                data-category-id="{{ $category->id }}"
                                                                {{ $category->status == 1 ? 'checked' : '' }}>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span
                                                            class="toggle-label">{{ $category->status == 1 ? 'Active' : 'Deactive' }}</span>
                                                    </div>
                                                </td>
                                                @can('subcategories-create')
                                                    <td data-label="">
                                                        <a class="purple-btn" data-bs-toggle="modal"
                                                            data-bs-target="#add-sub-category"
                                                            data-category-id="{{ $category->id }}">
                                                            <i class="fa-solid fa-plus me-3"></i> Add Sub Category
                                                        </a>
                                                    </td>
                                                @endcan
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <li>
                                                                <button
                                                                    class="dropdown-item complete fs-14 regular view-category"
                                                                    type="button" data-id="{{ $category->ids }}">
                                                                    <i class="bi bi-eye complete-icon"></i>
                                                                    View Detail
                                                                </button>
                                                            </li>
                                                            @can('categories-edit')
                                                                <li>
                                                                    <button
                                                                        class="dropdown-item complete fs-14 regular edit-category"
                                                                        type="button" data-id="{{ $category->ids }}">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Edit
                                                                    </button>
                                                                </li>
                                                            @endcan
                                                            @can('categories-delete')
                                                                <li>
                                                                    <form
                                                                        action="{{ route('categories.destroy', $category->ids) }}"
                                                                        method="POST" class="delete-form">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button"
                                                                            onclick="showDeleteConfirmation(this)">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Delete
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            @endcan
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="7" class="text-center py-5">
                                                    <div class="d-flex flex-column align-items-center">
                                                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                                        <h5 class="text-muted">No Categories Found</h5>
                                                        <p class="text-muted">Start by adding your first category</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-center" id="categoriesPagination"
                                    style="{{ $categories->hasPages() ? '' : 'display: none;' }}">
                                    @if ($categories->hasPages())
                                        {{ $categories->links('pagination::bootstrap-4') }}
                                    @endif
                                </div>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="pills-sub-category" role="tabpanel"
                            aria-labelledby="subcategory-tab" tabindex="1">

                            <div class="table-container">
                                <div class="table_top d-flex gap-3 align-items-center flex-wrap">
                                    <div class="search_box">
                                        <label for="subcategorySearchInput">
                                            <i class="fas fa-search"></i>
                                        </label>
                                        <input class="search_input search" type="text" id="subcategorySearchInput"
                                            placeholder="Search..." />
                                    </div>
                                    <!-- Date Picker -->
                                    <label for="subcategoryDatePicker" class="date_picker">
                                        <div class="date-picker-container">
                                            <i class="bi bi-calendar-event calender-icon"></i>
                                            <input type="text" name="subcategoryDatePicker" id="subcategoryDatePicker" class="datePicker ms-3 w-200px"
                                                placeholder="Select date range" readonly>
                                            <i class="fa fa-chevron-down down-arrow ms-9"></i>
                                        </div>
                                    </label>
                                </div>
                                <table id="responsiveTable" class="display w-100">
                                    <thead>
                                        <tr>
                                            <th></th>
                                            <th>SUB CATEGRORY NAME</th>
                                            <th>MAIN CATEGRORY</th>
                                            <th>DATE</th>
                                            <th></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @forelse ($subcategories as $subcategory)
                                            <tr>
                                                <td data-label="">
                                                    <div class="card  flex-row shadow-none p-0 gap-3">
                                                        <div class="card-header p-0 border-0 align-items-start">
                                                            <img src="{{ asset('website') . '/' . $subcategory->image ?? '' }}"

                                                                alt="{{ $subcategory->alt_tag ?? '' }}" />
                                                        </div>
                                                    </div>
                                                </td>
                                                <td data-label="SUB CATEGRORY NAME">{{ $subcategory->name ?? '' }}</td>
                                                <td data-label="MAIN CATEGRORY">
                                                    <p class="light-blue-badge">
                                                        {{ $subcategory->category->name ?? '' }}
                                                    </p>
                                                </td>
                                                <td data-label="DATE">
                                                    {{ $subcategory->created_at->format('d M, Y') ?? '' }}
                                                </td>
                                                <td data-label="">
                                                    <div class="toggle-container">
                                                        <label class="switch">
                                                            <!-- Dynamically set checked based on subcategory status -->
                                                            <input type="checkbox" class="toggle-input subcategory-toggle"
                                                                data-subcategory-id="{{ $subcategory->id }}"
                                                                {{ $subcategory->status == 1 ? 'checked' : '' }}>
                                                            <span class="slider"></span>
                                                        </label>
                                                        <span
                                                            class="toggle-label">{{ $subcategory->status == 1 ? 'Active' : 'Deactive' }}</span>
                                                    </div>
                                                </td>
                                                <td data-label="">
                                                    <div class="dropdown">
                                                        <button class="drop-btn" type="button" id="dropdownMenuButton"
                                                            data-bs-toggle="dropdown" aria-expanded="false">
                                                            <i class="bi bi-three-dots-vertical"></i>
                                                        </button>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                                            <li>
                                                                <button
                                                                    class="dropdown-item complete fs-14 regular view-subcategory"
                                                                    type="button" data-id="{{ $subcategory->ids }}">
                                                                    <i class="bi bi-eye complete-icon"></i>
                                                                    View Detail
                                                                </button>
                                                            </li>
                                                            @can('subcategories-edit')
                                                                <li>
                                                                    <button
                                                                        class="dropdown-item complete fs-14 regular edit-subcategory"
                                                                        type="button" data-id="{{ $subcategory->ids }}">
                                                                        <i class="bi bi-check-circle complete-icon"></i>
                                                                        Edit
                                                                    </button>
                                                                </li>
                                                            @endcan
                                                            @can('subcategories-delete')
                                                                <li>
                                                                    <form
                                                                        action="{{ route('subcategories.destroy', $subcategory->ids) }}"
                                                                        method="POST" class="delete-form">
                                                                        @csrf
                                                                        @method('DELETE')
                                                                        <button class="dropdown-item cancel fs-14 regular"
                                                                            type="button"
                                                                            onclick="showDeleteConfirmation(this)">
                                                                            <i class="fa-solid fa-xmark cancel-icon"></i>
                                                                            Delete
                                                                        </button>
                                                                    </form>
                                                                </li>
                                                            @endcan
                                                        </ul>
                                                    </div>
                                                </td>
                                            </tr>
                                        @empty
                                            <tr>
                                                <td colspan="7" class="text-center py-5">
                                                    <div class="d-flex flex-column align-items-center">
                                                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                                        <h5 class="text-muted">No Sub Categories Found</h5>
                                                        <p class="text-muted">Start by adding your first sub category</p>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforelse
                                    </tbody>
                                </table>
                                <div class="d-flex justify-content-center" id="subcategoriesPagination"
                                    style="{{ $subcategories->hasPages() ? '' : 'display: none;' }}">
                                    @if ($subcategories->hasPages())
                                        {{ $subcategories->links('pagination::bootstrap-4') }}
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @include('dashboard.admin.categories.modal.add-category-modal')
    @include('dashboard.admin.categories.modal.edit-category-modal')
    @include('dashboard.admin.categories.modal.add-sub-category-modal')
    @include('dashboard.admin.categories.modal.edit-sub-category-modal')
    @include('dashboard.admin.categories.modal.view-category-modal')
    @include('dashboard.admin.categories.modal.view-subcategory-modal')
@endsection
@push('js')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script>
        // Categories data for JavaScript lookup
        const categoriesData = {
            @foreach ($activeCategories as $category)
                {{ $category->id }}: "{{ $category->name }}",
            @endforeach
        };

        // Flag to track if category is pre-selected
        let categoryPreSelected = false;

        // Function to handle Add Sub Category button click to pre-select category
        function initializeAddSubCategoryButtons() {
            $('[data-bs-target="#add-sub-category"]').off('click').on('click', function() {
                var categoryId = $(this).data('category-id');
                console.log('Add Sub Category clicked, categoryId:', categoryId); // Debug log

                // Store the selection data for use in modal events
                $('#add-sub-category').data('preselected-category-id', categoryId);
                $('#add-sub-category').data('preselected-category-name', categoriesData[categoryId] || '');

                if (categoryId) {
                    categoryPreSelected = true;
                    console.log('Pre-selecting category:', categoriesData[categoryId]); // Debug log
                } else {
                    categoryPreSelected = false;
                    console.log('No category pre-selected'); // Debug log
                }
            });
        }

        // Dynamic Search with Debouncing - Best Practices Implementation
        class CategorySearch {
            constructor() {
                this.searchTimeout = null;
                this.debounceDelay = 300; // 300ms debounce delay
                this.currentRequest = null;
                this.dateRangePickers = {};
                this.init();
            }

            init() {
                // Initialize date range pickers first
                this.initializeDateRangePickers();

                // Initialize search for categories tab
                this.initializeSearch('#categorySearchInput', 'categories', '#pills-category tbody');

                // Initialize search for subcategories tab
                this.initializeSearch('#subcategorySearchInput', 'subcategories',
                    '#pills-sub-category tbody');

                // Initialize date picker search
                this.initializeDateSearch();
            }

            initializeDateRangePickers() {
                const self = this;

                console.log('Initializing date range pickers...');
                console.log('jQuery daterangepicker available:', typeof $.fn.daterangepicker !== 'undefined');

                // Initialize category date picker
                this.resetCategoryDatePicker();
                this.resetSubcategoryDatePicker();
            }

            resetCategoryDatePicker() {
                const self = this;
                const categoryDatePicker = $('#pills-category .datePicker');

                // Destroy existing picker if it exists
                if (categoryDatePicker.data('daterangepicker')) {
                    categoryDatePicker.data('daterangepicker').remove();
                }

                // Clear the input
                categoryDatePicker.val('');

                if (categoryDatePicker.length && typeof $.fn.daterangepicker !== 'undefined') {
                    categoryDatePicker.daterangepicker({
                        autoUpdateInput: false,
                        opens: 'center',
                        locale: {
                            format: 'MMM D, YYYY',
                            cancelLabel: 'Clear'
                        },
                        ranges: {
                            'Today': [moment(), moment()],
                            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                            'Last Week': [moment().subtract(6, 'days'), moment()],
                            'Last Month': [moment().subtract(29, 'days'), moment()],
                            'This Month': [moment().startOf('month'), moment().endOf('month')]
                        }
                    });

                    // Attach event handlers
                    this.attachCategoryDatePickerEvents();
                    this.dateRangePickers.categories = categoryDatePicker;
                } else if (categoryDatePicker.length) {
                    // Fallback: simple date input change handler
                    console.log('Using fallback date handler for categories');
                    categoryDatePicker.on('change', function() {
                        self.performSearch($('#categorySearchInput').val().trim(), 'categories',
                            '#pills-category tbody');
                    });
                }
            }

            resetSubcategoryDatePicker() {
                const self = this;
                const subcategoryDatePicker = $('#pills-sub-category .datePicker');

                // Destroy existing picker if it exists
                if (subcategoryDatePicker.data('daterangepicker')) {
                    subcategoryDatePicker.data('daterangepicker').remove();
                }

                // Clear the input
                subcategoryDatePicker.val('');

                if (subcategoryDatePicker.length && typeof $.fn.daterangepicker !== 'undefined') {
                    subcategoryDatePicker.daterangepicker({
                        autoUpdateInput: false,
                        opens: 'center',
                        locale: {
                            format: 'MMM D, YYYY',
                            cancelLabel: 'Clear'
                        },
                        ranges: {
                            'Today': [moment(), moment()],
                            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                            'Last Week': [moment().subtract(6, 'days'), moment()],
                            'Last Month': [moment().subtract(29, 'days'), moment()],
                            'This Month': [moment().startOf('month'), moment().endOf('month')]
                        }
                    });

                    // Attach event handlers
                    this.attachSubcategoryDatePickerEvents();
                    this.dateRangePickers.subcategories = subcategoryDatePicker;
                } else if (subcategoryDatePicker.length) {
                    // Fallback: simple date input change handler
                    console.log('Using fallback date handler for subcategories');
                    subcategoryDatePicker.on('change', function() {
                        self.performSearch($('#subcategorySearchInput').val().trim(), 'subcategories',
                            '#pills-sub-category tbody');
                    });
                }
            }

            attachCategoryDatePickerEvents() {
                const self = this;
                const categoryDatePicker = $('#pills-category .datePicker');

                categoryDatePicker.on('apply.daterangepicker', function(ev, picker) {
                    if (picker.startDate.isSame(picker.endDate, 'day')) {
                        $(this).val(picker.startDate.format('MMM D, YYYY'));
                    } else {
                        $(this).val(picker.startDate.format('MMM D, YYYY') + ' - ' + picker.endDate.format(
                            'MMM D, YYYY'));
                    }
                    console.log('Category date range applied:', $(this).val());
                    self.filterCategories('categories');
                });

                categoryDatePicker.on('cancel.daterangepicker', function(ev, picker) {
                    self.resetCategoryDatePicker();
                    self.filterCategories('categories');
                });

                categoryDatePicker.on('clear.daterangepicker', function(ev, picker) {
                    self.resetCategoryDatePicker();
                    self.filterCategories('categories');
                });
            }

            attachSubcategoryDatePickerEvents() {
                const self = this;
                const subcategoryDatePicker = $('#pills-sub-category .datePicker');

                subcategoryDatePicker.on('apply.daterangepicker', function(ev, picker) {
                    if (picker.startDate.isSame(picker.endDate, 'day')) {
                        $(this).val(picker.startDate.format('MMM D, YYYY'));
                    } else {
                        $(this).val(picker.startDate.format('MMM D, YYYY') + ' - ' + picker.endDate.format(
                            'MMM D, YYYY'));
                    }
                    console.log('Subcategory date range applied:', $(this).val());
                    self.filterCategories('subcategories');
                });

                subcategoryDatePicker.on('cancel.daterangepicker', function(ev, picker) {
                    self.resetSubcategoryDatePicker();
                    self.filterCategories('subcategories');
                });

                subcategoryDatePicker.on('clear.daterangepicker', function(ev, picker) {
                    self.resetSubcategoryDatePicker();
                    self.filterCategories('subcategories');
                });
            }

            initializeSearch(inputSelector, type, tableBodySelector) {
                const searchInput = $(inputSelector);
                if (searchInput.length === 0) return;

                const self = this;

                searchInput.on('input', function() {
                    const query = $(this).val().trim();

                    // Clear previous timeout
                    if (self.searchTimeout) {
                        clearTimeout(self.searchTimeout);
                    }

                    // Cancel previous request if still pending
                    if (self.currentRequest) {
                        self.currentRequest.abort();
                    }

                    // Show loading state
                    self.showLoadingState(tableBodySelector);

                    // Set new timeout for debounced search
                    self.searchTimeout = setTimeout(() => {
                        self.filterCategories(type);
                    }, self.debounceDelay);
                });

                // Clear search on escape key
                searchInput.on('keydown', function(e) {
                    if (e.key === 'Escape') {
                        $(this).val('');
                        self.filterCategories(type);
                    }
                });
            }

            initializeDateSearch() {
                // Initialize date picker functionality
                $('.datePicker').on('change', function() {
                    const activeTab = $('.nav-pills .nav-link.active').attr('id');
                    const type = activeTab === 'category-tab' ? 'categories' : 'subcategories';
                    const tableBodySelector = activeTab === 'category-tab' ? '#pills-category tbody' :
                        '#pills-sub-category tbody';
                    const searchInput = activeTab === 'category-tab' ? '#categorySearchInput' :
                        '#subcategorySearchInput';

                    const query = $(searchInput).val().trim();
                    this.performSearchWithDate(query, type, tableBodySelector);
                });
            }

            filterCategories(type) {
                // Get current search query and date values (like professional.index)
                let search = '';
                let date = '';

                if (type === 'categories') {
                    search = $('#categorySearchInput').val().trim();
                    date = $('#pills-category .datePicker').val().trim();
                } else {
                    search = $('#subcategorySearchInput').val().trim();
                    date = $('#pills-sub-category .datePicker').val().trim();
                }

                const tableBodySelector = type === 'categories' ? '#pills-category tbody' : '#pills-sub-category tbody';

                console.log('Filtering categories:', {
                    search,
                    date,
                    type
                });

                // Always perform search with current values (even if empty)
                this.performSearch(search, type, tableBodySelector);
            }

            performSearch(query, type, tableBodySelector) {
                const self = this;

                console.log('Performing search:', {
                    query,
                    type,
                    tableBodySelector
                });

                // Prepare search data
                const searchData = {
                    query: query,
                    type: type
                };

                // Add date filter if selected
                const dateInput = type === 'categories' ? '#pills-category .datePicker' :
                    '#pills-sub-category .datePicker';
                const dateValue = $(dateInput).val();
                console.log('Date value:', dateValue);

                if (dateValue) {
                    // Parse date range if using date range picker
                    const dates = this.parseDateRange(dateValue);
                    console.log('Parsed dates:', dates);
                    if (dates) {
                        searchData.date_from = dates.from;
                        searchData.date_to = dates.to;
                    }
                }

                console.log('Search data:', searchData);

                // Show loading indicator
                this.showLoadingState(tableBodySelector);

                // Perform AJAX search
                this.currentRequest = $.ajax({
                    url: '{{ route('categories.search') }}',
                    method: 'GET',
                    data: searchData,
                    beforeSend: function() {
                        console.log('Sending search request...');
                        // Add loading class to search input
                        const inputSelector = type === 'categories' ? '#categorySearchInput' :
                            '#subcategorySearchInput';
                        $(inputSelector).addClass('searching');
                    },
                    success: function(response) {
                        console.log('Search response:', response);
                        if (response.success) {
                            // Update table content
                            $(tableBodySelector).html(response.html);

                            // Update search results counter
                            self.updateSearchCounter(response.count, type);

                            // Reinitialize any dynamic elements (toggles, modals, etc.)
                            self.reinitializeDynamicElements();

                            // Show success feedback and handle empty results
                            if (response.count === 0) {
                                if (query || searchData.date_from || searchData.date_to) {
                                    // Show empty state for search with criteria
                                    self.showEmptyState(tableBodySelector, 'No results found');
                                    self.showSearchFeedback('No results found for your search criteria',
                                        'warning');
                                }
                            } else if (query || searchData.date_from || searchData.date_to) {
                                self.showSearchFeedback(`Found ${response.count} result(s)`, 'success');
                            }
                        } else {
                            console.error('Search failed:', response);
                            self.showSearchFeedback('Search failed. Please try again.', 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        if (status !== 'abort') { // Don't show error for aborted requests
                            console.error('Search error:', {
                                xhr,
                                status,
                                error
                            });
                            console.error('Response text:', xhr.responseText);
                            self.showSearchFeedback('Search failed. Please check your connection.',
                                'error');

                            // Show empty state
                            self.showEmptyState(tableBodySelector, 'Search failed');
                        }
                    },
                    complete: function() {
                        // Remove loading states
                        const inputSelector = type === 'categories' ? '#categorySearchInput' :
                            '#subcategorySearchInput';
                        $(inputSelector).removeClass('searching');
                        self.hideLoadingState(tableBodySelector);
                        self.currentRequest = null;
                    }
                });
            }

            showLoadingState(tableBodySelector) {
                const loadingHtml = `
                    <tr class="loading-row">
                        <td colspan="7" class="text-center py-4">
                            <div class="d-flex justify-content-center align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span>Searching...</span>
                            </div>
                        </td>
                    </tr>
                `;
                $(tableBodySelector).html(loadingHtml);
            }

            hideLoadingState(tableBodySelector) {
                $(tableBodySelector).find('.loading-row').remove();
            }

            showEmptyState(tableBodySelector, message = 'No results found') {
                const emptyHtml = `
                    <tr>
                        <td colspan="7" class="text-center py-4">
                            <div class="d-flex flex-column align-items-center">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">${message}</h5>
                                <p class="text-muted">Try adjusting your search criteria</p>
                            </div>
                        </td>
                    </tr>
                `;
                $(tableBodySelector).html(emptyHtml);
            }

            updateSearchCounter(count, type) {
                // Show search results count in toast notification (top right)
                if (count !== undefined) {
                    console.log(`Search completed: ${count} ${type} found`);
                }
            }

            showSearchFeedback(message, type) {
                // Create toast notification for search feedback
                const toastClass = type === 'success' ? 'bg-success' : 'bg-danger';
                const toast = `
                    <div class="toast align-items-center text-white ${toastClass} border-0 position-fixed top-0 end-0 m-3" role="alert" style="z-index: 9999;">
                        <div class="d-flex">
                            <div class="toast-body">${message}</div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                        </div>
                    </div>
                `;

                $('body').append(toast);
                const toastElement = $('.toast').last();
                const bsToast = new bootstrap.Toast(toastElement[0], {
                    delay: 3000
                });
                bsToast.show();

                // Remove toast element after it's hidden
                toastElement.on('hidden.bs.toast', function() {
                    $(this).remove();
                });
            }

            parseDateRange(dateString) {
                if (!dateString) return null;

                try {
                    // Parse date range: "MMM D, YYYY - MMM D, YYYY" or single date "MMM D, YYYY"
                    const parts = dateString.split(' - ');

                    if (parts.length === 2) {
                        // Date range
                        const fromDate = moment(parts[0], 'MMM D, YYYY');
                        const toDate = moment(parts[1], 'MMM D, YYYY');

                        if (fromDate.isValid() && toDate.isValid()) {
                            return {
                                from: fromDate.format('YYYY-MM-DD'),
                                to: toDate.format('YYYY-MM-DD')
                            };
                        }
                    } else if (parts.length === 1) {
                        // Single date
                        const singleDate = moment(parts[0], 'MMM D, YYYY');

                        if (singleDate.isValid()) {
                            return {
                                from: singleDate.format('YYYY-MM-DD'),
                                to: singleDate.format('YYYY-MM-DD')
                            };
                        }
                    }
                } catch (error) {
                    console.error('Error parsing date range:', error);
                }

                return null;
            }

            reinitializeDynamicElements() {
                // Reinitialize any dynamic elements that were replaced
                // Status toggles
                this.initializeStatusToggles();

                // Modal triggers
                this.initializeModalTriggers();

                // Dropdown menus
                this.initializeDropdowns();
            }

            initializeStatusToggles() {
                // Reinitialize category status toggles
                $('.category-toggle').off('change').on('change', function() {
                    const categoryId = $(this).data('category-id');
                    const status = $(this).is(':checked') ? 1 : 0;

                    $.ajax({
                        url: '{{ route('categories.update-status') }}',
                        method: 'POST',
                        data: {
                            category_id: categoryId,
                            status: status,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.success) {
                                // Update toggle label
                                const label = status ? 'Active' : 'Deactive';
                                $(this).siblings('.toggle-label').text(label);
                            }
                        }.bind(this)
                    });
                });

                // Reinitialize subcategory status toggles
                $('.subcategory-status-toggle').off('change').on('change', function() {
                    const subcategoryId = $(this).data('subcategory-id');
                    const status = $(this).is(':checked') ? 1 : 0;

                    $.ajax({
                        url: '{{ route('subcategories.update-status') }}',
                        method: 'POST',
                        data: {
                            subcategory_id: subcategoryId,
                            status: status,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            // Handle response
                        }
                    });
                });
            }

            initializeModalTriggers() {
                // Reinitialize edit category buttons
                $('.edit-category').off('click').on('click', function() {
                    const categoryId = $(this).data('id');
                    // Your existing edit category logic
                });

                // Reinitialize add subcategory buttons with proper category pre-selection logic
                initializeAddSubCategoryButtons();
            }

            initializeDropdowns() {
                // Reinitialize Bootstrap dropdowns
                $('.dropdown-toggle').dropdown();
            }
        }
        // Initialize search when document is ready
        $(document).ready(function() {
            // Wait a bit for all elements to be ready
            setTimeout(function() {
                console.log('Initializing CategorySearch...');
                try {
                    new CategorySearch();
                    console.log('CategorySearch initialized successfully');
                } catch (error) {
                    console.error('Error initializing CategorySearch:', error);
                }
            }, 500);

            // Handle down arrow click to open date pickers (like in professional.index)
            $('.date-picker-container .down-arrow').on('click', function() {
                var input = $(this).siblings('input.datePicker');
                if (input.data('daterangepicker')) {
                    input.data('daterangepicker').show();
                }
            });
        });
    </script>
    <script>
        $(document).ready(function() {

            // URL-based tab management
            function getTabFromUrl() {
                const urlParams = new URLSearchParams(window.location.search);
                const tab = urlParams.get('tab');
                return tab === 'subcategories' ? 'subcategory-tab' : 'category-tab';
            }

            function updateUrlWithTab(tabId) {
                const tab = tabId === 'subcategory-tab' ? 'subcategories' : 'categories';
                const url = new URL(window.location);
                url.searchParams.set('tab', tab);
                window.history.replaceState({}, '', url);
            }

            function setActiveTab(tabId) {
                $('#' + tabId).trigger('click');
                updateUrlWithTab(tabId);
            }

            // Function to show/hide appropriate add button
            function updateAddButton(tabId) {
                if (tabId === 'category-tab') {
                    $('#add-category-button').show();
                    $('#add-subcategory-button').hide();
                } else if (tabId === 'subcategory-tab') {
                    $('#add-category-button').hide();
                    $('#add-subcategory-button').show();
                }
            }

            // Set active tab on page load based on URL
            var activeTab = getTabFromUrl();
            $('#' + activeTab).trigger('click');
            updateAddButton(activeTab);

            // Update URL and button when tab is clicked
            $('.nav-link[data-bs-toggle="pill"]').on('shown.bs.tab', function(e) {
                var tabId = $(e.target).attr('id');
                updateUrlWithTab(tabId);
                updateAddButton(tabId);
            });

            // Handle browser back/forward navigation
            window.addEventListener('popstate', function(event) {
                var activeTab = getTabFromUrl();
                $('#' + activeTab).trigger('click');
                updateAddButton(activeTab);
            });
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true; // no file selected, let 'required' rule handle this
                }
                const fileSizeKB = element.files[0].size / 1024; // size in KB
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            $("#categoryForm").validate({
                errorClass: "error",
                errorElement: "label",
                errorPlacement: function(error, element) {
                    // Custom error placement for image input
                    if (element.attr('name') === 'avatar') {
                        // Remove any existing avatar errors first
                        $('.form-add-category').nextAll('.error').remove();
                        error.insertAfter('.form-add-category');
                    } else {
                        // Default error placement for other fields
                        error.insertAfter(element);
                    }
                },
                rules: {
                    avatar: {
                        required: true,
                        maxFileSize: 250
                    },
                    alt_tag: {
                        required: true,
                        maxlength: 100,
                    },
                    image_description: {
                        required: true,
                        maxlength: 100,
                    },
                    name: {
                        required: true,
                        maxlength: 100,
                    },
                    description: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image",
                        maxFileSize: "Image size must not exceed 250KB"
                    },
                    alt_tag: {
                        required: "Please enter alt tag",
                        maxlength: "Alt tag cannot exceed 100 characters",
                    },
                    image_description: {
                        required: "Please enter image description",
                        maxlength: "Image description cannot exceed 255 characters",
                    },
                    name: {
                        required: "Please enter category name",
                        maxlength: "Category name cannot exceed 100 characters",
                    },
                    description: {
                        required: "Please enter description"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });

            // Clear validation errors when image is uploaded (following the working pattern from individual.blade.php)
            $('input[name="avatar"]').on('change', function() {
                if (this.files && this.files.length > 0) {
                    $(this).removeClass('error');
                    $('.form-add-category').next('.error').remove();
                }
            });

            // Handle image removal via cross button
            $(document).on('click', '[data-kt-image-input-action="remove"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#categoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });

            // Handle image cancellation via cross button
            $(document).on('click', '[data-kt-image-input-action="cancel"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#categoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.edit-category').click(function() {
                var categoryId = $(this).data('id');
                $.ajax({
                    url: '/admin/categories/' + categoryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        $('#categoryId').val(data.ids);
                        $('#cat_name').val(data.name);
                        $('#cat_status').val(data.status);
                        $('#cat_image_description').val(data.image_description);
                        $('#cat_description').val(data.description);
                        $('#cat_alt_tag').val(data.alt_tag);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $(
                                '#edit-category .image-input[data-kt-image-input="true"]');
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-category').modal('show');
                    },
                });
            });

            $('.view-category').click(function() {
                var categoryId = $(this).data('id');
                $.ajax({
                    url: '/admin/categories/' + categoryId,
                    method: 'GET',
                    success: function(data) {
                        $('#view_cat_name').text(data.name || '-');
                        $('#view_cat_image_description').text(data.image_description || '-');
                        $('#view_cat_description').text(data.description || '-');
                        $('#view_cat_alt_tag').text(data.alt_tag || '-');

                        // Set status with badge styling
                        var statusText = data.status == 1 ? 'Active' : 'Inactive';
                        var statusClass = data.status == 1 ? 'bg-success' : 'bg-danger';
                        $('#view_cat_status_badge').text(statusText).removeClass(
                            'bg-success bg-danger').addClass(statusClass);
                        // Set image src
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            $('#view_cat_image').attr('src', imageUrl);
                        } else {
                            $('#view_cat_image').attr('src',
                                '/website/assets/images/placeholder.png');
                        }
                        $('#view-category').modal('show');
                    },
                    error: function(xhr) {
                        alert('Failed to load category details. Please try again.');
                    }
                });
            });

            // Add validation to edit category form
            $("#editCategoryForm").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    // Custom error placement for image input
                    if (element.attr('name') === 'avatar') {
                        // Remove any existing avatar errors first
                        $('.form-add-category').nextAll('.error').remove();
                        error.insertAfter('.form-add-category');
                    } else {
                        // Default error placement for other fields
                        error.insertAfter(element);
                    }
                },
                rules: {
                    avatar: {
                        maxFileSize: 250
                    },
                    alt_tag: {
                        required: true,
                        maxlength: 100,
                    },
                    image_description: {
                        required: true,
                        maxlength: 100,
                    },
                    name: {
                        required: true,
                        maxlength: 100,
                    },
                    description: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        maxFileSize: "Image size must not exceed 250KB"
                    },
                    alt_tag: {
                        required: "Please enter alt tag",
                        maxlength: "Alt tag cannot exceed 100 characters",
                    },
                    image_description: {
                        required: "Please enter image description",
                        maxlength: "Image description cannot exceed 255 characters",
                    },
                    name: {
                        required: "Please enter category name",
                        maxlength: "Category name cannot exceed 100 characters",
                    },
                    description: {
                        required: "Please enter description"
                    }
                },
                submitHandler: function(form) {
                    var categoryId = $('#categoryId').val();
                    var formData = new FormData(form);
                    $.ajax({
                        url: '/admin/categories/' + categoryId,
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-category').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    location.reload();
                                }
                            });
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                }
            });

            // Clear validation errors when image is uploaded for edit category form
            $('input[name="avatar"]').on('change', function() {
                if (this.files && this.files.length > 0) {
                    $(this).removeClass('error');
                    $('.form-add-category').next('.error').remove();
                }
            });

            // Handle image removal via cross button for edit category form
            $(document).on('click', '[data-kt-image-input-action="remove"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#editCategoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });

            // Handle image cancellation via cross button for edit category form
            $(document).on('click', '[data-kt-image-input-action="cancel"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#editCategoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });

        });
    </script>

    <script>
        $(document).ready(function() {
            $(".category-toggle").on('change', function() {
                var isChecked = $(this).prop('checked');
                var categoryId = $(this).data('category-id');
                var newStatus = isChecked ? 1 : 0;
                $.ajax({
                    url: "{{ route('categories.update-status') }}",
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        category_id: categoryId,
                        status: newStatus
                    },
                    success: function(response) {
                        $(this).next('.toggle-label').text(newStatus === 1 ? 'Active' :
                            'Deactive');
                        // alert('Category status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the category status');
                    }
                });
            });


            $(".subcategory-toggle").on('change', function() {
                var isChecked = $(this).prop('checked');
                var subcategoryId = $(this).data('subcategory-id');
                var newStatus = isChecked ? 1 : 0;
                $.ajax({
                    url: "{{ route('subcategories.update-status') }}",
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        subcategory_id: subcategoryId,
                        status: newStatus
                    },
                    success: function(response) {
                        $(this).next('.toggle-label').text(newStatus === 1 ? 'Active' :
                            'Deactive');
                        // alert('Subcategory status updated successfully');
                    },
                    error: function(xhr, status, error) {
                        alert('An error occurred while updating the subcategory status');
                    }
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $.validator.addMethod('maxFileSize', function(value, element, maxSizeKB) {
                if (element.files.length === 0) {
                    return true; // no file selected, let 'required' rule handle this
                }
                const fileSizeKB = element.files[0].size / 1024; // size in KB
                return fileSizeKB <= maxSizeKB;
            }, 'File size must be less than {0} KB.');

            $("#subCategoryForm").validate({
                errorClass: "error",
                errorElement: "label",
                errorPlacement: function(error, element) {
                    // Custom error placement for image input
                    if (element.attr('name') === 'avatar') {
                        // Remove any existing avatar errors first
                        $('.form-add-category').nextAll('.error').remove();
                        error.insertAfter('.form-add-category');
                    } else {
                        // Default error placement for other fields
                        error.insertAfter(element);
                    }
                },
                rules: {
                    avatar: {
                        required: true,
                        maxFileSize: 250
                    },
                    alt_tag: {
                        required: true,
                        maxlength: 100,
                    },
                    image_description: {
                        required: true,
                        maxlength: 100,
                    },
                    category_id: {
                        required: true
                    },
                    name: {
                        required: true,
                        maxlength: 100,
                    },
                    description: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        required: "Please upload an image",
                        maxFileSize: "Image size must not exceed 250KB"
                    },
                    alt_tag: {
                        required: "Please enter alt tag",
                        maxlength: "Alt tag cannot exceed 100 characters",
                    },
                    image_description: {
                        required: "Please enter image description",
                        maxlength: "Image description cannot exceed 255 characters",
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    name: {
                        required: "Please enter category name",
                        maxlength: "Category name cannot exceed 100 characters",
                    },
                    description: {
                        required: "Please enter description"
                    }
                },
                submitHandler: function(form) {
                    form.submit();
                },
            });

            // Clear validation errors when image is uploaded for subcategory form
            $('input[name="avatar"]').on('change', function() {
                if (this.files && this.files.length > 0) {
                    $(this).removeClass('error');
                    $('.form-add-category').next('.error').remove();
                }
            });

            // Handle image removal via cross button for subcategory form
            $(document).on('click', '[data-kt-image-input-action="remove"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#subCategoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });

            // Handle image cancellation via cross button for subcategory form
            $(document).on('click', '[data-kt-image-input-action="cancel"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#subCategoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            // Initialize on page load
            initializeAddSubCategoryButtons();

            // Handle category selection from dropdown
            $('#category_select').on('change', function() {
                var selectedCategoryId = $(this).val();
                $('#add_category_id').val(selectedCategoryId);

                // Clear category validation error immediately after selection
                if (selectedCategoryId && selectedCategoryId !== '') {
                    // Remove error class and clear validation error
                    $(this).removeClass('error');
                    $(this).nextAll('.error').remove();

                    // Re-validate the field to clear any existing errors
                    if ($("#subCategoryForm").data('validator')) {
                        $("#subCategoryForm").validate().element(this);
                    }
                }
            });

            // Reset form when modal is closed
            $('#add-sub-category').on('hidden.bs.modal', function() {
                var modal = $(this);

                // Reset form completely
                $('#subCategoryForm')[0].reset();

                // Clear all validation errors
                if ($("#subCategoryForm").data('validator')) {
                    $("#subCategoryForm").validate().resetForm();
                }

                // Reset category selection
                $('#add_category_name').val('').hide();
                $('#add_category_id').val('');
                $('#category_select').val('').trigger('change').show();

                // Show Select2 dropdown properly
                $('#category_select').closest('.col-md-12').find('.select2-container').show();

                // Reset the pre-selection flag and data attributes
                categoryPreSelected = false;
                modal.removeData('preselected-category-id');
                modal.removeData('preselected-category-name');

                // Reset image input completely
                var imageInput = $('#add-sub-category .image-input[data-kt-image-input="true"]');
                var wrapper = imageInput.find('.image-input-wrapper');
                wrapper.css('background-image', '');
                imageInput.removeClass('image-input-changed').addClass('image-input-empty');
                imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');

                // Clear any file input
                $('#add-sub-category input[name="avatar"]').val('');

                // Reset all form fields to default state
                $('#subCategoryForm input[type="text"], #subCategoryForm textarea').val('');
                $('#subCategoryForm select').val('').trigger('change');
            });

            $('.edit-subcategory').click(function() {
                var subCategoryId = $(this).data('id');
                $.ajax({
                    url: '/admin/subcategories/' + subCategoryId + '/edit',
                    method: 'GET',
                    success: function(data) {
                        console.log('Edit subcategory data:', data); // Debug log
                        $('#subCategoryId').val(data.ids);
                        $('#sub_name').val(data.name);
                        $('#sub_status').val(data.status);
                        $('#sub_image_description').val(data.image_description);
                        $('#sub_alt_tag').val(data.alt_tag);
                        $('#sub_category_name').val(data.category_name);
                        $('#sub_description').val(data.description);
                        $('#hiddenCategoryId').val(data.category_id);

                        // Initialize Select2 and set the category select dropdown
                        $('#edit_category_select').select2({
                            placeholder: 'Select Category',
                            allowClear: true
                        });

                        // Set the category select dropdown with a small delay to ensure Select2 is ready
                        setTimeout(function() {
                            console.log('Setting category ID:', data.category_id); // Debug log
                            $('#edit_category_select').val(data.category_id).trigger('change');
                        }, 100);
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            var imageInput = $(
                                '#edit-sub-category .image-input[data-kt-image-input="true"]'
                            );
                            var wrapper = imageInput.find('.image-input-wrapper');
                            wrapper.css('background-image', 'url(' + imageUrl + ')');
                            imageInput.removeClass('image-input-empty').addClass(
                                'image-input-changed');
                            imageInput.find('[data-kt-image-input-action="remove"]')
                                .removeClass('d-none');
                            imageInput.find('[data-kt-image-input-action="cancel"]')
                                .removeClass('d-none');
                        } else {
                            var imageInput = $('.image-input[data-kt-image-input="true"]');
                            imageInput.addClass('image-input-empty').removeClass(
                                'image-input-changed');
                            imageInput.find('.image-input-wrapper').css('background-image',
                                'none');
                        }
                        $('#edit-sub-category').modal('show');
                    },
                });
            });

            // Add validation to edit subcategory form
            $("#editSubCategoryForm").validate({
                errorClass: "error",
                errorElement: "span",
                errorPlacement: function(error, element) {
                    // Custom error placement for image input
                    if (element.attr('name') === 'avatar') {
                        // Remove any existing avatar errors first
                        $('.form-add-category').nextAll('.error').remove();
                        error.insertAfter('.form-add-category');
                    } else if (element.attr('name') === 'category_id') {
                        // Custom error placement for category select
                        error.insertAfter('#edit_category_select');
                    } else {
                        // Default error placement for other fields
                        error.insertAfter(element);
                    }
                },
                rules: {
                    avatar: {
                        maxFileSize: 250
                    },
                    alt_tag: {
                        required: true,
                        maxlength: 100,
                    },
                    image_description: {
                        required: true,
                        maxlength: 100,
                    },
                    category_id: {
                        required: true
                    },
                    name: {
                        required: true,
                        maxlength: 100,
                    },
                    description: {
                        required: true
                    }
                },
                messages: {
                    avatar: {
                        maxFileSize: "Image size must not exceed 250KB"
                    },
                    alt_tag: {
                        required: "Please enter alt tag",
                        maxlength: "Alt tag cannot exceed 100 characters",
                    },
                    image_description: {
                        required: "Please enter image description"
                    },
                    category_id: {
                        required: "Please select category"
                    },
                    name: {
                        required: "Please enter category name"
                    },
                    description: {
                        required: "Please enter description"
                    }
                },
                submitHandler: function(form) {
                    var subCategoryId = $('#subCategoryId').val();
                    var formData = new FormData(form);
                    formData.append('_method', 'PATCH'); // Ensure PATCH method is sent

                    // Debug: Log form data
                    console.log('Submitting subcategory update:', {
                        subCategoryId: subCategoryId,
                        category_id: $('#hiddenCategoryId').val(),
                        name: $('#sub_name').val()
                    });

                    $.ajax({
                        url: '/admin/subcategories/' + subCategoryId,
                        type: 'POST', // Use POST but with _method=PATCH
                        data: formData,
                        processData: false,
                        contentType: false,
                        success: function(response) {
                            $('#edit-sub-category').modal('hide');
                            Swal.fire({
                                icon: response.type,
                                title: response.title,
                                text: response.message
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    location.reload();
                                }
                            });
                        },
                        error: function(xhr) {
                            alert('Update failed. Please try again.');
                        }
                    });
                }
            });

            // Handle category selection change in edit subcategory modal
            $('#edit_category_select').on('change', function() {
                var selectedCategoryId = $(this).val();
                $('#hiddenCategoryId').val(selectedCategoryId);

                // Clear category validation error immediately after selection
                if (selectedCategoryId && selectedCategoryId !== '') {
                    // Remove error class and clear validation error
                    $(this).removeClass('error');
                    $(this).nextAll('.error').remove();

                    // Re-validate the field to clear any existing errors
                    if ($("#editSubCategoryForm").data('validator')) {
                        $("#editSubCategoryForm").validate().element(this);
                    }
                }
            });

            // Clear validation errors when image is uploaded for edit subcategory form
            $('input[name="avatar"]').on('change', function() {
                if (this.files && this.files.length > 0) {
                    $(this).removeClass('error');
                    $('.form-add-category').next('.error').remove();
                }
            });

            // Handle image removal via cross button for edit subcategory form
            $(document).on('click', '[data-kt-image-input-action="remove"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#editSubCategoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });

            // Handle image cancellation via cross button for edit subcategory form
            $(document).on('click', '[data-kt-image-input-action="cancel"]', function() {
                // Clear the file input
                $('input[name="avatar"]').val('');

                // Validate the field to show error immediately
                setTimeout(function() {
                    $('#editSubCategoryForm').validate().element('input[name="avatar"]');
                }, 200);
            });

            // Reset add category modal when opened
            $('#add-category').on('show.bs.modal', function() {
                // Reset form
                $('#categoryForm')[0].reset();

                // Clear any existing validation errors
                if ($("#categoryForm").data('validator')) {
                    $("#categoryForm").validate().resetForm();
                }

                // Reset image input
                var imageInput = $('#add-category .image-input[data-kt-image-input="true"]');
                var wrapper = imageInput.find('.image-input-wrapper');
                wrapper.css('background-image', '');
                imageInput.removeClass('image-input-changed').addClass('image-input-empty');
            });

            // Reinitialize validation when add category modal is fully shown
            $('#add-category').on('shown.bs.modal', function() {
                // Ensure validation is working
                if (!$("#categoryForm").data('validator')) {
                    $("#categoryForm").validate({
                        errorClass: "error",
                        errorElement: "span",
                        errorPlacement: function(error, element) {
                            error.insertAfter(element);
                        },
                        rules: {
                            avatar: {
                                required: true,
                                maxFileSize: 250
                            },
                            alt_tag: {
                                required: true,
                                maxlength: 100,
                            },
                            image_description: {
                                required: true,
                                maxlength: 100,
                            },
                            name: {
                                required: true,
                                maxlength: 100,
                            },
                            description: {
                                required: true
                            }
                        },
                        messages: {
                            avatar: {
                                required: "Please upload an image",
                                maxFileSize: "Image size must not exceed 250KB"
                            },
                            alt_tag: {
                                required: "Please enter alt tag",
                                maxlength: "Alt tag cannot exceed 100 characters",
                            },
                            image_description: {
                                required: "Please enter image description",
                                maxlength: "Image description cannot exceed 255 characters",
                            },
                            name: {
                                required: "Please enter category name",
                                maxlength: "Category name cannot exceed 100 characters",
                            },
                            description: {
                                required: "Please enter description"
                            }
                        },
                        submitHandler: function(form) {
                            form.submit();
                        }
                    });
                }
            });

            // Reset edit category modal validation when opened
            $('#edit-category').on('show.bs.modal', function() {
                // Clear any existing validation errors
                $("#editCategoryForm").validate().resetForm();
            });

            // Reset edit subcategory modal validation when opened
            $('#edit-sub-category').on('show.bs.modal', function() {
                // Clear any existing validation errors
                $("#editSubCategoryForm").validate().resetForm();
            });

            // Reset edit subcategory modal when closed
            $('#edit-sub-category').on('hidden.bs.modal', function() {
                // Reset category select dropdown when modal is closed
                $('#edit_category_select').val('').trigger('change');
            });

            // Handle add sub category modal when opened
            $('#add-sub-category').on('show.bs.modal', function() {
                var modal = $(this);
                var preselectedCategoryId = modal.data('preselected-category-id');
                var preselectedCategoryName = modal.data('preselected-category-name');

                console.log('Modal opening, preselected ID:', preselectedCategoryId, 'Name:', preselectedCategoryName);

                // Reset form completely first
                $('#subCategoryForm')[0].reset();

                // Clear all validation errors
                if ($("#subCategoryForm").data('validator')) {
                    $("#subCategoryForm").validate().resetForm();
                }

                // Always reset image input
                var imageInput = $('#add-sub-category .image-input[data-kt-image-input="true"]');
                var wrapper = imageInput.find('.image-input-wrapper');
                wrapper.css('background-image', '');
                imageInput.removeClass('image-input-changed').addClass('image-input-empty');
                imageInput.find('[data-kt-image-input-action="remove"]').addClass('d-none');
                imageInput.find('[data-kt-image-input-action="cancel"]').addClass('d-none');

                // Clear any file input
                $('#add-sub-category input[name="avatar"]').val('');
            });

            // Handle the modal after it's fully shown (to ensure Select2 is initialized)
            $('#add-sub-category').on('shown.bs.modal', function() {
                var modal = $(this);
                var preselectedCategoryId = modal.data('preselected-category-id');
                var preselectedCategoryName = modal.data('preselected-category-name');

                console.log('Modal shown - preselected ID:', preselectedCategoryId, 'Name:', preselectedCategoryName, 'Flag:', categoryPreSelected);

                if (preselectedCategoryId && categoryPreSelected) {
                    // Category is pre-selected: show text field, hide select dropdown
                    console.log('Setting up pre-selected category - hiding select, showing text');
                    $('#add_category_name').val(preselectedCategoryName).show();
                    $('#add_category_id').val(preselectedCategoryId);

                    // Hide Select2 dropdown properly
                    var select2Container = $('#category_select').closest('.col-md-12').find('.select2-container');
                    console.log('Select2 container found:', select2Container.length);
                    select2Container.hide();
                    $('#category_select').hide();

                    console.log('Text field visible:', $('#add_category_name').is(':visible'));
                    console.log('Select dropdown visible:', $('#category_select').is(':visible'));
                    console.log('Select2 container visible:', select2Container.is(':visible'));
                } else {
                    // No pre-selection: show select dropdown, hide text field
                    console.log('Setting up category select dropdown - showing select, hiding text');
                    $('#add_category_name').val('').hide();
                    $('#add_category_id').val('');
                    $('#category_select').val('').trigger('change');

                    // Show Select2 dropdown properly
                    var select2Container = $('#category_select').closest('.col-md-12').find('.select2-container');
                    select2Container.show();
                    $('#category_select').show();

                    console.log('Text field visible:', $('#add_category_name').is(':visible'));
                    console.log('Select dropdown visible:', $('#category_select').is(':visible'));
                    console.log('Select2 container visible:', select2Container.is(':visible'));
                }
            });

            // View subcategory functionality
            $('.view-subcategory').click(function() {
                var subcategoryId = $(this).data('id');
                $.ajax({
                    url: '/admin/subcategories/' + subcategoryId,
                    method: 'GET',
                    success: function(data) {
                        $('#view_subcat_name').text(data.name || '-');
                        $('#view_subcat_image_description').text(data.image_description || '-');
                        $('#view_subcat_description').text(data.description || '-');
                        $('#view_subcat_alt_tag').text(data.alt_tag || '-');
                        $('#view_subcat_category_name').text(data.category_name || '-');

                        // Set status with badge styling
                        var statusText = data.status == 1 ? 'Active' : 'Inactive';
                        var statusClass = data.status == 1 ? 'bg-success' : 'bg-danger';
                        $('#view_subcat_status_badge').text(statusText).removeClass(
                            'bg-success bg-danger').addClass(statusClass);

                        // Set image src
                        if (data.image) {
                            var baseImageUrl = $('meta[name="asset-url"]').attr('content') ||
                                '/website';
                            var imageUrl = baseImageUrl + '/' + data.image;
                            $('#view_subcat_image').attr('src', imageUrl);
                        } else {
                            $('#view_subcat_image').attr('src',
                                '/website/assets/images/placeholder.png');
                        }

                        $('#view-subcategory').modal('show');
                    },
                    error: function(xhr) {
                        alert('Failed to load subcategory details. Please try again.');
                    }
                });
            });

        });
    </script>
@endpush
