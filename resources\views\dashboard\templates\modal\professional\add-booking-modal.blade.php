
<div class="modal fade add-services-modal service-details-modal" id="service-details" tabindex="-1"
    aria-labelledby="serviceDetails" aria-hidden="true">
    <div id="loader"
        style="
    display: none;
    width: 100%; height: 100%;
    background: rgba(255, 255, 255, 0.8);
    z-index: 9999;
    text-align: center;
    padding-top: 20%;
    font-size: 20px;">
        Loading...
    </div>
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header p-5">
                <p class="modal-title fs-15 sora black semi_bold" id="filtersModalLabel">Add Booking</p>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="business-data">

                </div>
                <div id="service-detail-modal-body">
                    <div class="mb-5">
                        <div class="col-md-12">
                            <label for="clientName" class="form-label form-input-labels">Client Name</label>
                            <input type="text" class="form-control form-inputs-field" name="clientName"
                                id="clientName" placeholder="Enter client name">
                        </div>
                        <div class="col-md-12">
                            <label for="clientEmail" class="form-label form-input-labels">Email</label>
                            <input type="email" class="form-control form-inputs-field" name="clientEmail"
                                id="clientEmail" placeholder="Enter client email">
                        </div>
                        <div class="col-md-12 mb-6">
                            <label for="phone" class="form-label form-input-label">Phone Number</label>
                            <div class="form-control form-inputs-field">
                                <input type="tel" id="phone" name="phone" placeholder="Enter phone number">
                            </div>
                        </div>
                        <div class="col-md-12 mb-8">
                            <label for="professionalService" class="form-label form-input-labels">Select Service</label>
                            <select class="form-select form-select-field" id="professionalService" name="service"
                                data-placeholder="Select">
                                <option selected disabled>Select Service</option>
                                @foreach (auth()->user()->services as $index => $service)
                                    <option value="{{ $service->ids }}">
                                        {{ $service->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="cancel-btn" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="save-btn" id="add-to-cart-service">Add Booking</button>
            </div>
        </div>
    </div>
</div>
@push('js')
    <script
        src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.api_key') }}&libraries=places&v=weekly"
        async defer></script>

    <script>
        // Show loader only in modal during AJAX calls
        $(document).ajaxStart(function() {
            // Only show loader if the modal is open
            if ($('#add-booking-modal').hasClass('show')) {
                $('#add-booking-modal .modal-content').append(
                    '<div id="modal-loader" class="modal-loader-overlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>'
                    );
            }
        });

        // Hide modal loader when AJAX calls finish
        $(document).ajaxStop(function() {
            $('#modal-loader').remove();
        });
        $(document).on('change', '#professionalService', function() {
            var serviceId = $(this).val(); // Get the selected value of the service input field

            if (serviceId && serviceId !== '') {
                // Show professional and date sections when service is selected
                $('#professional-selection').show();
                $('.calendar-container').show();

                // Get the currently selected date or use today's date
                var initialSelectedDate = $('#dateGrid .date-item.selected').data('date');
                var newformattedDate = null;

                if (initialSelectedDate) {
                    // Parse the date string properly to avoid timezone issues
                    var parts = initialSelectedDate.split(' ');
                    var monthMap = {
                        Jan: '01', Feb: '02', Mar: '03', Apr: '04', May: '05', Jun: '06',
                        Jul: '07', Aug: '08', Sep: '09', Oct: '10', Nov: '11', Dec: '12'
                    };
                    var day = parts[2].padStart(2, '0');
                    var month = monthMap[parts[1]];
                    var year = parts[3];
                    newformattedDate = `${year}-${month}-${day}`;
                } else {
                    // Use today's date if no date is selected
                    newformattedDate = new Date().toISOString().split('T')[0];
                }

                updateTimeSlotsForDate(serviceId, newformattedDate);
                console.log('Service selected:', serviceId);
            } else {
                // Hide professional and date sections when no service is selected
                $('#professional-selection').hide();
                $('.calendar-container').hide();

                console.log('No service selected');
            }

        });
        $(document).ready(function() {
            function updateProfessionals(professionals) {
                var professionalSelect = $('#professional-selection');
                professionalSelect.empty(); // Clear the existing list

                // Loop through the professionals and populate the list
                professionals.forEach(function(professional) {
                    professionalSelect.append(`
                    <div class="col-md-4">
                        <label class="category-checkbox professional-option" data-professional-id="${professional.id}" data-professional-name="${professional.name}">
                            <input type="checkbox" name="professionals[]" value="${professional.id}" class="professional-checkbox-input">
                            <div class="d-flex flex-column align-items-center">
                                <img src="${professional.image}" class="h-90px w-90px rounded-pill top-rated-image" alt="professional-image">
                                <p class="professional-name fs-16 semi_bold black text-center m-0">${professional.name}</p>
                                <p class="professional-email fs-12 regular light-black opacity-6 text-center m-0">${professional.email}</p>
                            </div>
                        </label>
                    </div>
                `);
                });
            }
            // Initialize booking date if there's a pre-selected date
            const preSelectedDate = $('.date-item.selected').data('date');
            if (preSelectedDate) {
                // Format and set the pre-selected date
                const parts = preSelectedDate.split(' ');
                const monthMap = {
                    Jan: '01',
                    Feb: '02',
                    Mar: '03',
                    Apr: '04',
                    May: '05',
                    Jun: '06',
                    Jul: '07',
                    Aug: '08',
                    Sep: '09',
                    Oct: '10',
                    Nov: '11',
                    Dec: '12'
                };
                const day = parts[2].padStart(2, '0');
                const month = monthMap[parts[1]];
                const year = parts[3];
                const formattedDate = `${year}-${month}-${day}`;
            }
            $('input[name="loc"]').on('change', function() {
                var selectedValue = $('input[name="loc"]:checked').val();

                if (selectedValue === "Providers location") {
                    $('#providers-loc').show();
                    $('#home-loc').hide();
                } else if (selectedValue === "Your Home/Office") {
                    $('#providers-loc').hide();
                    $('#home-loc').show();
                }
            });
            $(document).on('click', ".family_category", function(e) {
                let type = $(this).val();
                $("#service_availability").html('');
                if (type == "family") {
                    // Show loading in service availability section
                    $("#service_availability").html(
                        '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>'
                        );

                    $.ajax({
                        type: "GET",
                        url: "{{ url('get-family-details') }}/" + type,
                        data: "data",
                        success: function(response) {
                            if (response.status == true) {
                                $("#service_availability").html(response.data);
                            }
                        },
                        error: function() {
                            $("#service_availability").html(
                                '<div class="text-center py-4 text-danger">Error loading family details</div>'
                                );
                        }
                    });
                }
            });

            // Handle professional checkbox selection
            $(document).on('change', '.professional-checkbox-input', function() {
                updateSelectedProfessionals();
            });

            function updateSelectedProfessionals() {
                let selectedProfessionals = [];
                let selectedContainer = $('#selected-professionals');
                let selectedList = $('#selected-professionals-list');

                // Clear previous selections
                selectedList.empty();

                // Get all checked professionals
                $('input[name="professionals[]"]:checked').each(function() {
                    let professionalName = $(this).closest('.professional-option').data(
                        'professional-name');
                    selectedProfessionals.push(professionalName);

                    // Add to selected list
                    selectedList.append(`
                                                        <span class="badge bg-primary me-2 mb-2">
                                                            ${professionalName}
                                                            <button type="button" class="btn-close btn-close-white ms-2"
                                                                    data-professional-id="${$(this).val()}"
                                                                    onclick="removeProfessional(this)"></button>
                                                        </span>
                                                    `);
                });

                // Show/hide selected professionals section
                if (selectedProfessionals.length > 0) {
                    selectedContainer.show();
                } else {
                    selectedContainer.hide();
                }
            }

            // Function to remove professional
            window.removeProfessional = function(button) {
                let professionalId = $(button).data('professional-id');
                $(`input[name="professionals[]"][value="${professionalId}"]`).prop('checked', false);
                updateSelectedProfessionals();
            };
        });

        function updateBookingDateInput(dateObj = null, time = null) {
            console.log('updateBookingDateInput called with:', dateObj, time); // Debug log
            const date = new Date(dateObj);
            let formattedDate = date.toLocaleDateString('en-CA'); // "2025-07-23"
            $('input[name="booking_date"]').val(formattedDate); // Set the date input
            $('input[name="booking_time"]').val(time); // Set the time input
            console.log('Updated inputs - Date:', formattedDate, 'Time:', time); // Debug log
            return true;
        }
        window.updateBookingDateInput = updateBookingDateInput;

        // Debug function to check time slots
        window.debugTimeSlots = function() {
            console.log('Time slots in DOM:', $('#timeGrid .time-item').length);
            $('#timeGrid .time-item').each(function(index) {
                console.log(`Slot ${index}:`, {
                    text: $(this).text(),
                    dataTime: $(this).data('time'),
                    classes: this.className,
                    disabled: $(this).hasClass('disabled')
                });
            });
        };

        // Function to update only time slots for selected date (without reloading modal)
        function updateTimeSlotsForDate(service_id, date) {
            let formattedDate = date ? date : new Date().toISOString().split('T')[0];
            console.log('updateTimeSlotsForDate called with service_id:', service_id, 'formattedDate:', formattedDate);
            const url = "{{ url('get-service-time-slots') }}/" + service_id + "?date=" + formattedDate;

            $.get(url, function(response) {

                if (response.status) {
                    // Update only the time slots without reloading the entire modal
                    updateTimeSlotDisplay(response.slots, formattedDate, response.bookedSlots);
                } else {
                    console.warn('Failed to fetch time slots:', response.message);
                    // Fallback: clear time slots if none available
                    $('#timeGrid').html('<div class="no-time-slots">No available time slots for this date</div>');
                }
            }).fail(function() {
                console.error('Error fetching time slots');

                // Show modal asking user to select a service first
                Swal.fire({
                    text: "Please select a service first",
                    icon: "warning",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });

                $('#timeGrid').html('<div class="no-time-slots">Please select a service first</div>');
            });
        }
        // Function to update time slot display
        function updateTimeSlotDisplay(slots, date, bookedSlots = []) {
            // Convert booked slots from HH:MM:SS to HH:MM format to match generated slots
            const normalizedBookedSlots = bookedSlots.map(slot => {
                if (slot.includes(':')) {
                    return slot.substring(0, 5); // Take only HH:MM part
                }
                return slot;
            });

            const timeGrid = document.getElementById("timeGrid");
            if (!timeGrid) return;

            timeGrid.innerHTML = ""; // Clear previous slots

            if (!slots || slots.length === 0) {
                const message = document.createElement("div");
                message.className = "no-time-slots";
                message.textContent = "No available time slots for this date";
                timeGrid.appendChild(message);
                return;
            }

            const today = new Date();
            const selectedDate = new Date(date);
            const isToday = selectedDate.toDateString() === today.toDateString();
            const currentTime = today.getHours().toString().padStart(2, '0') + ':' +
                today.getMinutes().toString().padStart(2, '0') ;

            const timeSlots = generateTimeSlots(slots[0], date);

            timeSlots.forEach(function(slot) {
                const timeItem = document.createElement("div");
                timeItem.className = "time-item";
                timeItem.textContent = slot.timeData;
                timeItem.setAttribute("data-time", slot.timeData);
                let shouldDisable = slot.disabled;
                console.log('shouldDisable', shouldDisable);
                // Disable if time is in the past (today)
                if (isToday && !shouldDisable) {
                    console.log('slot.timeData', slot.timeData);
                    console.log('currentTime', currentTime);
                    if (slot.timeData <= currentTime) {
                        shouldDisable = true;
                    }
                    console.log('shouldDisable in first if', shouldDisable);
                }
                // Disable if time is already booked
                if (normalizedBookedSlots.includes(slot.timeData)) {
                    console.log('boooked slots', normalizedBookedSlots);
                    shouldDisable = true;
                }
                if (shouldDisable) {
                    timeItem.classList.add("disabled");
                }
                // Note: Click handler is now handled by jQuery event delegation in document ready
                timeGrid.appendChild(timeItem);
            });
        }
        // Helper function to convert time string to minutes
        function parseTimeToMinutes(timeString) {
            const timeParts = timeString.match(/(\d+):(\d+)\s*(AM|PM)/i);
            if (!timeParts) return 0;

            let hours = parseInt(timeParts[1]);
            const minutes = parseInt(timeParts[2]);
            const period = timeParts[3].toUpperCase();

            // Convert to 24-hour format
            if (period === 'PM' && hours !== 12) {
                hours += 12;
            } else if (period === 'AM' && hours === 12) {
                hours = 0;
            }

            return hours * 60 + minutes;
        }

        function ajaxCall(service_id = null, date = null, callback = null) {
            // Show loading in business data section
            $('.business-data').html(
                '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading services...</span></div></div>'
                );

            $.ajax({
                url: '/get-services-by-id/' + {{ auth()->id() }} + '/' + (service_id ?? ''),
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.html) {
                        $('.business-data').html(response.html);
                    }

                    // Execute callback if provided (to restore client data)
                    if (callback && typeof callback === 'function') {
                        callback();
                    }
                    if (service_id == null && response.service_id) {
                        service_id = response.service_id;
                    }
                    let formattedDate = date ?
                        new Date(date).toISOString().split('T')[0] :
                        new Date().toISOString().split('T')[0];

                    const url = "{{ url('get-service-details') }}/" + service_id + "?date=" + formattedDate;
                    $.get(url, function(response) {
                        if (response.status) {
                            $('#service-details').modal('show');
                            $("#service-detail-modal-body").html(response.data);

                            let calendarDate = formattedDate;
                            if (window.cartData && window.cartData.isEdit && window.cartData
                                .booking_date) {
                                calendarDate = window.cartData.booking_date;
                            }

                            initializeCalendar(response.services, calendarDate, response.slots);

                            // Update time slots after calendar is initialized
                            updateTimeSlotsForDate(service_id, calendarDate);

                            if (window.cartData && window.cartData.isEdit) {
                                populateCartData(window.cartData);
                            }

                            if (window.shouldDisableLocationInputs) {
                                let loc = $('input[name="loc"]:checked').val();
                                if (loc === "provider") {
                                    $('#pac-input').prop('disabled', true);
                                }
                                window.shouldDisableLocationInputs = false;
                            }

                            setTimeout(function() {
                                initServiceMap();
                            }, 100);
                        } else {
                            Swal.fire({
                                text: response.message,
                                icon: "error",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary"
                                }
                            });
                        }
                    });
                },
                error: function(xhr) {
                    console.error('Error fetching services:', xhr.responseText);
                }
            });
        }

        $(document).on("click", "#add-to-cart-service", function() {
            let booking_date = $('input[name="booking_date"]').val();
            let booking_time = $('input[name="booking_time"]').val();
            let service_id = $('#professionalService option:selected').val();
            // let service_id = 'fd5e2a6b-8449-4ed0-86b4-319eb449071e';
            let comments = $('textarea[name="comments"]').val();
            let loc = $('input[name="loc"]:checked').val();
            let lat = $('input[name="lat"]').val();
            let lng = $('input[name="lng"]').val();
            let address = $('#pac-input').val();

            // Get client information
            let clientName = $('input[name="clientName"]').val();
            let clientEmail = $('input[name="clientEmail"]').val();
            let phone = $('input[name="phone"]').val();

            // Get selected professionals
            let selectedProfessionals = [];
            $('input[name="professionals[]"]:checked').each(function() {
                selectedProfessionals.push({
                    id: $(this).val(),
                    name: $(this).closest('.professional-option').data('professional-name')
                });
            });

            // Validation: Check if client information is provided for professional users
            if ($('input[name="clientName"]').length > 0) {
                if (!clientName || !phone) {
                    Swal.fire({
                        text: "Please fill in all client information fields",
                        icon: "error",
                        buttonsStyling: false,
                        confirmButtonText: "Ok, got it!",
                        customClass: {
                            confirmButton: "btn btn-primary"
                        }
                    });
                    return;
                }
                if ($('input[name="clientEmail"]').length > 0 && clientEmail) {
                    let emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(clientEmail)) {
                        Swal.fire({
                            text: "Please enter a valid email address",
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-primary"
                            }
                        });
                        return;
                    }
                }
            }

            // Validation: Check if professionals are selected for business services
            let isBusinessService = $('#professional-selection').length > 0;
            if (isBusinessService && selectedProfessionals.length === 0) {
                Swal.fire({
                    text: "Please select at least one professional",
                    icon: "error",
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                }).then(() => {
                    // Small delay to ensure SweetAlert is fully closed
                    setTimeout(() => {
                        // Scroll modal content to top - target the specific modal
                        $('#service-details .modal-body').animate({
                            scrollTop: 0
                        }, 300);
                    }, 100);
                });
                return;
            }

            // Validation: Check if address is provided (mandatory)
            if (!address || address.trim() === '') {
                Swal.fire({
                    text: "Please enter a valid address",
                    icon: "error",
                    buttonsStyling: false,
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                });
                return;
            }

            if (booking_date && booking_time) {
                // Show loading overlay on modal
                $('#add-booking-modal .modal-content').append(
                    '<div id="booking-save-loader" class="modal-loader-overlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Saving booking...</span></div></div>'
                    );

                $.ajax({
                    url: '{{ route('saveProfessionalBookingDetails') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        booking_date: booking_date,
                        booking_time: booking_time,
                        service_id: service_id,
                        comments: comments,
                        loc: loc,
                        lat: lat,
                        lng: lng,
                        address: address,
                        clientName: clientName,
                        clientEmail: clientEmail,
                        phone: phone,
                        selected_professionals: selectedProfessionals
                    },
                    beforeSend: function() {
                        // Disable the save button to prevent double submission
                        $('.save-booking-btn').prop('disabled', true).text('Saving...');
                    },
                    success: function(response) {
                        if (response.status === 'success') {
                            $('#service-details').modal('hide');
                            Swal.fire({
                                text: "Booking created successfully",
                                icon: "success",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary"
                                }
                            }).then((result) => {
                                if (result.isConfirmed) {
                                    window.location.href = '/booking';
                                }
                            });
                        } else {
                            Swal.fire({
                                text: response.message || 'Error saving booking details',
                                icon: "error",
                                buttonsStyling: false,
                                confirmButtonText: "Ok, got it!",
                                customClass: {
                                    confirmButton: "btn btn-primary"
                                }
                            });
                        }
                    },
                    error: function(xhr) {
                        let errorMessage = 'An error occurred while saving booking details';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }
                        Swal.fire({
                            text: errorMessage,
                            icon: "error",
                            buttonsStyling: false,
                            confirmButtonText: "Ok, got it!",
                            customClass: {
                                confirmButton: "btn btn-primary"
                            }
                        });
                    },
                    complete: function() {
                        // Remove loading overlay and re-enable button
                        $('#booking-save-loader').remove();
                        $('.save-booking-btn').prop('disabled', false).text('Save Booking');
                    }
                });
            } else {
                Swal.fire({
                    text: "Please select both date and time",
                    icon: "error",
                    confirmButtonText: "Ok, got it!",
                    customClass: {
                        confirmButton: "btn btn-primary"
                    }
                }).then(() => {
                    // Small delay to ensure SweetAlert is fully closed
                    setTimeout(() => {
                        // Scroll modal content to top - target the specific modal
                        $('#addBookingModal .modal-body').animate({
                            scrollTop: 0
                        }, 300);
                    }, 100);
                });
            }
        });
        $(document).on("click", ".add-to-cart-btn", function() {
            // let service_id = $(this).data('id');
            // let service_id = 'fd5e2a6b-8449-4ed0-86b4-319eb449071e';
            let service_id = null;
            let isEdit = $(this).data('is-edit') || false;

            // Store cart data for pre-population if editing
            if (isEdit) {
                let selectedProfessionalsData = $(this).data('selected-professionals');
                let selectedProfessionals = [];

                // Parse selected professionals data
                if (selectedProfessionalsData) {
                    try {
                        if (typeof selectedProfessionalsData === 'string') {
                            selectedProfessionals = JSON.parse(selectedProfessionalsData);
                        } else {
                            selectedProfessionals = selectedProfessionalsData;
                        }
                    } catch (e) {
                        console.warn('Error parsing selected professionals data:', e);
                        selectedProfessionals = [];
                    }
                }

                window.cartData = {
                    booking_date: $(this).data('booking-date'),
                    booking_time: $(this).data('booking-time'),
                    location_type: $(this).data('location-type'),
                    comments: $(this).data('comments'),
                    address: $(this).data('address'),
                    lat: $(this).data('lat'),
                    lng: $(this).data('lng'),
                    selected_professionals: selectedProfessionals,
                    isEdit: true
                };

                console.log('Cart data for editing:', window.cartData);
            } else {
                window.cartData = {
                    isEdit: false
                };
            }

            // Store the disabled state to apply after modal loads
            window.shouldDisableLocationInputs = true;
            ajaxCall(service_id);

        });
        $(document).on("click", "#dateGrid .date-item", function() {
            $(".date-item").removeClass("selected");
            $(this).addClass("selected");
            let service_id = $('#professionalService option:selected').val();
            // let service_id = 1;
            var initialSelectedDate = $('#dateGrid .date-item.selected').data('date');
            let formattedDate = new Date(initialSelectedDate);
            let selectedTime = $('#timeGrid .time-item.selected').data('time');
            updateBookingDateInput(formattedDate, selectedTime);
            var newformattedDate = null;
            if (initialSelectedDate) {
                // Parse the date string properly to avoid timezone issues
                var parts = initialSelectedDate.split(' ');
                var monthMap = {
                    Jan: '01',
                    Feb: '02',
                    Mar: '03',
                    Apr: '04',
                    May: '05',
                    Jun: '06',
                    Jul: '07',
                    Aug: '08',
                    Sep: '09',
                    Oct: '10',
                    Nov: '11',
                    Dec: '12'
                };
                var day = parts[2].padStart(2, '0');
                var month = monthMap[parts[1]];
                var year = parts[3];
                newformattedDate = `${year}-${month}-${day}`;
            }

            // Update display to show selected date
            const display = document.getElementById("selectionDisplay");
            if (display && formattedDate) {
                const options = {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                };
                const dateStr = formattedDate.toLocaleDateString('en-US', options);
                display.innerHTML = '<strong>Selected:</strong> ' + dateStr + ' - Please select a time';
            }

            // Just update time slots for the selected date, don't reload entire modal
            updateTimeSlotsForDate(service_id, newformattedDate);
        });
        $(document).on("click", "#timeGrid .time-item:not(.disabled)", function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Time slot clicked:', $(this).data('time')); // Debug log
            console.log('Time slot element:', this); // Debug log
            console.log('Has disabled class:', $(this).hasClass('disabled')); // Debug log

            // Remove selection from all time items
            $("#timeGrid .time-item").removeClass("selected");

            // Add selection to clicked item
            $(this).addClass("selected");

            let selectedTime = $(this).data('time'); // Get the selected time from the data-time attribute
            let initialDateStr = $('#dateGrid .date-item.selected').data('date'); // Get string
            let initialDate = new Date(initialDateStr); // Convert to Date object

            console.log('Selected time:', selectedTime, 'Selected date:', initialDateStr); // Debug log

            if (selectedTime && initialDateStr) {
                if (!isNaN(initialDate)) {
                    updateBookingDateInput(initialDate, selectedTime); // Call your custom function

                    // Update display to show selected date and time
                    const display = document.getElementById("selectionDisplay");
                    if (display) {
                        const options = {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        };
                        const dateStr = initialDate.toLocaleDateString('en-US', options);
                        display.innerHTML = '<strong>Selected:</strong> ' + dateStr + ' at ' + selectedTime;
                    }

                    console.log('Booking inputs updated successfully'); // Debug log
                } else {
                    console.error('Invalid date selected');
                }
            } else {
                console.error('Missing time or date data');
            }
        });

        function generateTimeSlots(slot, date) {
            const timeSlots = [];
            const pad = num => num.toString().padStart(2, '0');
            // Parse slot start and end times
            let [startHour, startMin] = slot.start_time.split(':').map(Number);
            let [endHour, endMin] = slot.end_time.split(':').map(Number);
            const duration = parseInt(slot.duration); // Duration in minutes
            const now = new Date(); // Current datetime
            const selectedDate = new Date(date); // selected date passed as argument
            const start = new Date(selectedDate);
            start.setHours(startHour, startMin, 0, 0);
            const end = new Date(selectedDate);
            end.setHours(endHour, endMin, 0, 0);
            while (start < end) {
                const currentSlot = new Date(start); // clone current time
                // 24-hour format timeData
                const timeData = `${pad(currentSlot.getHours())}:${pad(currentSlot.getMinutes())}`;
                // Convert to 12-hour format with AM/PM for timeLabel
                let hours = currentSlot.getHours();
                let minutes = currentSlot.getMinutes();
                let ampm = hours >= 12 ? 'PM' : 'AM';
                hours = hours % 12; // Convert to 12-hour format
                hours = hours ? hours : 12; // If hour is 0 (midnight), set it to 12
                minutes = pad(minutes);

                const timeLabel = `${hours}:${minutes} ${ampm}`; // 12-hour format with AM/PM

                let isDisabled = false;

                // 1. Disable past slots only if the selected date is today
                const isToday = now.toDateString() === selectedDate.toDateString();
                if (isToday && currentSlot < now) {
                    isDisabled = true;
                }

                timeSlots.push({
                    timeLabel: timeLabel, // This generates the time slot in 12-hour format with AM/PM
                    timeData: timeData , // This generates the time slot in 24-hour format (HH:mm)
                    disabled: isDisabled
                });
                // Move to next slot
                start.setMinutes(start.getMinutes() + duration);
            }
            return timeSlots;
        }

        function initializeCalendar(services = null, mydate = null, slots = null) {
            let currentWeekStart = mydate ? new Date(mydate) : new Date();
            let selectedDate = mydate ? new Date(mydate) : null; // Don't default to today, let user select
            let selectedTime = null;
            let formatedDate = selectedDate.toISOString().split('T')[0];
            const timeSlots = slots[0] ? generateTimeSlots(slots[0], formatedDate) : [];
            const monthNames = [
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            ];
            const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
            const today = new Date();

            function setToStartOfWeek(date) {
                const dayOfWeek = date.getDay();
                date.setDate(date.getDate() - dayOfWeek);
                return date;
            }

            function generateMonthOptions() {
                const monthSelect = document.getElementById("monthSelect");
                if (!monthSelect) return;
                monthSelect.innerHTML = "";
                for (let i = 0; i < 12; i++) {
                    const date = new Date();
                    date.setMonth(date.getMonth() + i);

                    const option = document.createElement("option");
                    option.value = date.getFullYear() + "-" + date.getMonth();
                    option.textContent = monthNames[date.getMonth()] + " " + date.getFullYear();

                    if (i === 0) {
                        option.selected = true;
                    }

                    monthSelect.appendChild(option);
                }
            }

            function renderDates(disabledDates = []) {
                // Get the container where date items will be rendered
                const dateGrid = document.getElementById("dateGrid");
                if (!dateGrid) return;
                // Clear any previously rendered dates
                dateGrid.innerHTML = "";
                // Loop to render 7 days (a full week)
                for (let i = 0; i < 7; i++) {
                    // Create a date starting from currentWeekStart and add 'i' days
                    const date = new Date(currentWeekStart);
                    date.setDate(currentWeekStart.getDate() + i);
                    // Format date as "YYYY-MM-DD" for comparison
                    const formattedDate = date.toISOString().split("T")[0];
                    // Create a new date item element
                    const dateItem = document.createElement("div");
                    dateItem.className = "date-item"; // Add class for styling and selector
                    dateItem.setAttribute("data-date", date.toDateString()); // Store readable date for later use
                    // --- Check if the date is in the disabledDates list ---
                    const isInDisabledList = disabledDates.includes(formattedDate);
                    // --- Check if the date is in the past ---
                    const now = new Date();
                    now.setHours(0, 0, 0, 0); // Remove time portion to compare only the date
                    const isPastDate = date < now;
                    // --- Highlight the selected date if it matches ---
                    if (selectedDate && isSameDay(date, selectedDate)) {
                        dateItem.classList.add("selected");
                    }
                    // --- If date is disabled or in the past, mark as disabled ---
                    if (isInDisabledList || isPastDate) {
                        dateItem.classList.add("disabled");
                    } else {
                        // If date is valid, make it clickable and bind the click handler
                        dateItem.addEventListener("click", function() {

                            selectDate(this, date); // Pass the clicked element and date to the selectDate function
                        });
                    }
                    // --- Create inner HTML showing date number and day name ---
                    dateItem.innerHTML =
                        '<div class="date-number">' + date.getDate() + '</div>' + // e.g. 17
                        '<div class="date-day">' + dayNames[date.getDay()] + '</div>'; // e.g. Wed

                    // Append the date item to the grid
                    dateGrid.appendChild(dateItem);
                }
                // Update the dropdown to reflect the current visible week’s month
                updateMonthSelect();
            }

            function renderTimeSlots() {
                const timeGrid = document.getElementById("timeGrid");
                if (!timeGrid) return;

                timeGrid.innerHTML = ""; // Clear previous items

                if (timeSlots.length === 0) {
                    // If no slots available
                    const message = document.createElement("div");
                    message.className = "no-time-slots";
                    message.textContent = "No available time slots";
                    timeGrid.appendChild(message);
                } else {
                    // If slots are available
                    timeSlots.forEach(function(slot) {
                        const timeItem = document.createElement("div");
                        timeItem.className = "time-item";
                        timeItem.textContent = slot.timeData;
                        timeItem.setAttribute("data-time", slot.timeData);

                        // Add "disabled" class and prevent selection if disabled
                        if (slot.disabled) {
                            timeItem.classList.add("disabled");
                        }

                        // Check if this time should be pre-selected
                        if (selectedTime === slot.timeData) {
                            timeItem.classList.add("selected");
                        }

                        // Note: Click handler is now handled by jQuery event delegation

                        timeGrid.appendChild(timeItem);
                    });
                }
            }

            function selectDate(element, date) {
                const allDates = document.querySelectorAll(".date-item");
                allDates.forEach(d => d.classList.remove("selected"));

                element.classList.add("selected");
                selectedDate = new Date(date);
                updateDisplay();
                console.log('Selected date:', $(element).data('date'));
            }

            function selectTime(element, time) {
                const allTimes = document.querySelectorAll(".time-item");
                allTimes.forEach(t => t.classList.remove("selected"));

                element.classList.add("selected");
                selectedTime = time;
                updateDisplay();
            }

            function updateDisplay() {
                const display = document.getElementById("selectionDisplay");
                if (!display) return;
                if (selectedDate && selectedTime) {
                    const options = {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    };
                    const dateStr = selectedDate.toLocaleDateString('en-US', options);
                    display.innerHTML = '<strong>Selected:</strong> ' + dateStr + ' at ' + selectedTime;
                } else if (selectedDate) {
                    const options = {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    };
                    const dateStr = selectedDate.toLocaleDateString('en-US', options);
                    display.innerHTML = '<strong>Selected:</strong> ' + dateStr + ' - Please select a time';
                } else {
                    display.innerHTML = '<div class="no-selection">Select a date and time</div>';
                }
            }

            function changeWeek(direction) {
                currentWeekStart.setDate(currentWeekStart.getDate() + (direction * 7));
                renderDates(["2025-07-13", "2025-07-14"]);
            }

            function updateMonthSelect() {
                const monthSelect = document.getElementById("monthSelect");
                if (!monthSelect) return;

                const middleDate = new Date(currentWeekStart);
                middleDate.setDate(middleDate.getDate() + 3);

                const targetValue = middleDate.getFullYear() + "-" + middleDate.getMonth();

                for (let i = 0; i < monthSelect.options.length; i++) {
                    if (monthSelect.options[i].value === targetValue) {
                        monthSelect.selectedIndex = i;
                        break;
                    }
                }
            }

            function setupEventListeners() {
                const prevBtn = document.getElementById("prevBtn");
                const nextBtn = document.getElementById("nextBtn");
                const monthSelect = document.getElementById("monthSelect");
                if (prevBtn) {
                    prevBtn.addEventListener("click", function() {
                        changeWeek(-1);
                    });
                }
                if (nextBtn) {
                    nextBtn.addEventListener("click", function() {
                        changeWeek(1);
                    });
                }
                if (monthSelect) {
                    monthSelect.addEventListener("change", function() {
                        const value = this.value.split("-");
                        const year = parseInt(value[0]);
                        const month = parseInt(value[1]);

                        const newDate = new Date(year, month, 1);
                        currentWeekStart = setToStartOfWeek(newDate);
                        renderDates(["2025-07-13", "2025-07-14"]);
                    });
                }
            }

            function isSameDay(date1, date2) {
                return date1.getDate() === date2.getDate() &&
                    date1.getMonth() === date2.getMonth() &&
                    date1.getFullYear() === date2.getFullYear();
            }

            // Initialize calendar
            currentWeekStart = setToStartOfWeek(currentWeekStart);
            generateMonthOptions();
            renderDates(["2025-07-13", "2025-07-14"]);
            renderTimeSlots();
            setupEventListeners();
            updateDisplay();
            updateBookingDateInput(formatedDate, selectedTime);
        }
        $(document).on('change', 'input[name="loc"]', function() {
            var selectedValue = $('input[name="loc"]:checked').val();
            if (selectedValue === "provider") {
                $('#providers-loc').show();
                $('#home-loc').hide();
                $('#pac-input').prop('disabled', true);
                // Update coordinates to service location
                updateMapToServiceLocation();
            } else if (selectedValue === "home") {
                $('#providers-loc').hide();
                $('#home-loc').show();
                $('#pac-input').prop('disabled', false);
                // Set coordinates to user's profile location for home
                const userLat = '{{ auth()->user()->profile?->lat ?? '' }}';
                const userLng = '{{ auth()->user()->profile?->lng ?? '' }}';

                if (userLat && userLng) {
                    $('#latitude').val(userLat);
                    $('#longitude').val(userLng);

                    // Get address from user's coordinates
                    getAddressFromCoordinates(userLat, userLng, function(address) {
                        if (address) {
                            $('#pac-input').val(address);
                        }
                    });
                } else {
                    // If no user profile coordinates, clear fields
                    $('#latitude').val('');
                    $('#longitude').val('');
                    $('#pac-input').val('');
                }

                // Reinitialize map for home location
                setTimeout(function() {
                    initServiceMap();
                }, 100);
            } else {
                $('#pac-input').prop('disabled', false);
            }
        });
        // Function to update cart display after deletion
        function updateCartDisplay() {
            const cartCount = $('.cart-item').length;
            $('.cart-count').text(cartCount);

            // Update any cart badges or counters
            if (cartCount === 0) {
                $('.cart-badge').hide();
            }

            // Recalculate and update total if total element exists
            updateCartTotal();
        }
        // Function to recalculate cart total
        function updateCartTotal() {
            let total = 0;
            $('.cart-item').each(function() {
                const priceText = $(this).find('.item-price').text().replace('$', '');
                const price = parseFloat(priceText) || 0;
                total += price;
            });

            // Update total display
            $('.cart-total').text('$' + total.toFixed(2));
        }
        // Function to populate cart data when editing
        function populateCartData(cartData) {
            if (cartData.booking_date) {
                $('input[name="booking_date"]').val(cartData.booking_date);

                // No need to select date again since calendar is already initialized with correct date
                // The date should already be selected from initializeCalendar
            }
            if (cartData.booking_time) {
                $('input[name="booking_time"]').val(cartData.booking_time);
                setTimeout(function() {
                    selectTimeSlot(cartData.booking_time);
                }, 500);
            }
            if (cartData.location_type) {
                $('input[name="loc"][value="' + cartData.location_type + '"]').prop('checked', true);
                $('input[name="loc"][value="' + cartData.location_type + '"]').trigger('change');
            }
            if (cartData.comments) {
                $('textarea[name="comments"]').val(cartData.comments);
            }
            if (cartData.address) {
                $('#pac-input').val(cartData.address);
            }
            if (cartData.lat) {
                $('#latitude').val(cartData.lat);
            }
            if (cartData.lng) {
                $('#longitude').val(cartData.lng);
            }

            // Pre-select professionals if editing
            if (cartData.selected_professionals && cartData.selected_professionals.length > 0) {
                setTimeout(function() {
                    selectProfessionals(cartData.selected_professionals);
                }, 800); // Wait for modal content to fully load
            }
        }

        // Function to select professionals visually
        function selectProfessionals(selectedProfessionals) {
            console.log('Selecting professionals:', selectedProfessionals);

            // Clear all previous selections
            $('input[name="professionals[]"]').prop('checked', false);
            $('.professional-option').removeClass('selected');

            // Select the professionals from cart data
            selectedProfessionals.forEach(function(professional) {
                let professionalId = professional.id;
                let checkbox = $('input[name="professionals[]"][value="' + professionalId + '"]');

                if (checkbox.length > 0) {
                    checkbox.prop('checked', true);
                    checkbox.closest('.professional-option').addClass('selected');
                    console.log('Selected professional:', professional.name, 'ID:', professionalId);
                } else {
                    console.warn('Professional checkbox not found for ID:', professionalId);
                }
            });

            // Update the selected professionals display
            updateSelectedProfessionals();
        }
        // Function to select time slot visually
        function selectTimeSlot(timeValue) {
            // Remove selection from all time items
            const allTimes = document.querySelectorAll(".time-item");
            allTimes.forEach(t => t.classList.remove("selected"));

            // Find and select the matching time slot
            const timeItem = document.querySelector('.time-item[data-time="' + timeValue + '"]');
            if (timeItem) {
                timeItem.classList.add("selected");
                console.log('Time slot selected:', timeValue);

                // Update display to show selected date and time
                const selectedDateElement = document.querySelector('#dateGrid .date-item.selected');
                if (selectedDateElement) {
                    const dateStr = selectedDateElement.getAttribute('data-date');
                    const selectedDate = new Date(dateStr);

                    const display = document.getElementById("selectionDisplay");
                    if (display && !isNaN(selectedDate)) {
                        const options = {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                        };
                        const formattedDateStr = selectedDate.toLocaleDateString('en-US', options);
                        display.innerHTML = '<strong>Selected:</strong> ' + formattedDateStr + ' at ' + timeValue;
                    }
                }
            } else {
                console.warn('Time slot not found:', timeValue);
            }
        }
        // Function to select date item visually
        function selectDateItem(dateValue) {
            // Remove selection from all date items
            const allDates = document.querySelectorAll(".date-item");
            allDates.forEach(d => d.classList.remove("selected"));

            // Find and select the matching date item
            // The dateValue might be in format "2024-01-15", but data-date might be in different format
            // Try to find by data-date attribute
            const dateItem = document.querySelector('.date-item[data-date*="' + dateValue + '"]');
            if (dateItem) {
                dateItem.classList.add("selected");
                console.log('Date item selected:', dateValue);
            } else {
                // Try alternative approach - convert date format and search
                try {
                    const date = new Date(dateValue);
                    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    const formattedDate = months[date.getMonth()] + ' ' + date.getDate() + ' ' + date.getFullYear();

                    const alternativeDateItem = document.querySelector('.date-item[data-date="' + formattedDate + '"]');
                    if (alternativeDateItem) {
                        alternativeDateItem.classList.add("selected");
                        console.log('Date item selected (alternative format):', formattedDate);
                    } else {
                        console.warn('Date item not found:', dateValue);
                    }
                } catch (e) {
                    console.warn('Date item not found and format conversion failed:', dateValue);
                }
            }
        }

        function updateMapToServiceLocation() {
            const $latElement = $('#latitude');
            const $lngElement = $('#longitude');
            // Get service coordinates from data attributes
            const serviceLat = parseFloat($latElement.data('service-lat'));
            const serviceLng = parseFloat($lngElement.data('service-lng'));
            if (serviceLat && serviceLng && serviceLat !== 0 && serviceLng !== 0) {
                $latElement.val(serviceLat);
                $lngElement.val(serviceLng);

                setTimeout(function() {
                    initServiceMap();
                }, 100);
            } else {
                console.warn('Service coordinates not available');
                setTimeout(function() {
                    initServiceMap();
                }, 100);
            }
        }

        function initServiceMap() {
            const $mapElement = $('#map');
            const $inputElement = $('#pac-input');
            const $latElement = $('#latitude');
            const $lngElement = $('#longitude');
            if (!$mapElement.length || !$inputElement.length || !$latElement.length || !$lngElement.length) {
                console.warn('Service Map: Required elements not found');
                return;
            }
            if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                console.warn('Google Maps API not loaded yet, retrying...');
                setTimeout(initServiceMap, 500);
                return;
            }

            let lat, lng;
            const locType = $('input[name="loc"]:checked').val();

            if (locType === "provider") {
                const serviceLat = parseFloat($latElement.val());
                const serviceLng = parseFloat($lngElement.val());

                if (serviceLat && serviceLng && serviceLat !== 0 && serviceLng !== 0) {
                    lat = serviceLat;
                    lng = serviceLng;
                } else {
                    lat = -33.8688;
                    lng = 151.2195;
                }
            } else {
                // For home location, use user's profile coordinates if available
                const userLat = parseFloat($latElement.val());
                const userLng = parseFloat($lngElement.val());

                if (userLat && userLng && userLat !== 0 && userLng !== 0) {
                    lat = userLat;
                    lng = userLng;
                } else {
                    // Fallback to default coordinates if no user profile coordinates
                    lat = -33.8688;
                    lng = 151.2195;
                }
            }
            const defaultLatLng = {
                lat: lat,
                lng: lng
            };
            const input = document.getElementById("pac-input");
            const map = new google.maps.Map($mapElement[0], {
                center: defaultLatLng,
                zoom: 14,
                mapTypeControl: false,
                streetViewControl: false,
                rotateControl: true,
            });

            const searchBox = new google.maps.places.SearchBox(input);
            const geocoder = new google.maps.Geocoder();

            const marker = new google.maps.Marker({
                map,
                position: defaultLatLng,
                draggable: true,
            });

            function updateLocationFields(lat, lng) {
                $latElement.val(lat);
                $lngElement.val(lng);
            }

            function updateAddressFromLatLng(lat, lng) {
                geocoder.geocode({
                    location: {
                        lat,
                        lng
                    }
                }, (results, status) => {
                    if (status === "OK" && results[0]) {
                        $inputElement.val(results[0].formatted_address);
                    } else {
                        $inputElement.val("");
                        console.warn("Geocoder failed due to: " + status);
                    }
                });
            }
            // Update location fields and address based on location type
            if (locType === "provider") {
                updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
            } else {
                // For home location, update both fields and address with user's profile coordinates
                updateLocationFields(defaultLatLng.lat, defaultLatLng.lng);
                updateAddressFromLatLng(defaultLatLng.lat, defaultLatLng.lng);
            }
            google.maps.event.addListener(marker, "dragend", function(event) {
                const lat = event.latLng.lat();
                const lng = event.latLng.lng();
                updateLocationFields(lat, lng);
                updateAddressFromLatLng(lat, lng);
            });

            searchBox.addListener("places_changed", () => {
                const places = searchBox.getPlaces();
                if (!places.length || !places[0].geometry?.location) return;

                const location = places[0].geometry.location;
                marker.setPosition(location);
                map.setCenter(location);
                map.setZoom(18);

                const lat = location.lat();
                const lng = location.lng();
                updateLocationFields(lat, lng);
                $inputElement.val(places[0].formatted_address || "");
            });
            console.log('Service Map: Initialization completed successfully');
        }
    </script>
@endpush
