@extends('layouts.app')

@section('content')
    {{-- <div class="container"> --}}
        {{-- <div class="row justify-content-center"> --}}
            {{-- <div class="col-md-8"> --}}
                {{-- <div class="card"> --}}
                    {{-- <div class="card-header">{{ __('Reset Password') }}</div> --}}

                    {{-- <div class="card-body"> --}}
                        {{-- @if (session('status')) --}}
                        {{-- <div class="alert alert-success" role="alert"> --}}
                            {{-- {{ session('status') }} --}}
                            {{-- </div> --}}
                        {{-- @endif --}}

                        {{-- <form method="POST" action="{{ route('password.email') }}"> --}}
                            {{-- @csrf --}}

                            {{-- <div class="row mb-3"> --}}
                                {{-- <label for="email" class="col-md-4 col-form-label text-md-end">{{ __('Email Address')
                                    }}</label> --}}

                                {{-- <div class="col-md-6"> --}}
                                    {{-- <input id="email" type="email"
                                        class="form-control @error('email') is-invalid @enderror" name="email"
                                        value="{{ old('email') }}" required autocomplete="email" autofocus> --}}

                                    {{-- @error('email') --}}
                                    {{-- <span class="invalid-feedback" role="alert"> --}}
                                        {{-- <strong>{{ $message }}</strong> --}}
                                        {{-- </span> --}}
                                    {{-- @enderror --}}
                                    {{-- </div> --}}
                                {{-- </div> --}}

                            {{-- <div class="row mb-0"> --}}
                                {{-- <div class="col-md-6 offset-md-4"> --}}
                                    {{-- <button type="submit" class="btn btn-primary"> --}}
                                        {{-- {{ __('Send Password Reset Link') }} --}}
                                        {{-- </button> --}}
                                    {{-- </div> --}}
                                {{-- </div> --}}
                            {{-- </form> --}}
                        {{-- </div> --}}
                    {{-- </div> --}}
                {{-- </div> --}}
            {{-- </div> --}}
        {{-- </div> --}}


    <div class="d-flex flex-column flex-column-fluid flex-lg-row">
        <!--begin::Aside-->
        <div class="d-flex flex-center w-50 pt-15 pt-lg-0 px-10">
            <!--begin::Aside-->
            <div class="d-flex flex-center flex-lg-start flex-column w-100 align-items-center">

                <!--begin::Wrapper-->
                <div class=" d-flex flex-center flex-column w-75 px-lg-10 pb-15  ">
                    <form class="form w-100" novalidate="novalidate" method="POST" action="{{ route('password.email') }}">
                        @csrf
                        <!--begin::Heading-->
                        <div class="text-center mb-11">
                            <!--begin::Title-->
                            <h1 class="text-dark fw-bolder mb-3">Reset Password</h1>
                            <!--end::Title-->

                        </div>
                        <!--begin::Heading-->


                        <!--begin::Input group=-->
                        <div class="fv-row mb-8">
                            <!--begin::Email-->
                            <input id="email" type="email" placeholder="Email"
                                class="form-control bg-transparent @error('email') is-invalid @enderror" name="email"
                                value="{{ old('email') }}" required autocomplete="email">

                            @error('email')
                                <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                            @enderror
                            <!--end::Email-->
                        </div>
                        <!--end::Input group=-->
                        <!--begin::Submit button-->
                        <div class="d-grid ">
                            <button type="submit" id="kt_password_reset_submit" class="blue-btn">
                                <!--begin::Indicator label-->
                                <span class="indicator-label">Send</span>
                                <!--end::Indicator label-->
                                <!--begin::Indicator progress-->
                                <span class="indicator-progress">Please wait...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                <!--end::Indicator progress-->
                            </button>
                        </div>
                        <!--end::Submit button-->
                    </form>
                    <!--end::Form-->

                    <div class="site_logo pt-15">
                        <a href="{{ url('/') }}" class="text-center">
                            <img src="{{ asset('website') . '/' . setting()->logo }}" alt="icon">
                            <h4 class="blue-text pt-2"> Stylenest </h4>
                        </a>
                    </div>
                </div>
                <!--end::Wrapper-->

            </div>
            <!--begin::Aside-->
        </div>


        <div class="w-50 login-side-image">
            <img src="{{ asset('website') }}/assets/images/login-banner.png" alt="icon">
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        $(document).ready(function () {
            // Check for session status message and show SweetAlert
            @if (session('status'))
                Swal.fire({
                    title: "Email Sent!",
                    text: "Password reset link has been sent to your email address. Please check your inbox and follow the instructions.",
                    icon: "success",
                    confirmButtonText: "OK"
                });
            @endif

            // Check for validation errors and show SweetAlert
            @if ($errors->has('email'))
                Swal.fire({
                    title: "Error",
                    text: "{{ $errors->first('email') }}",
                    icon: "error"
                });
            @endif
                // submitBtn.find('.indicator-progress').hide();
            });
    </script>


    <script>
        $('#email').on('input', function () {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        });

    </script>
@endsection