<?php

namespace App\Http\Controllers;

use App\Models\OutlookCalendar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\Booking;
use Carbon\Carbon;

class OutlookCalendarController extends Controller
{
    public function redirect()
    {
        $clientId    = config('services.outlook.client_id');
        $tenantId    = config('services.outlook.tenant_id');
        $redirectUri = route('outlook.callback');

        $queryParams = http_build_query([
            'client_id'     => $clientId,
            'response_type' => 'code',
            'redirect_uri'  => 'https://anders.democustomprojects.com/outlook/callback',
            'response_mode' => 'query',
            'scope'         => 'User.Read Calendars.ReadWrite offline_access',
            'prompt'        => 'login',
        ]);

        return redirect("https://login.microsoftonline.com/{$tenantId}/oauth2/v2.0/authorize?{$queryParams}");
    }

    public function callback(Request $request)
    {
        if (!$request->has('code')) {
            return redirect('/setting?tab=outlook-calendar')->with([
                'type'    => 'error',
                'title'   => 'Authorization Error',
                'message' => 'Authorization code was not provided.',
            ]);
        }

        try {
            $response = Http::asForm()->post('https://login.microsoftonline.com/common/oauth2/v2.0/token', [
                'client_id'     => config('services.outlook.client_id'),
                'client_secret' => config('services.outlook.client_secret'),
                'code'          => $request->code,
                'redirect_uri'  => route('outlook.callback'),
                'grant_type'    => 'authorization_code',
            ]);

            $body = $response->json();

            if (!isset($body['access_token'])) {
                return redirect('/setting?tab=outlook-calendar')->with([
                    'type'    => 'error',
                    'title'   => 'Token Error',
                    'message' => 'Failed to retrieve access token from Microsoft.',
                ]);
            }

            $accessToken  = $body['access_token'];
            $refreshToken = $body['refresh_token'] ?? null;
            $expiresAt    = now()->addSeconds($body['expires_in']);
            $tokenType    = $body['token_type'] ?? 'Bearer';

            // Optional: Get Microsoft user ID
            $userInfoResponse = Http::withToken($accessToken)->get('https://graph.microsoft.com/v1.0/me');
            $userInfo = $userInfoResponse->json();
            $providerId = $userInfo['id'] ?? null;

            OutlookCalendar::updateOrCreate(
                ['user_id' => auth()->id()],
                [
                    'access_token' => $accessToken,
                    'refresh_token' => $refreshToken,
                    'expires_at' => $expiresAt,
                    'token_type' => $tokenType,
                    'provider_id' => $providerId,
                    'updated_at' => now(),
                ]
            );

            return redirect('/setting?tab=outlook-calendar')->with([
                'type'    => 'success',
                'title'   => 'Done',
                'message' => 'Outlook Calendar connected!',
            ]);
        } catch (\Exception $e) {
            return redirect('/setting?tab=outlook-calendar')->with([
                'type'    => 'error',
                'title'   => 'Unexpected Error',
                'message' => 'Something went wrong while connecting Outlook. Please try again.',
            ]);
        }
    }

    public function createOutlookEventForBooking(Booking $booking): ?string
    {
        // Check if provider has Outlook calendar configured
        $calendar = OutlookCalendar::where('user_id', $booking->provider_id)->first();
        if (!$calendar || $calendar->expires_at->isPast()) return null;

        // $start = Carbon::parse($booking->booking_date . ' ' . $booking->booking_time);
        // $end = (clone $start)->addMinutes($booking->duration);

        // Load service details for event
        $booking->load('service');
        $serviceName = $booking->service->name ?? 'Service';
        $servicePrice = $booking->service_price ?? $booking->total_amount ?? 0;

        $response = Http::withToken($calendar->access_token)->post('https://graph.microsoft.com/v1.0/me/events', [
            'subject' => "Booking #{$booking->booking_number} - {$serviceName}",
            'start' => [
                'dateTime' => Carbon::parse($booking->booking_time)->format('H:i'),
                'timeZone' => 'UTC',
            ],
            // 'end' => [
            //     'dateTime' => $end->toIso8601String(),
            //     'timeZone' => 'UTC',
                
            // ],
            'location' => [
                'displayName' => $booking->provider->profile->location ?? 'No location provided',
            ],
            'body' => [
                'contentType' => 'HTML',
                'content' => "
                    <h3>Booking Details</h3>
                    <p><strong>Booking Number:</strong> {$booking->booking_number}</p>
                    <p><strong>Service:</strong> {$serviceName}</p>
                    <p><strong>Price:</strong> $" . number_format($servicePrice, 2) . "</p>
                    <p><strong>Duration:</strong> {$booking->duration} minutes</p>
                ",
            ],
        ]);

        if ($response->successful()) {
            return $response->json()['id']; // Return event ID for storage
        }

        return null;
    }

    public function updateOutlookEventForBooking(Booking $booking, string $eventId): bool
    {
        // Check if provider has Outlook calendar configured
        $calendar = OutlookCalendar::where('user_id', $booking->provider_id)->first();
        if (!$calendar || $calendar->expires_at->isPast()) return false;

        // $start = Carbon::parse($booking->booking_date . ' ' . $booking->booking_time);
        // $end = (clone $start)->addMinutes($booking->duration);

        // Load service details for event
        $booking->load('service');
        $serviceName = $booking->service->name ?? 'Service';
        $servicePrice = $booking->service_price ?? $booking->total_amount ?? 0;

        $response = Http::withToken($calendar->access_token)->patch("https://graph.microsoft.com/v1.0/me/events/{$eventId}", [
            'subject' => "Booking #{$booking->booking_number} - {$serviceName} (Updated)",
            'start' => [
                'dateTime' => Carbon::parse($booking->booking_time)->format('H:i'),
                'timeZone' => 'UTC',
            ],
            // 'end' => [
            //     'dateTime' => $end->toIso8601String(),
            //     'timeZone' => 'UTC',
            // ],
            'location' => [
                'displayName' => $booking->provider->profile->location ?? 'No location provided',
            ],
            'body' => [
                'contentType' => 'HTML',
                'content' => "
                    <h3>Updated Booking Details</h3>
                    <p><strong>Booking Number:</strong> {$booking->booking_number}</p>
                    <p><strong>Service:</strong> {$serviceName}</p>
                    <p><strong>Price:</strong> $" . number_format($servicePrice, 2) . "</p>
                    <p><strong>Duration:</strong> {$booking->duration} minutes</p>
                ",
            ],
        ]);
        return $response->successful();
    }

    public function deleteOutlookEventForBooking(int $providerId, string $eventId): bool
    {
        $calendar = OutlookCalendar::where('user_id', $providerId)->first();
        if (!$calendar || $calendar->expires_at->isPast()) return false;
        $response = Http::withToken($calendar->access_token)->delete("https://graph.microsoft.com/v1.0/me/events/{$eventId}");
        return $response->successful();
    }

    public function removeCalendar()
    {
        $user = auth()->user();
        $outlookCalendar = OutlookCalendar::where('user_id', $user->id)->first();
        if (!$outlookCalendar) {
            return response()->json(['message' => 'No calendar found for this user.'], 404);
        }
        // Revoke the Outlook access token
        $response = Http::asForm()->post("https://login.microsoftonline.com/{$outlookCalendar->provider_id}/oauth2/v2.0/logout", [
            'client_id' => config('services.outlook.client_id'),
            'client_secret' => config('services.outlook.client_secret'),
            'refresh_token' => $outlookCalendar->refresh_token,  // Using refresh token to revoke access
        ]);
        if ($response->failed()) {
            return response()->json(['message' => 'Failed to revoke the access token.'], 500);
        }
        $outlookCalendar->delete();
        return redirect()->back()->with(["type" => "success", "title" => "Created", "message" => 'Calendar Removed Successfully!!']);
    }
}
